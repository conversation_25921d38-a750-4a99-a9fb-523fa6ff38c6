!/gradle/wrapper/gradle-wrapper.jar
*.war
*~
*.class
*.lock
*.DS_Store
*.swp
*.out
out/
target/
build/
*.iml
*.ipr
*.iws
*.orig
*.log
.gradle/
.settings/
.classpath
.project
.metadata/
.idea/
.mvn/
logs/
dev.properties
dependency-reduced-pom.xml
*.rdb
log/
*.perm
LOG_FILE_IS_UNDEFINED*
*.py
*.pyc
web/src/main/webapp/public/
web/src/main/webapp/.sass-cache/
web/src/main/webapp/vendor/
.DS_Store
icons.scss
Linnerfile
_variables.scss
jiggly.json
web/src/main/webapp/vendor_modules/
web/src/main/webapp/components_vendor/
*.tar.gz
node_modules/
application.yml
generatorConfig.properties
generatorConfig.xml
logback-spring.xml
spring-devtools.properties
quartz.properties
bootstrap.yml
application-dev.yml
jeecg_config.properties
jeecg_database.properties