package org.jeecg.modules.mqtt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 重跑origin数据DTO
 *
 * <AUTHOR>
 * @date 2022-11-1
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmSensorRerunCIMCDataDTO {
    /**
     * 传感器编码
     */
    private String sensorCode;
    /**
     * 安装时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date installTime;
    /**
     * 安装时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    /**
     * 数据来源  1 安科瑞，2 前置机，3 模拟数据
     */
    private Integer dataSources;
}
