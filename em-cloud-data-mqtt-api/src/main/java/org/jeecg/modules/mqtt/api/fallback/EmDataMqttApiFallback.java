package org.jeecg.modules.mqtt.api.fallback;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.mqtt.api.IEmDataMqttApi;
import org.jeecg.modules.mqtt.dto.EmSensorRerunCIMCDataDTO;
import org.jeecg.modules.mqtt.dto.GatewayDeviceDTO;

/**
 * 进入fallback的方法 检查是否token未设置
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
@Slf4j
public class EmDataMqttApiFallback implements IEmDataMqttApi {
    @Setter
    private Throwable cause;

    @Override
    public Result<String> saveOrUpdate(GatewayDeviceDTO gatewayDeviceDTO) {
        log.error("新增网关出错 → {}", cause.getMessage());
        return Result.error("新增网关失败！");
    }

    @Override
    public Result<String> cancel(String id) {
        log.error("作废网关出错 → {}", cause.getMessage());
        return Result.error("作废网关出错！");
    }

    @Override
    public Result<String> rerunOriginData(EmSensorRerunCIMCDataDTO emSensorRerunCIMCDataDTO) {
        log.error("重跑origin数据出错 → {}", cause.getMessage());
        return Result.error("重跑origin数据出错！");
    }

    @Override
    public Result<String> getRealData(String gatewayCode) {
        log.error("中电抄表出错 → {}", cause.getMessage());
        return Result.error("中电抄表出错！");
    }
}
