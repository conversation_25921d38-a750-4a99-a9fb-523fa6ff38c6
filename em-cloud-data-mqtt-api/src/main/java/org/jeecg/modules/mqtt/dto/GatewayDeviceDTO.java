package org.jeecg.modules.mqtt.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 初始化网关DTO
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GatewayDeviceDTO implements Serializable {
    private static final long serialVersionUID = -3054616318134600845L;
    /**
     * 主键ID
     */
    private String id;
    /**
     * 网关编号
     */
    private String code;
    /**
     * 网关名称
     */
    private String name;
    /**
     * 需要解密：0否 1是
     */
    private Boolean needDecrypt;
}
