package org.jeecg.modules.mqtt.api;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.mqtt.api.factory.EmDataMqttApiFallbackFactory;
import org.jeecg.modules.mqtt.dto.EmSensorRerunCIMCDataDTO;
import org.jeecg.modules.mqtt.dto.GatewayDeviceDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

/**
 * 数据网关API
 *
 * <AUTHOR>
 * @date 2022-08-22 10:23
 */
@Component
@FeignClient(contextId = "emDataMqttApi",
        value = "em-data-mqtt",
        fallbackFactory = EmDataMqttApiFallbackFactory.class)
public interface IEmDataMqttApi {
    /**
     * 新增网关
     *
     * @param gatewayDeviceDTO
     * @return
     */
    @PostMapping("/dataMqtt/api/saveOrUpdate")
    Result<String> saveOrUpdate(@RequestBody GatewayDeviceDTO gatewayDeviceDTO);

    /**
     * 作废网关
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/dataMqtt/api/cancel")
    Result<String> cancel(@RequestParam("id") String id);

    /**
     * 重跑origin数据
     *
     * @param emSensorRerunCIMCDataDTO
     * @return
     */
    @PostMapping(value = "/dataMqtt/api/rerunOriginData")
    Result<String> rerunOriginData(@RequestBody EmSensorRerunCIMCDataDTO emSensorRerunCIMCDataDTO);

    /**
     * 抄表
     *
     * @param gatewayCode
     * @return
     */
    @PostMapping(value = "/dataMqtt/api/getRealData")
    Result<String> getRealData(@NotBlank(message = "网关编号不能为空！") @RequestParam(name = "gatewayCode") String gatewayCode);
}
