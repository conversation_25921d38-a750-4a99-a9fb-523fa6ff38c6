package org.jeecg.modules.mqtt.api.factory;

import org.jeecg.modules.mqtt.api.IEmDataMqttApi;
import org.jeecg.modules.mqtt.api.fallback.EmDataMqttApiFallback;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 回调工厂
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
@Component
public class EmDataMqttApiFallbackFactory implements FallbackFactory<IEmDataMqttApi> {
    @Override
    public IEmDataMqttApi create(Throwable throwable) {
        EmDataMqttApiFallback fallback = new EmDataMqttApiFallback();
        fallback.setCause(throwable);
        return fallback;
    }
}