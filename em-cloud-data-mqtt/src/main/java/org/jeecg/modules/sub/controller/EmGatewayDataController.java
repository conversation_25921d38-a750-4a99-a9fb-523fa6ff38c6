package org.jeecg.modules.sub.controller;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.SysLog;
import org.jeecg.common.aspect.annotation.SysReqLimit;
import org.jeecg.modules.common.service.EmGatewayDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 网关数据Controller
 *
 * <AUTHOR>
 * @date 2022-07-08 11:31
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/dataMqtt/gatewayData")
public class EmGatewayDataController {
    @Lazy
    @Autowired
    private EmGatewayDataService emGatewayDataService;

    /**
     * 重新发送数据
     *
     * @param deviceAddr
     * @param sdt
     * @param edt
     * @return
     */
    @GetMapping(value = "/reSendData")
    @SysLog(value = "能源监测-网关数据-重新发送")
    @SysReqLimit(key = "data:gatewayData:reSendData")
    @ApiOperation(value = "能源监测-网关数据-重新发送", notes = "能源监测-网关数据-重新发送")
    public Result<String> reSendData(@RequestParam(name = "deviceAddr") String deviceAddr,
                                     @RequestParam(name = "sdt") String sdt,
                                     @RequestParam(name = "edt") String edt) {
        emGatewayDataService.reSendData(deviceAddr, sdt, edt);
        return Result.ok("重新发送成功");
    }

    /**
     * 批量重新发送数据
     *
     * @param deviceAddrList
     * @param sdt
     * @param edt
     * @return
     */
    @GetMapping(value = "/batchReSendData")
    @SysLog(value = "能源监测-网关数据-批量重新发送")
    @SysReqLimit(key = "data:gatewayData:batchReSendData")
    @ApiOperation(value = "能源监测-网关数据-批量重新发送", notes = "能源监测-网关数据-批量重新发送")
    public Result<String> batchReSendData(@RequestParam(name = "deviceAddrList") List<String> deviceAddrList,
                                          @RequestParam(name = "sdt") String sdt,
                                          @RequestParam(name = "edt") String edt) {
        emGatewayDataService.reSendData(deviceAddrList, sdt, edt);
        return Result.ok("批量重新发送成功");
    }

    /**
     * 批量重新发送数据
     *
     * @param sdt
     * @param edt
     * @return
     */
    @GetMapping(value = "/reSendMqttData")
    @SysLog(value = "能源监测-网关数据-批量重新发送")
    @SysReqLimit(key = "data:gatewayData:reSendMqttData")
    @ApiOperation(value = "能源监测-网关数据-批量重新发送", notes = "能源监测-网关数据-批量重新发送")
    public Result<String> batchReSendData(@RequestParam(name = "sdt") String sdt,
                                          @RequestParam(name = "edt") String edt) {
        emGatewayDataService.reSendData(sdt, edt);
        return Result.ok("批量重新发送成功");
    }

    /**
     * 批量重新【红外】发送数据
     * 从gateway开始获取数据重新处理originData的device_addr
     *
     * @param deviceAddrList
     * @param sdt
     * @param edt
     * @return
     */
    @GetMapping(value = "/batchReSendInfraredData")
    @SysLog(value = "能源监测-网关数据-批量重新【红外】发送数据")
    @SysReqLimit(key = "data:gatewayData:batchReSendInfraredData")
    @ApiOperation(value = "能源监测-网关数据-批量重新【红外】发送数据", notes = "能源监测-网关数据-批量重新【红外】发送数据")
    public Result<String> batchReSendInfraredData(@RequestParam(name = "deviceAddrList") List<String> deviceAddrList,
                                                  @RequestParam(name = "sdt") String sdt,
                                                  @RequestParam(name = "edt") String edt) {
        emGatewayDataService.batchReSendInfraredData(deviceAddrList, sdt, edt);
        return Result.ok("批量重新发送成功");
    }
}
