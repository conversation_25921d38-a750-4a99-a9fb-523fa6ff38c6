package org.jeecg.modules.sub.listener;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.common.dto.BodyDTO;
import org.jeecg.modules.common.service.EmDealDataService;
import org.jeecg.modules.common.utils.GatewayUtil;
import org.jeecg.modules.sub.constant.enums.TopicSuffixEnum;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * 中电数据消费者
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Slf4j
@Component
public class RabbitMqCetListener {
    @Autowired
    private EmDealDataService emDealDataService;

    /**
     * 中电
     *
     * @param message
     * @param channel
     * @param deliveryTag
     */
    @RabbitListener(bindings = @QueueBinding(
            exchange = @Exchange(name = "${energy-monitor.profiles-active.rabbitMqPrefix}" + ".emqx.exchange"),
            value = @Queue(value = "${energy-monitor.profiles-active.rabbitMqPrefix}" + ".emqx.message.publish", durable = "true")
    ), concurrency = "8-10")
    public void onSensorDataMessage(Message<String> message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        log.info("RabbitMqCetListener → {}", message.getPayload());
        try {
            BodyDTO bodyDTO = JSON.parseObject(message.getPayload(), BodyDTO.class);
            String topic = bodyDTO.getTopic();
            // 获取topic后缀
            String topicSuffix = GatewayUtil.getTopicSuffix(topic);
            TopicSuffixEnum topicSuffixEnum = TopicSuffixEnum.getEnumBySuffix(topicSuffix);
            if (Objects.isNull(topicSuffixEnum)) {
                log.warn("topic:{}，系统无法识别，丢弃数据", topic);
            } else {
                switch (topicSuffixEnum) {
                    case HISTORY:
                    case RTG:
                        // 定时上传数据
                        emDealDataService.saveData(topic, bodyDTO.getContent());
                        break;
                    case DEVICED_ID:
                        // 设备准入id
                        emDealDataService.saveDeviceId(topic, bodyDTO.getContent());
                        break;
                    case INFO:
                        // 解密数据打印，不做处理
                        emDealDataService.decryptData(topic, bodyDTO.getContent());
                        break;
                    case REAL_DATA:
                        // 红外数据
                        try {
                            emDealDataService.saveInfraredData(bodyDTO.getContent());
                        } catch (Exception e) {
                            log.error("红外数据消费失败，不重新消费，失败原因 → " + e.getMessage());
                        }
                        break;
                    case GATEWAY_B_REAL_DATA:
                        // 网关B数据
                        try {
                            emDealDataService.saveGatewayBRealData(topic, bodyDTO.getContent());
                        } catch (Exception e) {
                            log.error("网关B数据消费失败，不重新消费，失败原因 → " + e.getMessage());
                        }
                        break;
                    default:
                        break;
                }
            }
            channel.basicAck(deliveryTag, true);
        } catch (Exception e) {
            log.error("中电数据消费失败，重新放回队列，失败原因 → " + e.getMessage());
            try {
                // deliveryTag → 该消息的index，multiple → 是否批量，true:将一次性拒绝所有小于deliveryTag的消息，requeue → 被拒绝的是否重新入队列
                channel.basicNack(deliveryTag, true, true);
            } catch (IOException ex) {
                log.error("中电数据重新放回队列错误，失败原因 → " + ex.getMessage());
            }
        }
    }
}
