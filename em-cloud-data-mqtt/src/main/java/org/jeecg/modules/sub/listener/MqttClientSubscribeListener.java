package org.jeecg.modules.sub.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
//import net.dreamlu.iot.mqtt.codec.MqttQoS;
//import net.dreamlu.iot.mqtt.spring.client.MqttClientSubscribe;
import org.jeecg.modules.common.constant.DataConstants;
import org.jeecg.modules.common.dto.BodyDTO;
import org.jeecg.modules.common.dto.DecryptDataDTO;
import org.jeecg.modules.common.entity.EmSecuritySensor;
import org.jeecg.modules.common.service.EmDealDataService;
import org.jeecg.modules.common.service.EmSecuritySensorService;
import org.jeecg.modules.common.utils.GatewayUtil;
import org.jeecg.modules.sub.constant.enums.TopicSuffixEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 监听
 *
 * <AUTHOR>
 * @date 2023-09-15 11:21
 */
@Slf4j
@Service
public class MqttClientSubscribeListener {
    @Autowired
    private EmDealDataService emDealDataService;
    @Autowired
    private EmSecuritySensorService emSecuritySensorService;

    /**
     * 测试消费
     *
     * @param topic
     * @param payload
     */
    /*@MqttClientSubscribe(value = "/edge/CIMC-PMC4230/3304027410/rtg", qos = MqttQoS.EXACTLY_ONCE)
    public void thingSubRegister(String topic, byte[] payload) {
        // 获取topic后缀
        String topicSuffix = GatewayUtil.getTopicSuffix(topic);
        TopicSuffixEnum topicSuffixEnum = TopicSuffixEnum.getEnumBySuffix(topicSuffix);
        if (Objects.isNull(topicSuffixEnum)) {
            log.warn("topic:{}，系统无法识别，丢弃数据", topic);
        } else {
            switch (topicSuffixEnum) {
                case RTG:
                    // 定时上传数据
                    emDealDataService.saveData(topic, new String(payload, StandardCharsets.UTF_8));
                    break;
                case DEVICED_ID:
                    // 设备准入id
                    emDealDataService.saveDeviceId(topic, new String(payload, StandardCharsets.UTF_8));
                    break;
                case INFO:
                default:
                    break;
            }
        }
    }*/

    /**
     * 测试消费二
     *
     * @param topic
     * @param payload
     */
    /*@MqttClientSubscribe(value = "/edge/CIMC-PMC4230/3304027410/#", qos = MqttQoS.EXACTLY_ONCE)
    public void thingSubRegister2(String topic, byte[] payload) {
//        String msg = new String(payload, StandardCharsets.UTF_8);
//        log.info("消费消息 deviceId  ======→ " + msg);
//        if (topic.contains(DataConstants.DEVICED_ID)) {
//            JSONObject ss = JSON.parseObject(msg, JSONObject.class);
//            System.out.println("588587658686868686868" + ss.getString("deviceId"));
//        }
        // 获取topic后缀
        String topicSuffix = GatewayUtil.getTopicSuffix(topic);
        TopicSuffixEnum topicSuffixEnum = TopicSuffixEnum.getEnumBySuffix(topicSuffix);
        if (Objects.isNull(topicSuffixEnum)) {
            log.warn("topic:{}，系统无法识别，丢弃数据", topic);
        } else {
            switch (topicSuffixEnum) {
                case RTG:
                    // 定时上传数据
                    emDealDataService.saveData(topic, new String(payload, StandardCharsets.UTF_8));
                    break;
                case DEVICED_ID:
                    // 设备准入id
                    emDealDataService.saveDeviceId(topic, new String(payload, StandardCharsets.UTF_8));
                    break;
                case INFO:
                default:
                    break;
            }
        }
    }*/
}
