package org.jeecg.modules.sub.constant.enums;

import lombok.Getter;

/**
 * 中电Topic后缀
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@Getter
public enum TopicSuffixEnum {

    /**
     * 定时上报数据
     * Topic详解： /edge/${供应商产品系列编号}/${物联网关编号}/${对应功能名称}
     * /devicedId : 明文-只在开机上报一次
     * /info : 密文-只在开机上报一次
     * /rtg : 密文-定时上报
     */
    RTG("rtg", "定时上报数据"),
    /**
     * 设备准入id
     */
    DEVICED_ID("devicedId", "设备准入id"),
    /**
     * 设备基本信息
     */
    INFO("info", "设备基本信息"),
    /**
     * 历史数据
     */
    HISTORY("history", "历史数据"),
    /**
     * 红外数据
     */
    REAL_DATA("realData", "红外数据"),
    /**
     * 网关B数据
     */
    GATEWAY_B_REAL_DATA("gatewayBRealData", "网关B数据");


    private final String code;

    private final String remark;

    TopicSuffixEnum(String code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public static TopicSuffixEnum getEnumBySuffix(String code) {
        for (TopicSuffixEnum dataInterfaceEnum : TopicSuffixEnum.values()) {
            if (dataInterfaceEnum.code.equals(code)) {
                return dataInterfaceEnum;
            }
        }
        return null;
    }
}
