//package org.jeecg.modules.sub.listener;
//
//import lombok.extern.slf4j.Slf4j;
//import net.dreamlu.iot.mqtt.core.client.MqttClientCreator;
//import net.dreamlu.iot.mqtt.spring.client.config.MqttClientProperties;
//import net.dreamlu.iot.mqtt.spring.client.event.MqttConnectedEvent;
//import net.dreamlu.iot.mqtt.spring.client.event.MqttDisconnectEvent;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.context.event.EventListener;
//import org.springframework.stereotype.Service;
//
///**
// * 客户端连接状态监听
// *
// * <AUTHOR>
// * @date 2024-01-09 18:05
// */
//@Slf4j
//@Service
//public class MqttClientConnectListener {
//    @Autowired
//    private MqttClientCreator mqttClientCreator;
//    @Lazy
//    @Autowired
//    private MqttClientProperties mqttClientProperties;
//
//    @EventListener
//    public void onConnected(MqttConnectedEvent event) {
//        log.info("MqttConnectedEvent:{}", event);
//    }
//
//    @EventListener
//    public void onDisconnect(MqttDisconnectEvent event) {
//        // 离线时更新重连时的密码，适用于类似阿里云 mqtt clientId 连接带时间戳的方式
//        log.info("MqttDisconnectEvent:{}", event);
//        // 在断线时更新 clientId、username、password, 华为云时需要更新登录用户时间戳
//        mqttClientCreator.clientId(mqttClientProperties.getClientId())
//                .username(mqttClientProperties.getUserName())
//                .password(mqttClientProperties.getPassword());
//    }
//}
