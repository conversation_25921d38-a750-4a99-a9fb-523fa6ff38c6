package org.jeecg.modules.sub.api.controller;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.SysLog;
import org.jeecg.common.constant.SysTips;
import org.jeecg.modules.mqtt.api.IEmDataMqttApi;
import org.jeecg.modules.mqtt.dto.EmSensorRerunCIMCDataDTO;
import org.jeecg.modules.mqtt.dto.GatewayDeviceDTO;
import org.jeecg.modules.sub.service.EmGatewayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * 中电数据网关 Controller
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@Slf4j
@RestController
@RequestMapping("/dataMqtt/api")
public class EmDataMqttApiController implements IEmDataMqttApi {
    @Autowired
    private EmGatewayService emGatewayService;

    /**
     * 新增传感器
     *
     * @param gatewayDeviceDTO
     * @return
     */
    @Override
    @SysLog("中电数据网关-新增网关")
    @PostMapping("/saveOrUpdate")
    public Result<String> saveOrUpdate(@RequestBody GatewayDeviceDTO gatewayDeviceDTO) {
        emGatewayService.saveOrUpdate(gatewayDeviceDTO);
        return Result.ok(SysTips.ADD_SUCCESS);
    }

    /**
     * 删除网关
     *
     * @param id
     * @return
     */
    @Override
    @SysLog("中电数据网关-作废网关")
    @PostMapping(value = "/cancel")
    public Result<String> cancel(@RequestParam("id") String id) {
        emGatewayService.cancel(id);
        return Result.ok(SysTips.SUCCESS_MSG);
    }

    /**
     * 回溯origin数据,指标模板变更、修改安装时间
     *
     * @param emSensorRerunCIMCDataDTO
     * @return
     */
    @Override
    @SysLog("中电数据网关-回溯origin数据")
    @PostMapping(value = "/rerunOriginData")
    public Result<String> rerunOriginData(@RequestBody EmSensorRerunCIMCDataDTO emSensorRerunCIMCDataDTO) {
        log.info("中电回溯origin数据, emSensorRerunCIMCDataDTO- {}", emSensorRerunCIMCDataDTO);
        emGatewayService.rerunData(emSensorRerunCIMCDataDTO);
        return Result.ok("回溯数据中，请稍后！");
    }

    /**
     * 抄表
     *
     * @param gatewayCode
     * @return
     */
    @Override
    @SysLog("中电数据网关-抄表")
    @PostMapping(value = "/getRealData")
    public Result<String> getRealData(@NotBlank(message = "网关编号不能为空！") @RequestParam(name = "gatewayCode") String gatewayCode) {
        emGatewayService.getRealData(gatewayCode);
        return Result.ok();
    }

    /**
     * 回溯历史数据
     *
     * @param gatewayCode
     * @return
     */
    @SysLog("中电数据网关-回溯历史数据")
    @PostMapping(value = "/getHistoryData")
    public Result<String> getHistoryData(@NotBlank(message = "网关编号不能为空！") @RequestParam(name = "gatewayCode") String gatewayCode,
                                         @NotBlank(message = "开始时间不能为空！") @RequestParam(name = "startTime") String startTime,
                                         @NotBlank(message = "结束时间不能为空！") @RequestParam(name = "endTime") String endTime) {
        emGatewayService.getHistoryData(gatewayCode, startTime, endTime);
        return Result.ok();
    }

    /**
     * 下发指令
     *
     * @param gatewayCode
     * @return
     */
    @SysLog("中电数据网关-回溯历史数据")
    @PostMapping(value = "/sendInstructions")
    public Result<String> sendInstructions(@NotBlank(message = "网关编号不能为空！") @RequestParam(name = "gatewayCode") String gatewayCode,
                                           @NotBlank(message = "topic不能为空！") @RequestParam(name = "topic") String topic,
                                           @NotBlank(message = "指令参数不能为空！") @RequestParam(name = "content") String content) {
        emGatewayService.sendInstructions(gatewayCode, topic, content);
        return Result.ok();
    }
}
