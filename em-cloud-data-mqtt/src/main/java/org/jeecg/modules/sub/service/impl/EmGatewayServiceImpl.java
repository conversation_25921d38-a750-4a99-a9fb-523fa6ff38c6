package org.jeecg.modules.sub.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.iot.mqtt.spring.client.MqttClientTemplate;
import org.jeecg.common.constant.StrPool;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.common.config.EmPrefixProperties;
import org.jeecg.modules.common.entity.EmSecuritySensor;
import org.jeecg.modules.common.service.EmDealDataService;
import org.jeecg.modules.common.service.EmSecuritySensorService;
import org.jeecg.modules.common.service.EmSensorOriginDataService;
import org.jeecg.modules.mqtt.dto.EmSensorRerunCIMCDataDTO;
import org.jeecg.modules.mqtt.dto.GatewayDeviceDTO;
import org.jeecg.modules.sub.dto.GatewayCallDTO;
import org.jeecg.modules.sub.dto.GatewayHistoryCallDTO;
import org.jeecg.modules.sub.service.EmGatewayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 网关Service实现
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@Slf4j
@Service
public class EmGatewayServiceImpl implements EmGatewayService {

    @Autowired
    private EmDealDataService emDealDataService;
    @Autowired
    private MqttClientTemplate mqttClientTemplate;
    @Lazy
    @Autowired
    private EmPrefixProperties emPrefixProperties;
    @Autowired
    private EmSecuritySensorService emSecuritySensorService;
    @Autowired
    private EmSensorOriginDataService emSensorOriginDataService;

    @Override
    public void addBatch(List<GatewayDeviceDTO> gatewayDeviceDTOList) {
        if (CollUtil.isNotEmpty(gatewayDeviceDTOList)) {
            List<EmSecuritySensor> emSecuritySensorList = new ArrayList<>();
            gatewayDeviceDTOList.forEach(gatewayDeviceDTO -> {
                EmSecuritySensor emSecuritySensor = emSecuritySensorService.getOneByDeviceAddr(gatewayDeviceDTO.getCode());
                if (Objects.nonNull(emSecuritySensor)) {
                    log.warn("网关：{}，已存在，跳过新增", gatewayDeviceDTO.getCode());
                    return;
                }
                emSecuritySensorList.add(new EmSecuritySensor(gatewayDeviceDTO.getId(), gatewayDeviceDTO.getName(), gatewayDeviceDTO.getCode(), gatewayDeviceDTO.getNeedDecrypt()));
            });
            emSecuritySensorService.saveBatch(emSecuritySensorList);
        }
    }

    @Override
    public void saveOrUpdate(GatewayDeviceDTO gatewayDeviceDTO) {
        EmSecuritySensor emSecuritySensor = new EmSecuritySensor(gatewayDeviceDTO.getId(), gatewayDeviceDTO.getName(), gatewayDeviceDTO.getCode(), gatewayDeviceDTO.getNeedDecrypt());
        emSecuritySensorService.saveOrUpdate(emSecuritySensor);
    }

    @Override
    public void delete(List<String> codeList) {
        if (CollUtil.isNotEmpty(codeList)) {
            emSecuritySensorService.removeByDeviceAddr(codeList);
        }
    }

    @Override
    public void cancel(String id) {
        EmSecuritySensor emSecuritySensor = emSecuritySensorService.getById(id);
        if (Objects.nonNull(emSecuritySensor)) {
            emSecuritySensor.setStatus(Boolean.FALSE);
            emSecuritySensorService.updateById(emSecuritySensor);
        }
    }

    @Override
    public void rerunData(EmSensorRerunCIMCDataDTO emSensorRerunCIMCDataDTO) {
        emSensorOriginDataService.reRunCIMCOriginData(emSensorRerunCIMCDataDTO.getSensorCode(), emSensorRerunCIMCDataDTO.getInstallTime(), emSensorRerunCIMCDataDTO.getEndDate());
    }

    @Override
    public void getRealData(String gatewayCode) {
        EmSecuritySensor emSecuritySensor = emSecuritySensorService.getOneByDeviceAddr(gatewayCode);
        if (Objects.isNull(emSecuritySensor)) {
           throw new JeecgBootException("网关不存在，请检查后再试！");
        }
//        String pKey = emPrefixProperties.getRabbitMqPrefix() + StrPool.SLASH + "PMC4230";
        String pKey = emSecuritySensor.getSupplierCode();
        GatewayCallDTO gatewayCallDTO = new GatewayCallDTO("2.1.0", pKey, gatewayCode, "rtg", 1012800, null);
        String encryptData = emDealDataService.encryptData(gatewayCode, JSON.toJSONString(gatewayCallDTO));
        String topic = "/cloud/" + pKey + "/" + gatewayCode + "/rtg/call";
        mqttClientTemplate.publish(topic, encryptData.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public void getHistoryData(String gatewayCode, String startTime, String endTime) {
        List<String> codeList = Arrays.asList(gatewayCode.split(StrPool.COMMA));
        if (CollUtil.isEmpty(codeList)) {
            return;
        }
        long start = DateUtil.parseDateTime(startTime).getTime() / 1000;
        long end = DateUtil.parseDateTime(endTime).getTime() / 1000;
        if (start > end) {
            log.warn("开始时间不能大于结束时间");
            return;
        }
        for (String code : codeList) {
            String pKey = "BNHL" + StrPool.SLASH + "SKWF-PMC4230";
            GatewayHistoryCallDTO gatewayCallDTO = new GatewayHistoryCallDTO("2.1.0", pKey, code, "history", 1012800, null, start, end);
            log.info("中电网关回溯历史数据, gatewayCallDTO- {}", gatewayCallDTO);
            String encryptData = emDealDataService.encryptData(code, JSON.toJSONString(gatewayCallDTO));
            log.info("中电网关回溯历史数据, encryptData- {}", encryptData);
            String topic = "/cloud/" + pKey + "/" + code + "/history/call";
            mqttClientTemplate.publish(topic, encryptData.getBytes(StandardCharsets.UTF_8));
        }
    }

    @Override
    public void sendInstructions(String gatewayCode, String topic, String content) {
        if (!topic.contains(gatewayCode)) {
            throw new JeecgBootException("topic必须包含网关编号");
        }
        String encryptData = emDealDataService.encryptData(gatewayCode, content);
        mqttClientTemplate.publish(topic, encryptData.getBytes(StandardCharsets.UTF_8));
    }
}
