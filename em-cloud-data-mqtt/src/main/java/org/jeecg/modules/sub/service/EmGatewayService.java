package org.jeecg.modules.sub.service;

import org.jeecg.modules.mqtt.dto.EmSensorRerunCIMCDataDTO;
import org.jeecg.modules.mqtt.dto.GatewayDeviceDTO;

import java.util.List;

/**
 * 网关Service
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
public interface EmGatewayService {
    /**
     * 新增
     *
     * @param gatewayDeviceDTOList
     * @return
     */
    void addBatch(List<GatewayDeviceDTO> gatewayDeviceDTOList);

    /**
     * 新增或修改
     *
     * @param gatewayDeviceDTO
     */
    void saveOrUpdate(GatewayDeviceDTO gatewayDeviceDTO);

    /**
     * 删除
     *
     * @param codeList
     */
    void delete(List<String> codeList);

    /**
     * 作废
     *
     * @param id
     */
    void cancel(String id);

    /**
     * 回溯origin数据
     *
     * @param emSensorRerunCIMCDataDTO
     */
    void rerunData(EmSensorRerunCIMCDataDTO emSensorRerunCIMCDataDTO);

    /**
     * 抄表
     *
     * @param gatewayCode
     */
    void getRealData(String gatewayCode);

    /**
     * 回溯历史数据
     *
     * @param gatewayCode 网关编号
     * @param startTime   开始时间
     * @param endTime     结束时间
     */
    void getHistoryData(String gatewayCode, String startTime, String endTime);

    /**
     * 下发指令
     *
     * @param gatewayCode
     * @param topic
     * @param content
     */
    void sendInstructions(String gatewayCode, String topic, String content);
}
