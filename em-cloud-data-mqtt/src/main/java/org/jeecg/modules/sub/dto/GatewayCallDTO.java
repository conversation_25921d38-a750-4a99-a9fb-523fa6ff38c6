package org.jeecg.modules.sub.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 网关召读数据DTO
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GatewayCallDTO implements Serializable {
    private static final long serialVersionUID = -3054616318134600845L;
    /**
     * 协议版本
     */
    private String ver;
    /**
     * 供应商产品系列编号
     */
    private String pKey;
    /**
     * 网关编号
     */
    private String sn;
    /**
     * 包类型为 rtg，其它字段和上报实时数据一致，数量比较大时，可以分包传输。
     */
    private String type;
    /**
     * 缺省表示自动上报，有序号表示召读消息
     */
    private Integer seq;
    /**
     * 如果有这个字段，表示召读指定的设备实时数据，如果没有，表示召读所有设备的数据
     */
    private String dev;
}
