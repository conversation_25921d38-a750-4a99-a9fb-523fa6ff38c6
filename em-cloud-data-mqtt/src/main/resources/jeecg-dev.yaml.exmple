spring:
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          url: *****************************************************************************************************************************************************************************
          username: root
          password: 582867
          driver-class-name: com.mysql.cj.jdbc.Driver
          # 多数据源配置
          #multi-datasource1:
          #url: ***************************************************************************************************************************************************************************************************************************
          #username: root
          #password: root
          #driver-class-name: com.mysql.cj.jdbc.Driver
  #redis 配置
  redis:
    database: 0
    host: 127.0.0.1
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: ''
    port: 6379
  ## mongo配置
  data:
    mongodb:
      uri: mongodb://admin:DJr!Y^$jOI@************:27017/energy_monitor?authSource=admin
  #rabbitmq配置
  rabbitmq:
    host: 127.0.0.1
    username: guest
    password: guest
    port: 5672
    publisher-confirm-type: correlated
    publisher-returns: true
    virtual-host: /
    listener:
      simple:
        acknowledge-mode: manual
        #消费者的最小数量
        concurrency: 1
        #消费者的最大数量
        max-concurrency: 1
        #是否支持重试
        retry:
          enabled: true
#jeecg专用配置
jeecg:
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: oss
  path:
    #文件上传根目录 设置
    upload: D://opt//upFiles
    #webapp文件路径
    webapp: D://opt//webapp
    #短信秘钥
#Mybatis输出sql日志
logging:
  level:
    org.jeecg.modules.system.mapper: info

process-data:
  url: http://yunsocket.acrel-eem.com/api/Yun/
  username: ys001
  password: 123456
  # 传感器新增时数据追溯开始时间
  data-date: 2022-05-11 00:00:00

# rabbitMq前缀
energy-monitor:
  profiles-active:
    rabbitMqPrefix: dev