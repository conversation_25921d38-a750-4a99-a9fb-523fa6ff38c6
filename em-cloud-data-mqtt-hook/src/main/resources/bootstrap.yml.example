grpc:
  server:
    port: 3333

server:
  port: 2222

spring:
  application:
    name: em-data-mqtt-hook
  profiles:
    active: dev
  cloud:
    bus:
      id: ${spring.application.name}:${server.port}
    nacos:
      config:
        username: nacos
        password: nacos
        namespace: d8a009f5-f100-4d41-975f-dbba07f49957
        server-addr: 127.0.0.1:8848
        group: DEFAULT_GROUP
        file-extension: yaml
        prefix: em-data-mqtt-hook
      discovery:
        namespace: d8a009f5-f100-4d41-975f-dbba07f49957
        server-addr: 127.0.0.1:8848
        watch:
          enabled: false
  # RabbitMQ配置信息
  rabbitmq:
    addresses: 127.0.0.1
    port: 5672
    userName: admin
    password: rabbitmq
    virtual-host: em
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      type: simple
      simple:
        # 每次从RabbitMQ获取的消息数量
        prefetch: 1
        # 【 (若有重试)重试次数超过限制后】，将被拒绝的消息重新入队
        default-requeue-rejected: true
        # 每个队列启动的消费者数量
        concurrency: 4
        # 每个队列最大的消费者数量
        max-concurrency: 50
        # 手动签收ACK
        acknowledge-mode: manual
        retry:
          # 10秒后重试
          initial-interval: 10000
    template:
      mandatory: false

rocketmq:
  name-server: *************:9876 # 访问地址
  producer:
    group: emqx_hook # 必须指定group
    send-message-timeout: 3000 # 消息发送超时时长，默认3s
    retry-times-when-send-failed: 3 # 同步发送消息失败重试次数，默认2
    retry-times-when-send-async-failed: 3 # 异步发送消息失败重试次数，默认2

logging:
  level:
    org.jeecg: info
  config: classpath:logback-spring.xml