package org.jeecg.modules.mqtt.config;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.mqtt.constant.QueueConstants;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;

/**
 * RabbitMQ配置
 *
 * <AUTHOR>
 * @date 2024-01-18 10:36
 */
@Slf4j
@Configuration
public class RabbitMqConfig {
    @Autowired
    private RabbitAdmin rabbitAdmin;
    // 交换机
    public static final String EMQX_EXCHANGE = "emqx.exchange";

    @Bean
    public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
        //设置忽略声明异常
        rabbitAdmin.setIgnoreDeclarationExceptions(true);
        return rabbitAdmin;
    }

    @Bean
    public void initQueue() {
        // 创建交换机
        FanoutExchange exchange = createExchange(EMQX_EXCHANGE);
        // 反射队列常量类
        Class<?> clazz = QueueConstants.class;
        // 获取类声明的字段
        Field[] fields = clazz.getDeclaredFields();
        // 声明队列
        createQueue(exchange, fields);
    }

    /**
     * 创建交换机
     *
     * @param exchangeName
     * @return
     */
    public FanoutExchange createExchange(String exchangeName) {
        // 创建交换机
        FanoutExchange exchange = new FanoutExchange(exchangeName, true, false);
        // 声明
        rabbitAdmin.declareExchange(exchange);
        return exchange;
    }

    /**
     * 创建队列
     *
     * @param exchange
     * @param fields
     * @throws IllegalAccessException
     */
    public void createQueue(FanoutExchange exchange, Field[] fields) {
        // 遍历字段，声明队列
        for (Field field : fields) {
            if (!field.isAccessible()) {
                // 设置为可访问状态
                field.setAccessible(true);
                // 获取字段值
                Object value = ReflectionUtils.getField(field, null);
                // 转换队列名称
                String queueName = String.valueOf(value);
                // 创建队列
                Queue queue = new Queue(queueName);
                // 声明队列
                rabbitAdmin.declareQueue(queue);
                // 绑定
                Binding binding = BindingBuilder.bind(queue).to(exchange);
                // 声明绑定
                rabbitAdmin.declareBinding(binding);
            }
        }
    }
}
