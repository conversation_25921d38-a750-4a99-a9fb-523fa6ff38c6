package org.jeecg.modules.mqtt.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import io.emqx.exhook.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.jeecg.modules.mqtt.constant.QueueConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;

import java.util.Arrays;

/**
 * EMQX Hook转RocketMQ Service实现
 *
 * <AUTHOR>
 * @date 2024-01-15 14:13
 */
@Slf4j
//@GrpcService
public class EmqxHookToRocketMqServiceImpl extends HookProviderGrpc.HookProviderImplBase {
    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Override
    public void onProviderLoaded(ProviderLoadedRequest request, StreamObserver<LoadedResponse> responseObserver) {
        log.info("onProviderLoaded → {}", request);
        HookSpec[] specs = {
                HookSpec.newBuilder().setName("client.connect").build(),
                HookSpec.newBuilder().setName("client.connack").build(),
                HookSpec.newBuilder().setName("client.connected").build(),
                HookSpec.newBuilder().setName("client.disconnected").build(),
                HookSpec.newBuilder().setName("client.authenticate").build(),
                HookSpec.newBuilder().setName("client.authorize").build(),
                HookSpec.newBuilder().setName("client.subscribe").build(),
                HookSpec.newBuilder().setName("client.unsubscribe").build(),

                HookSpec.newBuilder().setName("session.created").build(),
                HookSpec.newBuilder().setName("session.subscribed").build(),
                HookSpec.newBuilder().setName("session.unsubscribed").build(),
                HookSpec.newBuilder().setName("session.resumed").build(),
                HookSpec.newBuilder().setName("session.discarded").build(),
                HookSpec.newBuilder().setName("session.takenover").build(),
                HookSpec.newBuilder().setName("session.terminated").build(),

                HookSpec.newBuilder().setName("message.publish").build(),
                HookSpec.newBuilder().setName("message.delivered").build(),
                HookSpec.newBuilder().setName("message.acked").build(),
                HookSpec.newBuilder().setName("message.dropped").build()
        };
        LoadedResponse reply = LoadedResponse.newBuilder().addAllHooks(Arrays.asList(specs)).build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onProviderUnloaded(ProviderUnloadedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        log.info("onProviderUnloaded → {}", request);
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientConnect(ClientConnectRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onClientConnect → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientConnack(ClientConnackRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onClientConnack → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientConnected(ClientConnectedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onClientConnected → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientDisconnected(ClientDisconnectedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onClientDisconnected → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientAuthenticate(ClientAuthenticateRequest request, StreamObserver<ValuedResponse> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onClientAuthenticate → {}", request);
        }
        ValuedResponse reply = ValuedResponse.newBuilder()
                .setBoolResult(true)
                .setType(ValuedResponse.ResponsedType.STOP_AND_RETURN)
                .build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientAuthorize(ClientAuthorizeRequest request, StreamObserver<ValuedResponse> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onClientAuthorize → {}", request);
        }
        ValuedResponse reply = ValuedResponse.newBuilder()
                .setBoolResult(true)
                .setType(ValuedResponse.ResponsedType.STOP_AND_RETURN)
                .build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientSubscribe(ClientSubscribeRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onClientSubscribe → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientUnsubscribe(ClientUnsubscribeRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onClientUnsubscribe → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionCreated(SessionCreatedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onSessionCreated → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionSubscribed(SessionSubscribedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onSessionSubscribed → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionUnsubscribed(SessionUnsubscribedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onSessionUnsubscribed → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionResumed(SessionResumedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onSessionResumed → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionDiscarded(SessionDiscardedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onSessionDiscarded → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionTakenover(SessionTakenoverRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onSessionTakenover → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionTerminated(SessionTerminatedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onSessionTerminated → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onMessagePublish(MessagePublishRequest request, StreamObserver<ValuedResponse> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onMessagePublish → {}", request);
        }
        ValuedResponse reply = ValuedResponse.newBuilder()
                .setType(ValuedResponse.ResponsedType.STOP_AND_RETURN).build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
        // 发送消息到MQ
        this.sendMsg(QueueConstants.MESSAGE_PUBLISH, request);
    }

    @Override
    public void onMessageDelivered(MessageDeliveredRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onMessageDelivered → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onMessageAcked(MessageAckedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onMessageAcked → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onMessageDropped(MessageDroppedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if(log.isDebugEnabled()) {
            log.debug("onMessageDropped → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    /**
     * 发送消息
     *
     * @param topic
     * @param msg
     */
    private void sendMsg(String topic, Object msg) {
        rocketMQTemplate.asyncSend(topic, MessageBuilder.withPayload(msg).build(), new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                if(log.isDebugEnabled()) {
                    log.debug("onSuccess → {}", sendResult);
                }
            }

            @Override
            public void onException(Throwable throwable) {
                if(log.isErrorEnabled()) {
                    log.debug("onException → {}", ExceptionUtil.getMessage(throwable));
                }
            }
        });
    }
}
