package org.jeecg.modules.mqtt.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import io.emqx.exhook.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.mqtt.config.RabbitMqConfig;
import org.jeecg.modules.mqtt.constant.QueueConstants;
import org.jeecg.modules.mqtt.constant.SysRedisKeyConstants;
import org.jeecg.modules.mqtt.dto.BodyDTO;
import org.jeecg.modules.mqtt.dto.IotPlatformDTO;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Arrays;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * EMQX Hook转RabbitMQ Service实现
 *
 * <AUTHOR>
 * @date 2024-01-15 14:13
 */
@Slf4j
@GrpcService
public class EmqxHookToRabbitMqServiceImpl extends HookProviderGrpc.HookProviderImplBase {
    @Value("${data.dataType.emData}")
    private Boolean isEmData;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void onProviderLoaded(ProviderLoadedRequest request, StreamObserver<LoadedResponse> responseObserver) {
        log.info("onProviderLoaded → {}", request);
        HookSpec[] specs = {
                /*HookSpec.newBuilder().setName("client.connect").build(),
                HookSpec.newBuilder().setName("client.connack").build(),
                HookSpec.newBuilder().setName("client.connected").build(),
                HookSpec.newBuilder().setName("client.disconnected").build(),
                HookSpec.newBuilder().setName("client.authenticate").build(),
                HookSpec.newBuilder().setName("client.authorize").build(),
                HookSpec.newBuilder().setName("client.subscribe").build(),
                HookSpec.newBuilder().setName("client.unsubscribe").build(),
                HookSpec.newBuilder().setName("session.created").build(),
                HookSpec.newBuilder().setName("session.subscribed").build(),
                HookSpec.newBuilder().setName("session.unsubscribed").build(),
                HookSpec.newBuilder().setName("session.resumed").build(),
                HookSpec.newBuilder().setName("session.discarded").build(),
                HookSpec.newBuilder().setName("session.takenover").build(),
                HookSpec.newBuilder().setName("session.terminated").build(),
                HookSpec.newBuilder().setName("message.delivered").build(),
                HookSpec.newBuilder().setName("message.acked").build(),
                HookSpec.newBuilder().setName("message.dropped").build(),*/
                HookSpec.newBuilder().setName("message.publish").build()
        };
        LoadedResponse reply = LoadedResponse.newBuilder().addAllHooks(Arrays.asList(specs)).build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onProviderUnloaded(ProviderUnloadedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        log.info("onProviderUnloaded → {}", request);
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientConnect(ClientConnectRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onClientConnect → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientConnack(ClientConnackRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onClientConnack → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientConnected(ClientConnectedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onClientConnected → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientDisconnected(ClientDisconnectedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onClientDisconnected → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientAuthenticate(ClientAuthenticateRequest request, StreamObserver<ValuedResponse> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onClientAuthenticate → {}", request);
        }
        ValuedResponse reply = ValuedResponse.newBuilder()
                .setBoolResult(true)
                .setType(ValuedResponse.ResponsedType.CONTINUE)
                .build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientAuthorize(ClientAuthorizeRequest request, StreamObserver<ValuedResponse> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onClientAuthorize → {}", request);
        }
        ValuedResponse reply = ValuedResponse.newBuilder()
                .setBoolResult(true)
                .setType(ValuedResponse.ResponsedType.CONTINUE)
                .build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientSubscribe(ClientSubscribeRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onClientSubscribe → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onClientUnsubscribe(ClientUnsubscribeRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onClientUnsubscribe → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionCreated(SessionCreatedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onSessionCreated → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionSubscribed(SessionSubscribedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onSessionSubscribed → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionUnsubscribed(SessionUnsubscribedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onSessionUnsubscribed → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionResumed(SessionResumedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onSessionResumed → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionDiscarded(SessionDiscardedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onSessionDiscarded → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionTakenover(SessionTakenoverRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onSessionTakenover → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onSessionTerminated(SessionTerminatedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onSessionTerminated → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onMessagePublish(MessagePublishRequest request, StreamObserver<ValuedResponse> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onMessagePublish → {}", request);
        }
        log.info("onMessagePublish → {}", request.getMessage());
        ValuedResponse reply = ValuedResponse.newBuilder()
                .setType(ValuedResponse.ResponsedType.STOP_AND_RETURN).build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
        // 发送消息到MQ
        this.sendMsg(QueueConstants.MESSAGE_PUBLISH, request.getMessage());
    }

    @Override
    public void onMessageDelivered(MessageDeliveredRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onMessageDelivered → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onMessageAcked(MessageAckedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onMessageAcked → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void onMessageDropped(MessageDroppedRequest request, StreamObserver<EmptySuccess> responseObserver) {
        if (log.isDebugEnabled()) {
            log.debug("onMessageDropped → {}", request);
        }
        EmptySuccess reply = EmptySuccess.newBuilder().build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    /**
     * 发送消息
     *
     * @param routingKey
     * @param msg
     */
    private void sendMsg(String routingKey, io.emqx.exhook.Message msg) {
        String topic = msg.getTopic();
        // 过滤一下没有用的topic，防止下游数据转换失败
        if (match(topic)) {
            String profilesPrefix = getProfilesPrefix(topic);
            // 中集数据并且是网关b数据,中集只有网关b数据，可以不需要获取mq前缀
            if (Boolean.TRUE.equals(isEmData)) {
                if (!topic.contains("gatewayBRealData")) {
                    return;
                }
                rabbitTemplate.convertAndSend("dev." + RabbitMqConfig.EMQX_EXCHANGE, "dev." + routingKey, convertMessage(msg));
                return;
            }
            if (StringUtils.isBlank(profilesPrefix)) {
                log.error("topic:{},获取mq前缀为空！", topic);
                return;
            }
            log.info("匹配成功，topic:{},推送到rabbitmq, profilesPrefix:{}", topic, profilesPrefix);
            rabbitTemplate.convertAndSend(profilesPrefix + RabbitMqConfig.EMQX_EXCHANGE, profilesPrefix + routingKey, convertMessage(msg));
        }
    }

    /**
     * 正则匹配
     *
     * @param params
     * @return
     */
    private boolean match(String params) {
        Pattern pattern = Pattern.compile("/edge/[a-zA-Z]{2,20}/\\w[-\\w.+]*/\\d{5,15}/(rtg|devicedId|info|history|realData|gatewayBRealData)");
        Matcher matcher = pattern.matcher(params);
        return matcher.find();
    }

    /**
     * 获取前缀
     *
     * @param params
     * @return
     */
    private String getProfilesPrefix(String params) {
        Pattern pattern = Pattern.compile("/edge/([A-Z]{2,20})/");
        Matcher matcher = pattern.matcher(params);
        if (matcher.find()) {
            String groupText = matcher.group(1);
            if (StringUtils.isBlank(groupText)) {
                log.error("groupText 匹配为空！");
                return null;
            }
            // 缓存中获取
            Object value = redisTemplate.opsForHash().get(SysRedisKeyConstants.IotPlatform.PLATFORM_CODE_HASH, CharSequenceUtil.format(SysRedisKeyConstants.IotPlatform.PLATFORM_CODE_KEY, groupText));
            IotPlatformDTO iotPlatformDTO = JSON.parseObject(String.valueOf(value), IotPlatformDTO.class, Feature.AutoCloseSource);
            if (Objects.isNull(iotPlatformDTO)) {
                return null;
            } else {
                return iotPlatformDTO.getRabbitMqPrefix();
            }
        }
        return null;
    }

    /**
     * 转换Message对象
     *
     * @param msg
     * @return
     */
    public Message convertMessage(io.emqx.exhook.Message msg) {
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setContentType(MessageProperties.CONTENT_TYPE_JSON);
        BodyDTO bodyDTO = new BodyDTO(msg.getTopic(), msg.getPayload().toStringUtf8());
        return new Message(JSON.toJSONString(bodyDTO).getBytes(), messageProperties);
    }
}
