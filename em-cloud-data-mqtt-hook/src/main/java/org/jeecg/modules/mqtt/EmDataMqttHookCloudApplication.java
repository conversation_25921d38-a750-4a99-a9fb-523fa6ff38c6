package org.jeecg.modules.mqtt;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * EMQX Hook Server
 *
 * <AUTHOR>
 * @date 2024-01-15 13:56
 */
@Slf4j
@EnableDiscoveryClient
@SpringBootApplication
public class EmDataMqttHookCloudApplication extends SpringBootServletInitializer {
    public static void main(String[] args) throws UnknownHostException {
        SpringApplication application = new SpringApplication(EmDataMqttHookCloudApplication.class);
        application.setAllowBeanDefinitionOverriding(Boolean.TRUE);
        application.setAllowCircularReferences(Boolean.TRUE);
        ConfigurableApplicationContext applicationContext = application.run(args);
        Environment env = applicationContext.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = StrUtil.nullToEmpty(env.getProperty("server.servlet.context-path"));
        log.info("\n----------------------------------------------------------\n" +
                "Application EM-Data-Mqtt-Hook-Cloud is running! Access URLs:\n" +
                "Local: \t\thttp://localhost:" + port + path + "/doc.html\n" +
                "External: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
                "Swagger文档: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
                "----------------------------------------------------------");
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(EmDataMqttHookCloudApplication.class);
    }
}
