package org.jeecg.modules.mqtt.listener;

import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.mqtt.config.RabbitMqConfig;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * 传感器数据消费者
 *
 * <AUTHOR>
 * @date 2022/1/14
 */
@Slf4j
//@Component
public class RabbitMqListener {
    /**
     * 传感器数据
     * 安科瑞和前置机
     *
     * @param message
     * @param channel
     * @param deliveryTag
     */
    @RabbitListener(bindings = @QueueBinding(
            exchange = @Exchange(name = RabbitMqConfig.EMQX_EXCHANGE),
            value = @Queue(value = "emqx_msg", durable = "true")
    ), concurrency = "1", exclusive = true)
    public void onSensorDataMessage(Message<String> message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        log.info("RabbitMqListener → {}", message);
    }
}
