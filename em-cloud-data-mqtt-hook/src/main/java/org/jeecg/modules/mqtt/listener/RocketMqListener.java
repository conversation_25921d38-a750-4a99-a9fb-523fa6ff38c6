package org.jeecg.modules.mqtt.listener;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * 传感器数据消费者
 *
 * <AUTHOR>
 * @date 2022/1/14
 */
@Slf4j
//@Component
//@RocketMQMessageListener(topic = "bnhl_emqx_msg", consumerGroup = "bnhl_em")
public class RocketMqListener implements RocketMQListener<String> {
    // 监听到消息就会执行此方法
    @Override
    public void onMessage(String msg) {
        log.info("消息时间 → {}, 消息内容 → {}", DateUtil.now(), msg);
        int num = Integer.parseInt(msg);
        if(num%2 == 0) {
            log.info("消息成功, 消息内容 → {}", msg);
        } else {
            throw new RuntimeException("抛异常");
        }
    }
}
