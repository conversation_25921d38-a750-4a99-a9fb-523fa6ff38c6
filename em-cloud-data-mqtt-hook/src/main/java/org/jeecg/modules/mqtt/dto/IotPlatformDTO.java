package org.jeecg.modules.mqtt.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 网关-平台配置 DTO
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@Data
public class IotPlatformDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private String id;
    /**
     * 平台名称
     */
    private String name;
    /**
     * 平台编码
     */
    private String code;
    /**
     * MQ前缀
     */
    private String rabbitMqPrefix;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 服务地址
     */
    private String serviceAddress;
    /**
     * 状态：0无效 1有效
     */
    private Boolean status;
}
