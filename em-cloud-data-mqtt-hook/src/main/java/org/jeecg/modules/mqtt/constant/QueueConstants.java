package org.jeecg.modules.mqtt.constant;

/**
 * 队列常量
 *
 * <AUTHOR>
 * @date 2024-01-18 11:00
 */
public interface QueueConstants {
    // 连接建立事件
    String CLIENT_CONNECT = "emqx.client.connect";
    // 连接已断开事件
    String CLIENT_CONNACK = "emqx.client.connack";
    // 连接已建立事件
    String CLIENT_CONNECTED = "emqx.client.connected";
    // 客户端已断开连接
    String CLIENT_DISCONNECTED = "emqx.client.disconnected";
    // 实现客户端接入认证
    String CLIENT_AUTHENTICATE = "emqx.client.authenticate";
    // 实现 PUB/SUB 权限检查
    String CLIENT_AUTHORIZE = "emqx.client.authorize";
    // 客户端订阅
    String CLIENT_SUBSCRIBE = "emqx.client.subscribe";
    // 客户端取消订阅
    String CLIENT_UNSUBSCRIBE = "emqx.client.unsubscribe";
    // 会话已创建
    String SESSION_CREATED = "emqx.session.created";
    // 订阅关系已创建
    String SESSION_SUBSCRIBED = "emqx.session.subscribed";
    // 订阅关系已取消
    String SESSION_UNSUBSCRIBED = "emqx.session.unsubscribed";
    // 会话已重新启用
    String SESSION_RESUMED = "emqx.session.resumed";
    // 会话已被关闭（被丢弃）
    String SESSION_DISCARDED = "emqx.session.discarded";
    // 会话已被关闭（被接管）
    String SESSION_TAKENOVER = "emqx.session.takenover";
    // 会话已被关闭（被终止）
    String SESSION_TERMINATED = "emqx.session.terminated";
    // 处理即将发布到 Broker 的上行消息
    String MESSAGE_PUBLISH = "emqx.message.publish";
    // 处理将要投递到 Socket 的下行消息
    String MESSAGE_DELIVERED = "emqx.message.delivered";
    // 已收到消息的确认报文
    String MESSAGE_ACKED = "emqx.message.acked";
    // 出现消息丢弃时触发
    String MESSAGE_DROPPED = "emqx.message.dropped";
}
