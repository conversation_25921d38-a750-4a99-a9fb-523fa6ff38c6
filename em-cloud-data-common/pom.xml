<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bonait.boot</groupId>
        <artifactId>em-data-parent</artifactId>
        <version>2.3.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>em-cloud-data-common</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.bonait.boot</groupId>
            <artifactId>em-base-core</artifactId>
        </dependency>
        <!-- MongoDB -->
        <dependency>
            <groupId>com.bonait.boot</groupId>
            <artifactId>em-module-mongo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.grepw</groupId>
            <artifactId>sdk-authority</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/sdk-authority-1.1.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/bcprov-jdk.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>
    </dependencies>

</project>
