package org.jeecg.modules.common.service;

import org.jeecg.modules.common.entity.EmGatewayData;
import org.jeecg.modules.common.entity.EmSensorOriginData;

import java.util.Date;
import java.util.List;

/**
 * 能源监测-传感器原始数据Service
 *
 * <AUTHOR>
 * @date 2022-04-24 14:37
 */
public interface EmSensorOriginDataService {
    /**
     * 新增
     *
     * @param gatewayData
     * @return
     */
    List<EmSensorOriginData> insert(EmGatewayData gatewayData);

    /**
     * 获取数据列表
     *
     * @param dataDate
     * @param deviceAddr
     * @param di
     * @return
     */
    List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, String di);

    /**
     * 获取数据列表
     *
     * @param dataDate
     * @param deviceAddr
     * @param pn
     * @param di
     * @return
     */
    List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, Integer pn, String di);

    /**
     * 获取数据列表
     *
     * @param dataDate
     * @param deviceAddr
     * @param pn
     * @return
     */
    List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, Integer pn);

    /**
     * 根据网关ID，数据时间和DI获取本条下一条
     *
     * @param deviceAddr
     * @param dataTime
     * @return
     */
    EmSensorOriginData getNowOrNextOneBy(String deviceAddr, Date dataTime);

    /**
     * 根据网关ID和DI获取第一条数据
     *
     * @param deviceAddr
     * @param di
     * @return
     */
    EmSensorOriginData getFirstOne(String deviceAddr, String di);

    /**
     * 中电重新发送数据
     *
     * @param deviceAddr
     * @param sdt
     * @param edt
     */
    void reRunCIMCOriginData(String deviceAddr, Date sdt, Date edt);
}
