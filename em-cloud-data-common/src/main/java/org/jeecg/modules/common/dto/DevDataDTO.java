package org.jeecg.modules.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 解密后数据
 *
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
public class DevDataDTO implements Serializable {
    private static final long serialVersionUID = -7597598328052175029L;
    /**
     * dev
     */
    private String dev;
    /**
     * sysId
     */
    private String sysId;
    /**
     * 时间戳
     */
    private long ts;
    /**
     * 体
     */
    @JSONField(name = "d")
    private List<EmAuthSensorOriginDataDTO> emAuthSensorOriginDataDTOList;
}
