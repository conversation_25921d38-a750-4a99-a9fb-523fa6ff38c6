package org.jeecg.modules.common.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.DatabaseConstants;
import org.jeecg.modules.common.service.EmProcessDataLogService;
import org.jeecg.modules.common.entity.EmProcessDataLog;
import org.jeecg.modules.mongo.util.MongodbDaoUtils;
import org.jeecg.modules.common.repository.EmProcessDataLogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 能源监测-安科瑞数据处理-接口日志
 *
 * <AUTHOR>
 * @date 2022-05-30
 */
@Slf4j
@Service
public class EmProcessDataLogServiceImpl implements EmProcessDataLogService {
    @Autowired
    private MongodbDaoUtils mongodbDaoUtils;
    @Autowired
    private EmProcessDataLogRepository emProcessDataLogRepository;

    @Override
    public EmProcessDataLog insert(EmProcessDataLog emProcessDataLog) {
        emProcessDataLog.setCreateBy(DatabaseConstants.SYS_ADMIN);
        emProcessDataLog.setCreateTime(new Date());
        return emProcessDataLogRepository.insert(emProcessDataLog);
    }

    @Override
    public EmProcessDataLog getById(String id) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and("_id").is(id);
        return mongodbDaoUtils.getOne(query.addCriteria(criteria), EmProcessDataLog.class);
    }

    @Override
    public void update(EmProcessDataLog emProcessDataLog) {
        Criteria criteria = Criteria.where("_id").is(emProcessDataLog.getId());
        Query query = Query.query(criteria);
        Update update = new Update();
        update.set("is_already_retry", emProcessDataLog.getAlreadyRetry());
        update.set("is_retry_success", emProcessDataLog.getRetrySuccess());
        update.set("update_time", new Date());
        mongodbDaoUtils.update(query, update, EmProcessDataLog.class);
    }
}
