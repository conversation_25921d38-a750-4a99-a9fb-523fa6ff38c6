package org.jeecg.modules.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 能源监测-数据处理-接口日志
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Data
@Document(value = "em_process_data_log")
@ApiModel(value = "em_process_data_log对象", description = "能源监测-数据处理-接口日志")
@CompoundIndexes({
        @CompoundIndex(useGeneratedName = true, def = "{'device_addr':1, 'data_date':1}", background = true),
        @CompoundIndex(useGeneratedName = true, def = "{'device_addr':1, 'data_date':-1}", background = true)
})
public class EmProcessDataLog {
    private static final long serialVersionUID = 1L;
    /**
     * 接口类型：1.用户登录，2.获取用户名下所有电表，3.获取电表信息，4.电费充值，5.分合闸控制，6.开户，7.修改用户，8.销户，9.用电统计，10.根据电表编号查管控表信息的接口，11.根据电表编号获取抄表信息，12.获取管理员账号下所有电表，13.获得一段时间内的电表电能数据，14.获得一段时间内的电表电能数据，15.获取管控表数据，16.获取管控表数据，17.获取某天用电数据
     */
    @NotNull(message = "接口类型：1.用户登录，2.获取用户名下所有电表，3.获取电表信息，4.电费充值，5.分合闸控制，6.开户，7.修改用户，8.销户，9.用电统计，10.根据电表编号查管控表信息的接口，11.根据电表编号获取抄表信息，12.获取管理员账号下所有电表，13.获得一段时间内的电表电能数据，14.获得一段时间内的电表电能数据，15.获取管控表数据，16.获取管控表数据，17.获取某天用电数据不能为空！")
    @ApiModelProperty(value = "接口类型：1.用户登录，2.获取用户名下所有电表，3.获取电表信息，4.电费充值，5.分合闸控制，6.开户，7.修改用户，8.销户，9.用电统计，10.根据电表编号查管控表信息的接口，11.根据电表编号获取抄表信息，12.获取管理员账号下所有电表，13.获得一段时间内的电表电能数据，14.获得一段时间内的电表电能数据，15.获取管控表数据，16.获取管控表数据，17.获取某天用电数据")
    private Integer type;
    /**
     * 接口路径
     */
    @Field("interface_url")
    @NotBlank(message = "接口路径不能为空！")
    @ApiModelProperty(value = "接口路径")
    private String interfaceUrl;
    /**
     * 记录日期
     */
    @ApiModelProperty(value = "记录日期")
    @Field("data_date")
    private String dataDate;
    /**
     * 记录日期
     */
    @Field("device_addr")
    @ApiModelProperty(value = "记录日期")
    private String deviceAddr;
    /**
     * 请求类型：1 get，2-post
     */
    @Excel(name = "请求类型：1 get，2 -post", width = 15)
    @Field("request_type")
    @NotNull(message = "请求类型：1 get，2 -post不能为空！")
    @ApiModelProperty(value = "请求类型：1 get，2 post")
    private Integer requestType;
    /**
     * 请求参数
     */
    @Excel(name = "请求参数", width = 15)
    @Field("request_param")
    @NotBlank(message = "请求参数不能为空！")
    @ApiModelProperty(value = "请求参数")
    private String requestParam;
    /**
     * 返回值
     */
    @Excel(name = "返回值", width = 15)
    @Field("response_body")
    @NotBlank(message = "返回值不能为空！")
    @ApiModelProperty(value = "返回值")
    private String responseBody;
    /**
     * 返回消息
     */
    @Excel(name = "返回消息", width = 15)
    @Field("response_msg")
    @NotBlank(message = "返回消息不能为空！")
    @ApiModelProperty(value = "返回消息")
    private String responseMsg;
    /**
     * 是否成功：0.否，1.是
     */
    @Excel(name = "是否成功：0.否，1.是", width = 15)
    @NotNull(message = "是否成功：0.否，1.是不能为空！")
    @ApiModelProperty(value = "是否成功：0.否，1.是")
    @TableField("is_success")
    private Boolean success;
    /**
     * 是否已经重试：0-否，1-是
     */
    @Excel(name = "是否已经重试：0-否，1-是", width = 15)
    @Field("is_already_retry")
    @NotNull(message = "是否已经重试：0-否，1-是不能为空！")
    @ApiModelProperty(value = "是否已经重试：0-否，1-是")
    private Boolean alreadyRetry;
    /**
     * 重试是否成功：0-否，1-是
     */
    @Excel(name = "重试是否成功：0-否，1-是", width = 15)
    @Field("is_retry_success")
    @NotNull(message = "重试是否成功：0-否，1-是不能为空！")
    @ApiModelProperty(value = "重试是否成功：0-否，1-是")
    private Boolean retrySuccess;
    /**
     * 主键Id，mongodb自动生成
     */
    @Id
    private String id;
    /**
     * 创建人
     */
    @Field("create_by")
    private String createBy;
    /**
     * 创建时间
     */
    @Indexed(expireAfterSeconds = 8035200)
    @Field("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    @Field("update_by")
    private String updateBy;
    /**
     * 更新时间
     */
    @Field("update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
