package org.jeecg.modules.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializeFilter;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.jeecg.common.constant.StrPool;

import java.util.Arrays;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 网关工具类
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GatewayUtil {

    /**
     * 获取topic中的sn
     *
     * @param topic
     * @return
     */
    public static String getDeviceSn(String topic) {
        int firstLastIndex = topic.lastIndexOf(StrPool.SLASH);
        int secondLastIndex = topic.lastIndexOf(StrPool.SLASH, firstLastIndex - 1);
        return topic.substring(secondLastIndex + 1, firstLastIndex);
    }

    /**
     * 正则匹配topic后缀
     *
     * @param params
     * @return
     */
    public static String getTopicSuffix(String params) {
        Pattern pattern = Pattern.compile("rtg|devicedId|info|history|realData|gatewayBRealData");
        Matcher matcher = pattern.matcher(params);
        String suffix = null;
        // 正常情况下只会匹配一次
        if (matcher.find()) {
            suffix = matcher.group();
        }
        return suffix;
    }

    /**
     * 格式化对象信息
     *
     * @param obj
     * @param params
     * @return
     */
    public static String filterField(Object obj, String[] params) {
        if (Objects.isNull(params) || params.length < 1) {
            return JSON.toJSONString(obj);
        }
        SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
        Arrays.stream(params).forEach(param -> filter.getExcludes().add(param));
        SerializeFilter[] filters = {filter};
        return JSON.toJSONString(obj, filters);
    }
}
