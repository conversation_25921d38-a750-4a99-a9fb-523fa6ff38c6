package org.jeecg.modules.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.grepw.client.EnDecrypt;
import com.grepw.client.SystemCertificate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.RabbitMqConstants;
import org.jeecg.common.constant.RedisKeyConstants;
import org.jeecg.common.constant.StrPool;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.BusinessExceptionsUtil;
import org.jeecg.common.util.RedisUtils;
import org.jeecg.common.util.ThrowableUtils;
import org.jeecg.modules.common.config.EmPrefixProperties;
import org.jeecg.modules.common.dto.*;
import org.jeecg.modules.common.entity.EmGatewayData;
import org.jeecg.modules.common.entity.EmSecuritySensor;
import org.jeecg.modules.common.entity.EmSensor;
import org.jeecg.modules.common.entity.EmSensorOriginData;
import org.jeecg.modules.common.service.EmDealDataService;
import org.jeecg.modules.common.service.EmGatewayDataService;
import org.jeecg.modules.common.service.EmSecuritySensorService;
import org.jeecg.modules.common.service.EmSensorService;
import org.jeecg.modules.common.utils.GatewayUtil;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * BN数据处理-Service实现
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Slf4j
@Service
@ConditionalOnProperty(prefix = "jeecg", name = "platform", havingValue = "bn", matchIfMissing = true)
public class EmBnDealDataServiceImpl implements EmDealDataService {

    @Value("${data.dataType.isEmData:true}")
    private Boolean isEmData;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private EmSensorService emSensorService;
    @Autowired
    private EmPrefixProperties emPrefixProperties;
    @Autowired
    private EmGatewayDataService emGatewayDataService;
    @Autowired
    private EmSecuritySensorService emSecuritySensorService;

    @Override
    public void saveData(String topic, String content) {
        // 截取网关编号
        String deviceSn = GatewayUtil.getDeviceSn(topic);
        EmSecuritySensor emSecuritySensor = emSecuritySensorService.getOneByDeviceAddr(deviceSn);
        if (Objects.isNull(emSecuritySensor)) {
            log.warn("网关 → {}，不存在！", deviceSn);
            return;
        }
        String decryptData;
        if (Boolean.TRUE.equals(emSecuritySensor.getNeedDecrypt())) {
            decryptData = securityDecrypt(emSecuritySensor.getAccessId(), emSecuritySensor.getAccessKey(), content);
            log.info("解密完数据为：{}", decryptData);
        } else {
            decryptData = content;
        }
        // 数据转换对象
        String taskStr = redisUtils.strGet(CharSequenceUtil.format(RedisKeyConstants.TaskDi.MQ_TASK_DI_KEY, "CIMC0001"));
        TaskDTO taskDTO = JSON.parseObject(taskStr, TaskDTO.class);
        BusinessExceptionsUtil.objectIsEmptyToExceptions(taskDTO, "数据转换对象为空，请联系管理员！");
        // 数据转换对象明细
        Map<String, TaskDiDTO> taskDiDTOMap = taskDTO.getDiList().stream().collect(Collectors.toMap(TaskDiDTO::getCode, Function.identity()));
        // 转换解密数据
        DecryptDataDTO decryptDataDTO = JSON.parseObject(decryptData, DecryptDataDTO.class);
        if (Objects.nonNull(decryptDataDTO)) {
            DevDataDTO devDataDTO = decryptDataDTO.getDevDataDTOList().get(0);
            // 设备编号
            String deviceAddr = deviceSn + StrPool.UNDER_LINE + devDataDTO.getSysId();
            // 原始数据集合
            List<EmAuthSensorOriginDataDTO> emAuthSensorOriginDataDTOList = devDataDTO.getEmAuthSensorOriginDataDTOList();
            // 初始原始数据
            List<EmSensorOriginData> emSensorOriginDataList = new ArrayList<>();
            if (CollUtil.isNotEmpty(emAuthSensorOriginDataDTOList)) {
                emAuthSensorOriginDataDTOList.forEach(data -> {
                    if (taskDiDTOMap.containsKey(data.getDi())) {
                        TaskDiDTO taskDiDTO = taskDiDTOMap.get(data.getDi());
                        emSensorOriginDataList.add(new EmSensorOriginData(deviceAddr, DateUtil.formatDateTime(data.getDataTime()), taskDiDTO.getDi(), taskDiDTO.getName(), data.getValue(), 0));
                    }
                });
            }
            // 网关数据
            EmGatewayData gatewayData = new EmGatewayData(null, DateUtil.formatDateTime(new Date()), deviceSn, deviceSn, taskDTO.getTaskDi(), taskDTO.getTaskName(), JSON.toJSONString(emSensorOriginDataList));
            // 持久化网关数据和传感器原始数据
            List<EmSensorOriginData> sensorOriginDataList = emGatewayDataService.insert(gatewayData);
            if (CollUtil.isNotEmpty(sensorOriginDataList)) {
                log.info("发送传感器数据到MQ → {}", GatewayUtil.filterField(gatewayData, new String[]{"createBy", "deleted", "updateBy", "updateTime", "createTime", "taskName", "dataDate", "jcdId"}));
                rabbitTemplate.convertAndSend(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.DIRECT_EXCHANGE, RabbitMqConstants.SENSOR_DATA_QUEUE, JSON.toJSONString(sensorOriginDataList));
            }
        }
    }

    @Override
    public void saveDeviceId(String topic, String content) {
        JSONObject jsonObject = JSON.parseObject(content, JSONObject.class);
        emSecuritySensorService.setAccessId(GatewayUtil.getDeviceSn(topic), jsonObject.getString("deviceId"));
    }

    @Override
    public String securityDecrypt(String accessId, String accessKey, String data) {
        AuthResultDTO authResultDTO;
        // 解密失败后重新获取秘钥再解密一次
        try {
            authResultDTO = JSON.parseObject(EnDecrypt.securityDecrypt(accessKey, data), AuthResultDTO.class);
            if (HttpStatus.HTTP_OK != authResultDTO.getCode()) {
                log.error("解密失败，返回：{}", authResultDTO.getMsg());
                String key = getSecretkey(accessId);
                authResultDTO = JSON.parseObject(EnDecrypt.securityDecrypt(key, data), AuthResultDTO.class);
            }
        } catch (Exception e) {
            log.error(ThrowableUtils.getStackTrace(e));
            String key = getSecretkey(accessId);
            authResultDTO = JSON.parseObject(EnDecrypt.securityDecrypt(key, data), AuthResultDTO.class);
        }
        return authResultDTO.getData();
    }

    @Override
    public String getSecretkey(String accessId) {
        List<Map<String, String>> keyList = new ArrayList<>(1);
        Map<String, String> kinfo = new HashMap<>(2);
        kinfo.put("accessId", accessId);
        kinfo.put("token", null);
        keyList.add(kinfo);
        String result = SystemCertificate.getDialogKeys(keyList);
        log.info(result);
        JSONObject jsonObject = JSON.parseObject(result);
        JSONArray jsonArray = jsonObject.getJSONArray("data");
        List<DeviceSecretKeyDTO> secretKeyDTOList = JSON.parseArray(jsonArray.toJSONString(), DeviceSecretKeyDTO.class);
        log.info(secretKeyDTOList.toString());
        String accessKey = null;
        if (CollUtil.isNotEmpty(secretKeyDTOList)) {
            accessKey = secretKeyDTOList.get(0).getKey();
            // 更新
            emSecuritySensorService.updateKeyByAccessId(accessId, accessKey);
        }
        return accessKey;
    }

    @Override
    public void decryptData(String topic, String content) {
        // 截取网关编号
        String deviceSn = GatewayUtil.getDeviceSn(topic);
        EmSecuritySensor emSecuritySensor = emSecuritySensorService.getOneByDeviceAddr(deviceSn);
        if (Objects.isNull(emSecuritySensor)) {
            log.warn("网关 → {}，不存在！", deviceSn);
            return;
        }
        String decryptData;
        if (Boolean.TRUE.equals(emSecuritySensor.getNeedDecrypt())) {
            decryptData = securityDecrypt(emSecuritySensor.getAccessId(), emSecuritySensor.getAccessKey(), content);
            log.warn("解密完数据为：{}", decryptData);
        }
    }

    @Override
    public String encryptData(String deviceSn, String content) {
        EmSecuritySensor emSecuritySensor = emSecuritySensorService.getOneByDeviceAddr(deviceSn);
        if (Objects.isNull(emSecuritySensor)) {
            log.error("网关 → {}，不存在！", deviceSn);
            return null;
        }
        String encryptData;
        if (Boolean.TRUE.equals(emSecuritySensor.getNeedDecrypt())) {
            encryptData = securityEncrypt(emSecuritySensor.getAccessId(), emSecuritySensor.getAccessKey(), content);
            log.info("加密完数据为：{}", encryptData);
        } else {
            encryptData = content;
        }
        return encryptData;
    }

    @Override
    public void saveInfraredData(String content) {
        log.info("消费红外消息 → " + content);
        EmExGatewayDataDTO exGatewayData = JSON.parseObject(content, EmExGatewayDataDTO.class);
        // 网关数据
        EmGatewayData gatewayData = new EmGatewayData();
        gatewayData.setSendTime(DateUtil.formatDateTime(exGatewayData.getSendTime()));
        gatewayData.setJcdId(exGatewayData.getJcdId());
        gatewayData.setTaskDi(exGatewayData.getTaskDi());
        gatewayData.setTaskName(exGatewayData.getTaskName());
        gatewayData.setDeviceAddr(exGatewayData.getDeviceAddr());
        gatewayData.setDataContent(exGatewayData.getDatas());
        // 特殊处理红外网关数据
        emGatewayDataService.specialDealInfraredGatewayCode(gatewayData);
        List<EmSensorOriginData> sensorOriginDataList = emGatewayDataService.insert(gatewayData);
        log.info("发送红外数据 → {}", JSON.toJSONString(sensorOriginDataList));
        // 投递消息到RabbitMQ
        rabbitTemplate.convertAndSend(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.DIRECT_EXCHANGE, RabbitMqConstants.SENSOR_DATA_QUEUE, JSON.toJSONString(sensorOriginDataList));
    }

    @Override
    public void saveGatewayBRealData(String topic, String content) {
        // 创建 JSONObject
        JSONObject jsonObject = JSON.parseObject(content, JSONObject.class);
        if (!jsonObject.containsKey("data")) {
            log.error("网关B数据非实时数据或格式错误：{}", content);
            return;
        }
        // 是否中集数据，中集数据用设备编号拼_0，否则用网关编号拼设备编号
        EmGatewayBDataDTO emGatewayBDataDTO = JSON.parseObject(content, EmGatewayBDataDTO.class);
        String deviceAddr = emGatewayBDataDTO.getDeviceAddr();
        String dateTime = DateUtil.formatDateTime(emGatewayBDataDTO.getSendTime());
        // 数据转换对象
        String taskStr = redisUtils.strGet(CharSequenceUtil.format(RedisKeyConstants.TaskDi.MQ_TASK_DI_KEY, "gatewayBData"));
        TaskDTO taskDTO = JSON.parseObject(taskStr, TaskDTO.class);
        BusinessExceptionsUtil.objectIsEmptyToExceptions(taskDTO, "数据转换对象为空，请联系管理员！");
        List<EmSensorOriginData> emSensorOriginDataList = new ArrayList<>();
        JSONObject dataJson = JSON.parseObject(emGatewayBDataDTO.getData());
        dataJson.keySet().stream()
                .filter(key -> key.startsWith("pn"))
                .forEach(key -> {
                    JSONArray pnArray = dataJson.getJSONArray(key);
                    Map<String, String> pnDataMap = new HashMap<>();
                    for (int i = 0; i < pnArray.size(); i++) {
                        JSONObject item = pnArray.getJSONObject(i);
                        pnDataMap.put(item.getString("id"), item.getString("value"));
                    }
                    String deviceId = pnDataMap.get("device-id");
                    if (StringUtils.isBlank(deviceId)) {
                        log.error("网关B数据中缺少设备编号！");
                        return;
                    }
                    String replace = getDeviceId(deviceAddr, deviceId);
                    taskDTO.getDiList().forEach(diDTO -> Optional.ofNullable(pnDataMap.get(diDTO.getCode()))
                            .ifPresent(pnDataValue -> emSensorOriginDataList.add(new EmSensorOriginData(replace, dateTime, diDTO.getDi(), diDTO.getName(), pnDataValue, 0))));
                });
        // 网关数据
        EmGatewayData gatewayData = new EmGatewayData(null, dateTime, deviceAddr, deviceAddr, taskDTO.getTaskDi(), taskDTO.getTaskName(), JSON.toJSONString(emSensorOriginDataList));
        // 持久化网关数据和传感器原始数据
        List<EmSensorOriginData> sensorOriginDataList = emGatewayDataService.insert(gatewayData);
        if (CollUtil.isNotEmpty(sensorOriginDataList)) {
            // 中集交换机没有前缀
            String exchange = Boolean.TRUE.equals(isEmData) ? RabbitMqConstants.DIRECT_EXCHANGE : emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.DIRECT_EXCHANGE;
            rabbitTemplate.convertAndSend(exchange, RabbitMqConstants.SENSOR_DATA_QUEUE, JSON.toJSONString(sensorOriginDataList));
        }
    }

    /**
     * 网关b数据获取设备编号
     *
     * @param deviceAddr 网关编号
     * @param deviceId   原始设备编号
     * @return 设备编号
     */
    private String getDeviceId(String deviceAddr, String deviceId) {
        String returnStr = deviceId.replace("0x", StrPool.EMPTY);
        if (Boolean.TRUE.equals(isEmData)) {
            returnStr = "12" + returnStr;
            // 根据sn查询设备地址
            EmSensor emSensor = emSensorService.getBySn("12" + returnStr);
            if (Objects.isNull(emSensor)) {
                throw new JeecgBootException("sn:" + returnStr + ",查询不到传感器编号！");
            }
            return emSensor.getCode();
        } else {
            // 网关编号 加 设备编号
            returnStr = deviceAddr + StrPool.UNDER_LINE + returnStr;
        }
        return returnStr;
    }

    /**
     * 加密数据
     *
     * @param accessId
     * @param accessKey
     * @param data
     * @return
     */
    private String securityEncrypt(String accessId, String accessKey, String data) {
        AuthResultDTO authResultDTO;
        // 解密失败后重新获取秘钥再解密一次
        try {
            authResultDTO = JSON.parseObject(EnDecrypt.securityEncrypt(accessKey, data), AuthResultDTO.class);
            if (HttpStatus.HTTP_OK != authResultDTO.getCode()) {
                log.error("加密失败，返回：{}", authResultDTO.getMsg());
                String key = getSecretkey(accessId);
                authResultDTO = JSON.parseObject(EnDecrypt.securityEncrypt(key, data), AuthResultDTO.class);
            }
        } catch (Exception e) {
            log.error(ThrowableUtils.getStackTrace(e));
            String key = getSecretkey(accessId);
            authResultDTO = JSON.parseObject(EnDecrypt.securityEncrypt(key, data), AuthResultDTO.class);
        }
        return authResultDTO.getData();
    }
}
