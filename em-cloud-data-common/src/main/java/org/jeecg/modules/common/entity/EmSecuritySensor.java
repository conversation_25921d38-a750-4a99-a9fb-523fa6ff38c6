package org.jeecg.modules.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.jeecg.common.system.base.entity.SysBaseEntity;

/**
 * 能源监测-中电安全-设备
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("em_security_sensor")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "em_security_sensor对象", description = "能源监测-数据处理-设备")
public class EmSecuritySensor extends SysBaseEntity {
    private static final long serialVersionUID = 8835818876259024989L;
    /**
     * 网关名称
     */
    private String name;
    /**
     * 终端地址
     */
    @ApiModelProperty(value = "终端地址")
    private String deviceAddr;
    /**
     * 设备准入ID
     */
    @ApiModelProperty(value = "accessId")
    private String accessId;
    /**
     * 设备密钥
     */
    @ApiModelProperty(value = "accessKey")
    private String accessKey;
    /**
     * 状态：0-无效，1-有效
     */
    @ApiModelProperty(value = "状态：0-无效，1-有效")
    private Boolean status;
    /**
     * 是否删除：0-否，1-是
     */
    @ApiModelProperty(value = "是否删除：0-否，1-是")
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;
    /**
     * 需要解密：0否 1是
     */
    @ApiModelProperty(value = "需要解密：0否 1是")
    private Boolean needDecrypt;
    /**
     * 供应商资产编号
     */
    @ApiModelProperty(value = "供应商资产编号")
    private String supplierCode;

    public EmSecuritySensor(String id, String name, String deviceAddr, Boolean needDecrypt) {
        this.id = id;
        this.name = name;
        this.deviceAddr = deviceAddr;
        this.needDecrypt = needDecrypt;
    }

}
