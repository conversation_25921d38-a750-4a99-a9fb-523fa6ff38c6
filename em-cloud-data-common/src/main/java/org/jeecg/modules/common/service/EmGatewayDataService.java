package org.jeecg.modules.common.service;

import org.jeecg.modules.common.entity.EmGatewayData;
import org.jeecg.modules.common.entity.EmSensorOriginData;

import java.util.List;

/**
 * 能源监测-网关数据Service
 *
 * <AUTHOR>
 * @date 2022-04-24 14:32
 */
public interface EmGatewayDataService {
    /**
     * 新增
     *
     * @param gatewayData
     * @return
     */
    List<EmSensorOriginData> insert(EmGatewayData gatewayData);

    /**
     * 重新发送数据
     *
     * @param deviceAddr
     * @param sdt
     * @param edt
     */
    void reSendData(String deviceAddr, String sdt, String edt);

    /**
     * 批量重新发送数据
     *
     * @param deviceAddrList
     * @param sdt
     * @param edt
     */
    void reSendData(List<String> deviceAddrList, String sdt, String edt);

    /**
     * 批量重新发送mqtt数据
     *
     * @param sdt
     * @param edt
     */
    void reSendData(String sdt, String edt);

    /**
     * 批量重新发送红外数据
     *
     * @param deviceAddrList
     * @param sdt
     * @param edt
     */
    void batchReSendInfraredData(List<String> deviceAddrList, String sdt, String edt);

    /**
     * 特殊处理红外网关数据
     *
     * @param emGatewayData 网关数据
     */
    void specialDealInfraredGatewayCode(EmGatewayData emGatewayData);
}
