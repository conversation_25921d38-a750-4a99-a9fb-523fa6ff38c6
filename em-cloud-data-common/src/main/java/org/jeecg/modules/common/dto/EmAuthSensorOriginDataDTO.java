package org.jeecg.modules.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.jeecg.modules.common.config.FastJsonLocalDateTimeDeserializer;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备数据信息DTO
 *
 * <AUTHOR>
 * @date 2023-09-15 18:21
 */
@Data
public class EmAuthSensorOriginDataDTO implements Serializable {
    private static final long serialVersionUID = -7861939644592120974L;
    /**
     * di
     */
    @JSONField(name = "m")
    private String di;
    /**
     * 数据值
     */
    @JSONField(name = "v")
    private String value;
    /**
     * 数据时间
     */
    @JSONField(name = "ts", format = "yyyy-MM-dd HH:mm:ss", deserializeUsing = FastJsonLocalDateTimeDeserializer.class)
    private Date dataTime;
}
