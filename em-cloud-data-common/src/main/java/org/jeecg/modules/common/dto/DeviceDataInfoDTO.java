package org.jeecg.modules.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 设备数据信息DTO
 *
 * <AUTHOR>
 * @date 2023-09-15 18:21
 */
@Data
public class DeviceDataInfoDTO implements Serializable {
    private static final long serialVersionUID = -1443664937973392505L;
    /**
     * 指标值
     */
    private BigDecimal v;
    /**
     * 指标名称
     */
    private String m;
    /**
     * 数据上报时间
     */
    private Integer ts;
    /**
     *   数据质量标识， 值为 0 时表示当前值有效，非 0 时表示当前值无效（值有效 是 dq 可缺省，值无效时 dq 不可缺省）
     */
    private Integer dq;
}
