package org.jeecg.modules.common.config;

import cn.hutool.http.HttpUtil;
import com.grepw.client.SystemCertificate;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.StrPool;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 注册
 *
 * <AUTHOR>
 * @date 2023-09-18 18:17
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "jeecg", name = "platform", havingValue = "bn", matchIfMissing = true)
public class EmAuthRunner implements ApplicationRunner {
    /**
     * 业务系统授权文件路径
     */
    @Value(value = "${auth.systemKeyPath}")
    private String systemKeyPath;
    /**
     * 安全管理中心公钥
     */
    @Value(value = "${auth.smcKeyPath}")
    private String smcKeyPath;
    /**
     * 业务系统准入Id
     */
    @Value(value = "${auth.systemId}")
    private String systemId;
    /**
     * 安全管理中心访问域名或IP
     */
    @Value(value = "${auth.serverUrl}")
    private String serverUrl;

    @Override
    public void run(ApplicationArguments args) {
        authentication();
    }

    /**
     * 项目启动初始化
     */
    private void authentication() {
        // 解析授权文件, 参数：业务系统授权文件路径, 安全管理中心公钥, 业务系统准入Id, 安全管理中心访问域名或IP
        String re = SystemCertificate.create(getFileUrl(systemKeyPath), getFileUrl(smcKeyPath), systemId, serverUrl);
        log.info("解析授权文件:{}", re);
        // 平台认证
        String result = SystemCertificate.certificate();
        log.info("平台认证:{}", result);
    }

    /**
     * 获取文件路径,每次下载最新
     *
     * @param ossUrl
     * @return
     */
    private String getFileUrl(String ossUrl) {
        String fileName = ossUrl.substring(ossUrl.lastIndexOf(StrPool.SLASH) + 1);
        String returnUrl = "/home/<USER>/datas/" + fileName;
        Path path = Paths.get(returnUrl);
        if (!Files.exists(path)) {
            HttpUtil.downloadFile(ossUrl, returnUrl);
        }
        return returnUrl;
    }
}
