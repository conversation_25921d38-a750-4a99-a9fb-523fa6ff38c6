package org.jeecg.modules.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * mqtt消息
 *
 * <AUTHOR>
 * @date 2024-01-09
 */
@Data
public class NotifyDataDTO implements Serializable {
    private static final long serialVersionUID = -7597598328052175029L;
    /**
     * 头
     */
    @JSONField(name = "header")
    private HeaderDTO headerDTO;
    /**
     * 体
     */
    @JSONField(name = "body")
    private BodyDTO bodyDTO;
}
