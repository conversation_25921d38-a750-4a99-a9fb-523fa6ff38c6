package org.jeecg.modules.common.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.SysConstants;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.common.mapper.QuartzJobMapper;
import org.jeecg.modules.common.entity.SysQuartzJob;
import org.jeecg.modules.common.service.QuartzJobService;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 定时任务在线管理
 *
 * <AUTHOR>
 * @version V1.1
 * @date 2019-04-28
 */
@Slf4j
@Service
public class QuartzJobServiceImpl extends ServiceImpl<QuartzJobMapper, SysQuartzJob> implements QuartzJobService {
    /**
     * 立即执行的任务分组
     */
    private static final String JOB_TEST_GROUP = "test_group";

    @Autowired
    private QuartzJobMapper quartzJobMapper;

    @Autowired
    private Scheduler scheduler;

    private static Job getClass(String classname) throws Exception {
        Class<?> class1 = Class.forName(classname);
        return (Job) class1.newInstance();
    }

    @Override
    public List<SysQuartzJob> findByJobClassName(String jobClassName) {
        return quartzJobMapper.findByJobClassName(jobClassName);
    }

    /**
     * 保存&启动定时任务
     */
    @Override
    @Transactional(rollbackFor = JeecgBootException.class)
    public boolean saveAndScheduleJob(SysQuartzJob sysQuartzJob) {
        // DB设置修改
        sysQuartzJob.setDeleted(SysConstants.IsStatus.NO);
        boolean success = this.save(sysQuartzJob);
        if (success) {
            if (SysConstants.IsStatus.YES.equals(sysQuartzJob.getStatus())) {
                // 定时器添加
                this.schedulerAdd(sysQuartzJob.getId(), sysQuartzJob.getJobClassName().trim(), sysQuartzJob.getCronExpression().trim(), sysQuartzJob.getParameter());
            }
        }
        return success;
    }

    /**
     * 恢复定时任务
     */
    @Override
    @Transactional(rollbackFor = JeecgBootException.class)
    public boolean resumeJob(SysQuartzJob sysQuartzJob) {
        schedulerDelete(sysQuartzJob.getId());
        schedulerAdd(sysQuartzJob.getId(), sysQuartzJob.getJobClassName().trim(), sysQuartzJob.getCronExpression().trim(), sysQuartzJob.getParameter());
        sysQuartzJob.setStatus(SysConstants.IsStatus.YES);
        return this.updateById(sysQuartzJob);
    }

    /**
     * 编辑&启停定时任务
     *
     * @throws SchedulerException
     */
    @Override
    @Transactional(rollbackFor = JeecgBootException.class)
    public boolean editAndScheduleJob(SysQuartzJob sysQuartzJob) throws SchedulerException {
        if (SysConstants.IsStatus.YES.equals(sysQuartzJob.getStatus())) {
            schedulerDelete(sysQuartzJob.getId());
            schedulerAdd(sysQuartzJob.getId(), sysQuartzJob.getJobClassName().trim(), sysQuartzJob.getCronExpression().trim(), sysQuartzJob.getParameter());
        } else {
            scheduler.pauseJob(JobKey.jobKey(sysQuartzJob.getId()));
        }
        return this.updateById(sysQuartzJob);
    }

    /**
     * 删除&停止删除定时任务
     */
    @Override
    @Transactional(rollbackFor = JeecgBootException.class)
    public boolean deleteAndStopJob(SysQuartzJob job) {
        schedulerDelete(job.getId());
        return this.removeById(job.getId());
    }

    @Override
    public void execute(SysQuartzJob sysQuartzJob) throws Exception {
        String jobName = sysQuartzJob.getJobClassName().trim();
        Date startDate = new Date();
        String ymd = DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN);
        String identity = jobName + ymd;
        //3秒后执行 只执行一次
        // update-begin--author:sunjianlei ---- date:20210511--- for：定时任务立即执行，延迟3秒改成0.1秒-------
        startDate.setTime(startDate.getTime() + 100L);
        // update-end--author:sunjianlei ---- date:20210511--- for：定时任务立即执行，延迟3秒改成0.1秒-------
        // 定义一个Trigger
        SimpleTrigger trigger = (SimpleTrigger) TriggerBuilder.newTrigger()
                .withIdentity(identity, JOB_TEST_GROUP)
                .startAt(startDate)
                .build();
        // 构建job信息
        JobDetail jobDetail = JobBuilder.newJob(getClass(jobName).getClass()).withIdentity(identity).usingJobData("parameter", sysQuartzJob.getParameter()).build();
        // 将trigger和 jobDetail 加入这个调度
        scheduler.scheduleJob(jobDetail, trigger);
        // 启动scheduler
        scheduler.start();
    }

    @Override
    @Transactional(rollbackFor = JeecgBootException.class)
    public void pause(SysQuartzJob sysQuartzJob) {
        schedulerDelete(sysQuartzJob.getId());
        sysQuartzJob.setStatus(SysConstants.IsStatus.YES);
        this.updateById(sysQuartzJob);
    }

    /**
     * 添加定时任务
     *
     * @param jobClassName
     * @param cronExpression
     * @param parameter
     */
    private void schedulerAdd(String id, String jobClassName, String cronExpression, String parameter) {
        try {
            // 启动调度器
            scheduler.start();

            // 构建job信息
            JobDetail jobDetail = JobBuilder.newJob(getClass(jobClassName).getClass()).withIdentity(id).usingJobData("parameter", parameter).build();

            // 表达式调度构建器(即任务执行的时间)
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cronExpression);

            // 按新的cronExpression表达式构建一个新的trigger
            CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity(id).withSchedule(scheduleBuilder).build();

            scheduler.scheduleJob(jobDetail, trigger);
        } catch (SchedulerException e) {
            throw new JeecgBootException("创建定时任务失败", e);
        } catch (RuntimeException e) {
            throw new JeecgBootException(e.getMessage(), e);
        } catch (Exception e) {
            throw new JeecgBootException("后台找不到该类名：" + jobClassName, e);
        }
    }

    /**
     * 删除定时任务
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void schedulerDelete(String id) {
        try {
            scheduler.pauseTrigger(TriggerKey.triggerKey(id));
            scheduler.unscheduleJob(TriggerKey.triggerKey(id));
            scheduler.deleteJob(JobKey.jobKey(id));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new JeecgBootException("删除定时任务失败");
        }
    }
}
