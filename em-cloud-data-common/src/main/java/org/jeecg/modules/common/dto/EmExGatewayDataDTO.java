package org.jeecg.modules.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 前置机网关数据
 *
 * <AUTHOR>
 * @date 2022-04-24 10:48
 */
@Data
public class EmExGatewayDataDTO implements Serializable {
    private static final long serialVersionUID = 8404429107628463392L;
    /**
     * 发送时间
     */
    @JSONField(name = "send_time", format = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;
    /**
     * 前置机终端地址
     */
    @JSONField(name = "device_addr")
    private String deviceAddr;
    /**
     * 前置机jcd_id
     */
    @JSONField(name = "jcd_id")
    private String jcdId;
    /**
     * 任务标识
     */
    @JSONField(name = "task_di")
    private String taskDi;
    /**
     * 任务名称
     */
    @JSONField(name = "task_name")
    private String taskName;
    /**
     * 接收数据
     */
    private String datas;
}
