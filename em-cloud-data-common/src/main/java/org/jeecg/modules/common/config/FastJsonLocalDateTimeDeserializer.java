package org.jeecg.modules.common.config;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONToken;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;
import java.util.Date;

/**
 * 时间戳秒处理
 *
 * <AUTHOR>
 * @date 2023-09-21 15:40
 */
public class FastJsonLocalDateTimeDeserializer implements ObjectDeserializer {

    @Override
    public Date deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        long input = parser.lexer.longValue();
        if (input < 10000000000L) {
            input = input * 1000;
        }
        return DateUtil.date(input);
    }

    @Override
    public int getFastMatchToken() {
        return JSONToken.LITERAL_INT;
    }
}
