package org.jeecg.modules.common.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 获取设备密钥DTO
 *
 * <AUTHOR>
 * @date 2023-09-15 18:21
 */
@Data
public class DeviceSecretKeyDTO implements Serializable {
    private static final long serialVersionUID = -3409136603253820825L;
    /**
     * 设备认证后的ID
     */
    private String accessId;
    /**
     * accessKey
     */
    private String key;
    /**
     * 有效时长
     */
    private Long validDuration;
}
