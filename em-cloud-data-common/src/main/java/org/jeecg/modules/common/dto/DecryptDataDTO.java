package org.jeecg.modules.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 解密后数据
 *
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
public class DecryptDataDTO implements Serializable {
    private static final long serialVersionUID = -7597598328052175029L;
    /**
     * 版本
     */
    private String ver;
    /**
     * pKey
     */
    @JSONField(name = "pKey")
    private String pKey;
    /**
     * 网关id
     */
    private String sn;
    /**
     * 时间戳
     */
    private long ts;
    /**
     * 体
     */
    @JSONField(name = "devs")
    private List<DevDataDTO> devDataDTOList;
}
