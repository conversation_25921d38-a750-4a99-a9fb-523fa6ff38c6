package org.jeecg.modules.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * mqtt消息
 *
 * <AUTHOR>
 * @date 2024-01-09
 */
@Data
public class PayloadDTO implements Serializable {
    private static final long serialVersionUID = -7597598328052175029L;
    /**
     * 来源
     */
    private String resource;
    /**
     * 事件类型
     */
    private String event;
    /**
     * 事件时间
     */
    @JSONField(name = "event_time")
    private String eventTime;
    /**
     * 事件时间
     */
    @JSONField(name = "event_time_ms")
    private String eventTimeMs;
    /**
     * 请求id
     */
    @JSONField(name = "request_id")
    private String requestId;
    /**
     * notify_data
     */
    @JSONField(name = "notify_data")
    private NotifyDataDTO notifyData;
}
