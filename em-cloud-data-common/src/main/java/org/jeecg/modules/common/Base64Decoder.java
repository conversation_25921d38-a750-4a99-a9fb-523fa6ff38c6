package org.jeecg.modules.common;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.asymmetric.SM2;
import org.bouncycastle.crypto.engines.SM2Engine;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class Base64Decoder {
    private static final Map<String, String> algorithmMethod = new HashMap<>();

    static {
        algorithmMethod.put("101", "SM1算法ECB加密模式");
        algorithmMethod.put("102", "SM1算法CBC加密模式");
        algorithmMethod.put("104", "SM1算法CFB加密模式");
        algorithmMethod.put("108", "SM1算法OFB加密模式");
        algorithmMethod.put("110", "SM1算法MAC运算");
        algorithmMethod.put("201", "SSF33算法ECB加密模式");
        algorithmMethod.put("202", "SSF33算法CBC加密模式");
        algorithmMethod.put("204", "SSF33算法CFB加密模式");
        algorithmMethod.put("208", "SSF33算法OFB加密模式");
        algorithmMethod.put("210", "SSF33算法MAC运算");
        algorithmMethod.put("401", "SM4算法ECB加密模式");
        algorithmMethod.put("402", "SM4算法CBC加密模式");
        algorithmMethod.put("404", "SM4算法CFB加密模式");
        algorithmMethod.put("408", "SM4算法OFB加密模式");
        algorithmMethod.put("410", "SM4算法MAC运算");
        algorithmMethod.put("801", "ZUC祖冲之机密性算法128-EEA3算法");
        algorithmMethod.put("802", "ZUC祖冲之完整性算法128-EEA3算法");
    }

    public static void main(String[] args) {
        /*if (args.length < 1) {
            System.out.println("使用示例：java Base64Decoder 数字信封密文数据文件路径");
            System.exit(1);
        }
*/
        String filePath = "/Users/<USER>/Desktop/SSL/device_enc.key";
        String data = "";

        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            data = br.readLine();
        } catch (IOException e) {
            System.err.println(e.getMessage());
            System.exit(1);
        }

        System.out.println("原始数据(数字信封,经过base64编码)： " + data);
        System.out.println();

        String decodedString = HexUtil.encodeHexStr(Base64.decode(data)); // 解码后的字符串
        System.out.println("将base64编码转成二进制： " + decodedString);
        System.out.println();

        String algId = decodedString.substring(8, 8 + 8);
        int val = Integer.parseUnsignedInt(algId, 16);
        int swappedVal = swap32(val);
        System.out.println("提取的算法标识： " + algorithmMethod.get(Integer.toHexString(swappedVal)));
        System.out.println();

        String privKeyData = decodedString.substring(3 * 8, 3 * 8 + 2 * 64);
        if (privKeyData.startsWith(repeat("0", 64))) {
            privKeyData = privKeyData.substring(64);
        }
        System.out.println("提取的私钥密文字节数组： " + privKeyData);
        System.out.println();

        String cipher = decodedString.substring(3 * 2 * 4 + 2 * 64 + 2 * 4 + 2 * 64 * 2);
        String xCipher = cipher.substring(0, 128);
        String yCipher = cipher.substring(128, 256);
        String zaCipher = cipher.substring(256, 320);
        String lenDataCipher = cipher.substring(320, 328);
        String dataCipher = cipher.substring(328);

        // 提取对称密钥逻辑
        String newCipher = "";
        if (xCipher.startsWith(repeat("0", 64)) && yCipher.startsWith(repeat("0", 64))) {
            newCipher = xCipher.substring(64) + yCipher.substring(64) + zaCipher + dataCipher; // 确保这里的逻辑与JS一致
        } else {
            newCipher = xCipher + yCipher + zaCipher + dataCipher; // 可能的替代逻辑
        }

        System.out.println("提取的对称密钥密文： " + newCipher);

        // 添加PEM私钥文件路径
        String pemPath = "/Users/<USER>/Desktop/SSL/private_key.pem";

        try {
            // 读取PEM私钥
            String privateKeyBase64 = readPrivateKeyFromPem(pemPath);
            byte[] privateKeyBytes = Base64.decode(privateKeyBase64);

            // 创建SM2解密器
            SM2 sm2 = new SM2(privateKeyBytes, null);
            sm2.setMode(SM2Engine.Mode.C1C3C2);

            // 解密newCipher (需要先将十六进制字符串转换为字节数组)
            byte[] encryptedData = hexToByteArray(newCipher);
            byte[] decryptedData = sm2.decrypt(encryptedData);

            // 输出解密结果
            System.out.println("解密后的数据： " + new String(decryptedData));

        } catch (Exception e) {
            System.err.println("解密失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    private static String readFile(String filePath) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }

    /**
     * Hex字符串转byte数组
     * @param inHex 待转换的Hex字符串
     * @return 转换后的byte数组
     */
    public static byte[] hexToByteArray(String inHex) {
        int hexlen = inHex.length();
        byte[] result;
        if (hexlen % 2 == 1) {
            // 奇数
            hexlen++;
            result = new byte[(hexlen / 2)];
            inHex = "0" + inHex;
        } else {
            // 偶数
            result = new byte[(hexlen / 2)];
        }

        for (int i = 0; i < result.length; i++) {
            result[i] = hexToByte(inHex.substring(i * 2, i * 2 + 2));
        }
        return result;
    }

    /**
     * Hex字符串转byte
     * @param inHex 待转换的Hex字符串
     * @return 转换后的byte
     */
    public static byte hexToByte(String inHex) {
        return (byte) Integer.parseUnsignedInt(inHex, 16);
    }

    // 添加新的方法来读取PEM私钥
    private static String readPrivateKeyFromPem(String pemPath) throws IOException {
        try (BufferedReader br = new BufferedReader(new FileReader(pemPath))) {
            StringBuilder key = new StringBuilder();
            String line;
            boolean inKey = false;
            while ((line = br.readLine()) != null) {
                if (line.contains("-----BEGIN PRIVATE KEY-----") ||
                        line.contains("-----BEGIN EC PRIVATE KEY-----")) {
                    inKey = true;
                    continue;
                }
                if (line.contains("-----END PRIVATE KEY-----") ||
                        line.contains("-----END EC PRIVATE KEY-----")) {
                    break;
                }
                if (inKey) {
                    key.append(line.trim());
                }
            }
            System.out.println("私钥:" + key.toString());
            return key.toString();
        }
    }

    private static int swap32(int val) {
        return Integer.reverseBytes(val);
    }

    private static String repeat(String str, int times) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < times; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
