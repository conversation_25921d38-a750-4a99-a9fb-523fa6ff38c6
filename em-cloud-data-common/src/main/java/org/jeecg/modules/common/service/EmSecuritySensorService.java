package org.jeecg.modules.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.common.entity.EmSecuritySensor;

import java.util.List;

/**
 * 能源监测-数据处理-设备Service接口
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
public interface EmSecuritySensorService extends IService<EmSecuritySensor> {

    /**
     * 查询所有
     *
     * @return
     */
    List<EmSecuritySensor> selectAll();

    /**
     * 根据网关id获取
     *
     * @param deviceAddr
     * @return
     */
    EmSecuritySensor getOneByDeviceAddr(String deviceAddr);

    /**
     * 根据accessId获取
     *
     * @param accessId
     * @return
     */
    EmSecuritySensor getOneByAccessId(String accessId);

    /**
     * 根据accessId修改key
     *
     * @param accessId
     * @param accessKey
     * @return
     */
    void updateKeyByAccessId(String accessId, String accessKey);

    /**
     * 新增网关
     *
     * @param emSecuritySensor
     */
    void addSecuritySensor(EmSecuritySensor emSecuritySensor);

    /**
     * 根据网关地址删除
     *
     * @param deviceAddr
     */
    void removeByDeviceAddr(String deviceAddr);

    /**
     * 根据网关地址删除
     *
     * @param deviceAddrList
     */
    void removeByDeviceAddr(List<String> deviceAddrList);

    /**
     * 设备准入id赋值
     *
     * @param deviceAddr
     * @param accessId
     */
    void setAccessId(String deviceAddr, String accessId);
}
