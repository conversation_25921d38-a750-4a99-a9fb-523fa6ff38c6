package org.jeecg.modules.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.jeecg.common.aspect.annotation.SysDict;
import org.jeecg.common.system.base.entity.SysBaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 能源监测-传感器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("em_sensor")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "em_sensor对象", description = "能源监测-传感器")
public class EmSensor extends SysBaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 传感器编号
     */
    @NotEmpty(message = "传感器编号不能为空！")
    @ApiModelProperty(value = "传感器编号")
    private String code;
    /**
     * 传感器名称
     */
    @NotEmpty(message = "传感器名称不能为空！")
    @ApiModelProperty(value = "传感器名称")
    private String name;
    /**
     * 仪表表号
     */
    @ApiModelProperty(value = "仪表表号")
    private String sn;
    /**
     * 安装时间
     */
    @ApiModelProperty(value = "安装时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date installTime;
    /**
     * 监测指标模板ID
     */
    @ApiModelProperty(value = "监测指标模板ID")
    @SysDict(dictTable = "em_monitor_index_template", dictText = "name", dictCode = "id")
    private String monitorIndexTemplateId;
    /**
     * 能源类型
     */
    @NotNull(message = "能源类型不能为空！")
    @ApiModelProperty(value = "能源类型")
    private Integer energyType;
    /**
     * 传感器类型：1-智能表，2-机械表, 3-虚拟表
     */
    @NotNull(message = "传感器类型不能为空！")
    @ApiModelProperty(value = "传感器类型：1-智能表，2-机械表, 3-虚拟表")
    private Integer sensorCategory;
    /**
     * 上报频率(min)
     */
    @NotNull(message = "上报频率(min)不能为空！")
    @ApiModelProperty(value = "上报频率(min)")
    private Integer reportFreq;
    /**
     * 是否有效：0-无效，1-有效
     */
    @NotNull(message = "是否有效不能为空！")
    @ApiModelProperty(value = "是否有效：0-无效，1-有效")
    @TableField("is_valid")
    private Boolean valid;
    /**
     * 是否在线：0-离线，1-在线
     */
    @NotNull(message = "是否在线不能为空！")
    @ApiModelProperty(value = "是否在线：0-离线，1-在线")
    @TableField("is_online")
    private Boolean online;
    /**
     * 是否绑定设备：0-否，1-是
     */
    @NotNull(message = "是否绑定设备不能为空！")
    @ApiModelProperty(value = "是否绑定设备：0-否，1-是")
    @TableField("is_bind_equips")
    private Boolean bindEquips;
    /**
     * 初始化数据
     **/
    @ApiModelProperty(value = "初始化数据")
    private BigDecimal initDataVal;
    /**
     * 计量方式: 1-递增计量，2-用量计量
     **/
    @ApiModelProperty(value = "计量方式: 1-递增计量，2-用量计量")
    private Integer measureMethod;
    /**
     * 阀控是否开启：0-否，1-是
     **/
    @ApiModelProperty(value = "阀控是否开启")
    @TableField("is_valve_open")
    private Boolean valveOpen;
    /**
     * 删除状态：0-未删除，1-已删除
     */
    @NotNull(message = "删除状态不能为空！")
    @ApiModelProperty(value = "删除状态：0-未删除，1-已删除")
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;
    /**
     * 系统园区ID
     */
    @NotEmpty(message = "系统园区ID不能为空！")
    @ApiModelProperty(value = "系统园区ID")
    private String sysParkId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 能源价格策略ID
     */
    @ApiModelProperty(value = "能源价格策略ID")
    private transient String energyPriceStrategyId;
}
