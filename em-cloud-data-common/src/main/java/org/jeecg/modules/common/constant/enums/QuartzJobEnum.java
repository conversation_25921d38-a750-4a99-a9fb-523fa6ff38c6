package org.jeecg.modules.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务枚举类
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Getter
@AllArgsConstructor
public enum QuartzJobEnum {
    /**
     * 定时发送
     */
    GATEWAY_DATA_JOB("gatewayDataJob", "org.jeecg.modules.data.job.GatewayDataJob");

    /**
     * 分组名称
     */
    private final String groupName;

    /**
     * 任务类名
     */
    private final String className;
}
