package org.jeecg.modules.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jeecg.common.constant.DatabaseConstants;
import org.jeecg.common.constant.RabbitMqConstants;
import org.jeecg.common.constant.RedisKeyConstants;
import org.jeecg.common.constant.StrPool;
import org.jeecg.common.util.RedisUtils;
import org.jeecg.modules.common.config.EmPrefixProperties;
import org.jeecg.modules.common.entity.EmGatewayData;
import org.jeecg.modules.common.entity.EmSensor;
import org.jeecg.modules.common.entity.EmSensorOriginData;
import org.jeecg.modules.common.repository.EmSensorOriginDataRepository;
import org.jeecg.modules.common.service.EmSensorOriginDataService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 能源监测-传感器原始数据Service实现
 *
 * <AUTHOR>
 * @date 2022-04-24 14:37
 */
@Slf4j
@Service
public class EmSensorOriginDataServiceImpl implements EmSensorOriginDataService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private EmPrefixProperties emPrefixProperties;
    @Autowired
    private EmSensorOriginDataRepository emSensorOriginDataRepository;

    @Override
    @Transactional(value = "mongoTransactionManager", rollbackFor = Exception.class)
    public List<EmSensorOriginData> insert(EmGatewayData gatewayData) {
        // 获取传感器数据详情
        List<EmSensorOriginData> emSensorOriginDataList = JSON.parseArray(gatewayData.getDataContent(), EmSensorOriginData.class);
        // 更新列表
        List<EmSensorOriginData> updateSensorOriginDataList = new ArrayList<>(16);
        // 新增列表
        List<EmSensorOriginData> addSensorOriginDataList = new ArrayList<>(16);
        for (EmSensorOriginData emSensorOriginData : emSensorOriginDataList) {
            // 网关数据ID
            String gatewayDataId = gatewayData.getId();
            // 网关地址
            String deviceAddr = StringUtils.isNotBlank(emSensorOriginData.getDeviceAddr()) ? emSensorOriginData.getDeviceAddr() : gatewayData.getDeviceAddr();
            // pn
            Integer pn = emSensorOriginData.getPn();
            // 数据di
            String di = emSensorOriginData.getDi();
            // 数据时间
            String dataTime = emSensorOriginData.getDataTime();
            // 数据日期
            String dataDate = DateUtil.formatDate(DateUtil.parseDate(dataTime));
            EmSensorOriginData sensorOriginData = emSensorOriginDataRepository.findByDataTimeAndDeviceAddrAndDiAndPn(dataTime, deviceAddr, di, pn);
            if (Objects.nonNull(sensorOriginData)) {
                sensorOriginData.setGatewayDataId(gatewayDataId);
                sensorOriginData.setValue(emSensorOriginData.getValue());
                sensorOriginData.setName(emSensorOriginData.getName());
                sensorOriginData.setDataDate(dataDate);
                sensorOriginData.setCreateBy(DatabaseConstants.SYS_ADMIN);
                sensorOriginData.setCreateTime(DateUtil.parseDateTime(dataTime));
                sensorOriginData.setUpdateBy(DatabaseConstants.SYS_ADMIN);
                sensorOriginData.setUpdateTime(new Date());
                updateSensorOriginDataList.add(sensorOriginData);
            } else {
                // 再添加一份原始数据供操作
                emSensorOriginData.setDataDate(dataDate);
                emSensorOriginData.setGatewayDataId(gatewayDataId);
                emSensorOriginData.setDeviceAddr(deviceAddr);
                emSensorOriginData.setCreateBy(DatabaseConstants.SYS_ADMIN);
                emSensorOriginData.setCreateTime(new Date());
                addSensorOriginDataList.add(emSensorOriginData);
            }
        }
        if (CollectionUtils.isNotEmpty(addSensorOriginDataList)) {
            emSensorOriginDataRepository.insert(addSensorOriginDataList);
        }
        if (CollectionUtils.isNotEmpty(updateSensorOriginDataList)) {
            emSensorOriginDataRepository.saveAll(updateSensorOriginDataList);
        }
        addSensorOriginDataList.addAll(updateSensorOriginDataList);
        return addSensorOriginDataList;
    }


    @Override
    public List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, String di) {
        return emSensorOriginDataRepository.findByDataDateAndDeviceAddrAndDi(DateUtil.formatDate(dataDate), deviceAddr, di, Sort.by(Sort.Direction.ASC, "data_time"));
    }

    @Override
    public List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, Integer pn, String di) {
        return emSensorOriginDataRepository.findByDataDateAndDeviceAddrAndPnAndDi(DateUtil.formatDate(dataDate), deviceAddr, pn, di, Sort.by(Sort.Direction.ASC, "data_time"));
    }

    @Override
    public List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, Integer pn) {
        return emSensorOriginDataRepository.findByDataDateAndDeviceAddrAndPn(DateUtil.formatDate(dataDate), deviceAddr, pn, Sort.by(Sort.Direction.ASC, "data_time"));
    }

    @Override
    public EmSensorOriginData getNowOrNextOneBy(String deviceAddr, Date dataDate) {
        return emSensorOriginDataRepository.findNowOrNextOneBy(deviceAddr, DateUtil.formatDateTime(dataDate));
    }

    @Override
    public EmSensorOriginData getFirstOne(String deviceAddr, String di) {
        return emSensorOriginDataRepository.findFirstOne(deviceAddr, di);
    }

    @Async("asyncServiceExecutor")
    @Override
    public void reRunCIMCOriginData(String deviceAddr, Date sdt, Date edt) {
        DateTime endDateTime = DateUtil.endOfDay(edt);
        if (endDateTime.before(sdt)) {
            return;
        }
        Integer pn = 0;
        // 第一个开始时间有时分秒,第一天过滤安装时间，后面都拿整天
        deviceAddr = checkIsNullRerunData(deviceAddr, pn, null, sdt, null, null, Boolean.TRUE);
        // 开始时间加一天，从零点开始
        sdt = DateUtil.beginOfDay(DateUtils.addDays(sdt, 1));
        while (sdt.compareTo(endDateTime) <= 0) {
            deviceAddr = checkIsNullRerunData(deviceAddr, pn, null, sdt, null, null, Boolean.FALSE);
            // 开始日期加一
            sdt = DateUtils.addDays(sdt, 1);
        }
    }

    /**
     * 判断网关编号是否为空去重跑数据
     *
     * @param deviceAddr        网关编号
     * @param pn                pn
     * @param di                数据项
     * @param sdt               开始时间
     * @param dataSources       数据类型
     * @param dataFreq          数据频率
     * @param filterInstallTime 是否需要过滤安装时间
     * @return 网关编号
     */
    private String checkIsNullRerunData(String deviceAddr, Integer pn, String di, Date sdt, Integer dataSources, Integer dataFreq, boolean filterInstallTime) {
        if (StringUtils.isNotBlank(deviceAddr)) {
            checkNullToSend(deviceAddr, pn, di, sdt, dataSources, dataFreq, filterInstallTime);
        } else {
            List<EmSensor> emSensorList = redisUtils.hashGetList(RedisKeyConstants.EmSensor.SENSOR_CODE_HASH, EmSensor.class);
            for (EmSensor emSensor : emSensorList) {
                deviceAddr = StringUtils.substringBeforeLast(emSensor.getCode(), StrPool.UNDER_LINE);
                checkNullToSend(deviceAddr, pn, di, sdt, dataSources, dataFreq, filterInstallTime);
            }
            // 循环所有传感器之后，网关地址置空
            deviceAddr = null;
        }
        return deviceAddr;
    }

    /**
     * 判断参数是否为空 查询不同的数据
     *
     * @param deviceAddr        网关编号
     * @param pn                pn
     * @param di                数据项
     * @param sdt               开始时间
     * @param dataSources       数据类型
     * @param filterInstallTime 是否需要过滤安装时间
     */
    private void checkNullToSend(String deviceAddr, Integer pn, String di, Date sdt, Integer dataSources, Integer dataFreq, boolean filterInstallTime) {
        List<EmSensorOriginData> sensorOriginDataList = getSensorOriginDataList(deviceAddr, pn, di, sdt, filterInstallTime);
        if (CollUtil.isNotEmpty(sensorOriginDataList)) {
            log.info("时间 → {},获取到传感器原始数据集合大小 → {}", DateUtil.formatDate(sdt), sensorOriginDataList.size());
            // 发送数据到MQ
            this.rerunOriginSendMsg(sensorOriginDataList);
        }
    }

    /**
     * 获取原始数据
     *
     * @param deviceAddr        网关编号
     * @param pn                pn
     * @param di                数据项
     * @param sdt               开始时间
     * @param filterInstallTime 是否需要过滤安装时间
     * @return 原始数据
     */
    private List<EmSensorOriginData> getSensorOriginDataList(String deviceAddr, Integer pn, String di, Date sdt, boolean filterInstallTime) {
        List<EmSensorOriginData> sensorOriginDataList = null;
        // 根据各种情况查不同的数据
        if (Objects.isNull(pn) && StringUtils.isNotBlank(di)) {
            sensorOriginDataList = this.getList(sdt, deviceAddr, di);
        } else if (Objects.nonNull(pn) && StringUtils.isBlank(di)) {
            sensorOriginDataList = this.getList(sdt, deviceAddr, pn);
        } else if (Objects.nonNull(pn) && StringUtils.isNotBlank(di)) {
            sensorOriginDataList = this.getList(sdt, deviceAddr, pn, di);
        }
        // 不为空并且需要过滤安装时间
        if (CollUtil.isNotEmpty(sensorOriginDataList) && filterInstallTime) {
            // 过滤大于等于开始时间
            log.info("过滤大于等于开始时间, 时间：{}", DateUtil.formatDateTime(sdt));
            return sensorOriginDataList.stream().filter(origin -> DateUtil.parseDateTime(origin.getDataTime()).compareTo(sdt) >= 0).collect(Collectors.toList());
        }
        return sensorOriginDataList;
    }

    /**
     * 发送MQ消息-重发origin不同队列消息
     *
     * @param emSensorOriginDataList
     */
    private void rerunOriginSendMsg(List<EmSensorOriginData> emSensorOriginDataList) {
        emSensorOriginDataList.forEach(emSensorOriginData -> {
            // 发送数据到MQ
            rabbitTemplate.convertAndSend(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.RERUN_EXCHANGE, RabbitMqConstants.RERUN_DATA_QUEUE, JSON.toJSONString(Collections.singletonList(emSensorOriginData)));
        });
    }
}
