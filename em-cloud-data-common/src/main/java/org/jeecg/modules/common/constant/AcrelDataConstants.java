package org.jeecg.modules.common.constant;

/**
 * acrel数据constants
 *
 * <AUTHOR>
 * @date 2022-01-12
 */
public interface AcrelDataConstants {
    /**
     * acrel token
     */
    String ACREL_DATA_TOKEN = "acrel_data_token";

    /**
     * 网关code
     */
    String METER_ID = "MeterID";

    /**
     * 网关code
     */
    String METER_ID_TWO = "meterID";

    /**
     * 网关code
     */
    String STR = "str";

    /**
     * 创建时间、数据时间
     */
    String CREATE_TIME = "CreateTime";

    /**
     * 请求类型 1：get,2：post
     */
    interface RequestType {
        Integer GET = 1;
        Integer POST = 2;
    }

    /**
     * 能源类型 1：电, 2：水
     */
    interface DeviceType {
        Integer POWER = 1;
        Integer WATER = 2;
    }

}
