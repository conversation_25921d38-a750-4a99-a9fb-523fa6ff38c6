package org.jeecg.modules.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.common.entity.EmSensor;
import org.jeecg.modules.common.mapper.EmSensorMapper;
import org.jeecg.modules.common.service.EmSensorService;
import org.springframework.stereotype.Service;

/**
 * 能源监测-传感器Service实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-10
 */
@Slf4j
@Service
public class EmSensorServiceImpl extends ServiceImpl<EmSensorMapper, EmSensor> implements EmSensorService {

    @Override
    public EmSensor getBySn(String sn) {
        return this.getOne(new LambdaQueryWrapper<EmSensor>().eq(EmSensor::getSn, sn));
    }
}