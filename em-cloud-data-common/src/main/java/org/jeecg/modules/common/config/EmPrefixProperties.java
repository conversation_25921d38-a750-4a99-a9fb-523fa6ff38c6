package org.jeecg.modules.common.config;


import lombok.Setter;
import org.jeecg.common.constant.StrPool;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 配置环境rabbitMq前缀
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Setter
@Component
@ConfigurationProperties(prefix = "energy-monitor.profiles-active")
public class EmPrefixProperties {
    /**
     * 前缀
     */
    private String rabbitMqPrefix;

    public String getRabbitMqPrefix() {
        return rabbitMqPrefix + StrPool.DOT;
    }
}
