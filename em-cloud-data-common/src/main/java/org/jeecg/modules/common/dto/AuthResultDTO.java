package org.jeecg.modules.common.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 接口返回对象
 *
 * <AUTHOR>
 * @date 2023-09-21 14:56
 */
@Data
public class AuthResultDTO implements Serializable {
    private static final long serialVersionUID = -7597598328052175029L;
    /**
     * 消息
     */
    private String msg;
    /**
     * 状态码
     */
    private int code;
    /**
     * 数据
     */
    private String data;
    /**
     * 是否成功
     */
    private boolean success;
}
