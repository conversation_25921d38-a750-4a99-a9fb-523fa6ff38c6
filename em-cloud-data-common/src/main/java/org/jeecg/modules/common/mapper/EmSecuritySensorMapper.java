package org.jeecg.modules.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.common.dto.TaskDTO;
import org.jeecg.modules.common.dto.TaskDiDTO;
import org.jeecg.modules.common.entity.EmSecuritySensor;

import java.util.List;

/**
 * 能源监测-数据处理-设备Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Mapper
public interface EmSecuritySensorMapper extends BaseMapper<EmSecuritySensor> {
    /**
     * 查询所有和明细
     *
     * @return
     */
    List<TaskDTO> selectAllUpdateRedis();

    /**
     * 根据taskId查询
     *
     * @param taskId
     * @return
     */
    List<TaskDiDTO> selectByTaskId(@Param("taskId") String taskId);
}