package org.jeecg.modules.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.common.entity.EmSensor;

/**
 * 能源监测-传感器Service接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-10
 */
public interface EmSensorService extends IService<EmSensor> {

    /**
     * 根据仪表表号查询传感器信息
     *
     * @param sn
     * @return
     */
    EmSensor getBySn(String sn);
}
