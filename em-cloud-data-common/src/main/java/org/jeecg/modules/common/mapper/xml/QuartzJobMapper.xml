<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.common.mapper.QuartzJobMapper">
    <resultMap id="BaseResultMap" type="org.jeecg.modules.common.entity.SysQuartzJob">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="job_class_name" property="jobClassName" jdbcType="VARCHAR"/>
        <result column="cron_expression" property="cronExpression" jdbcType="VARCHAR"/>
        <result column="parameter" property="parameter" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="BOOLEAN"/>
        <result column="is_deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 表名称 -->
    <sql id="tname">sys_quartz_job sqj</sql>

    <!-- 列名称 -->
    <sql id="cols">sqj.id, sqj.job_class_name, sqj.cron_expression, sqj.parameter, sqj.status, sqj.is_deleted, sqj.description, sqj.create_by, sqj.create_time, sqj.update_by, sqj.update_time</sql>

    <!-- 根据jobClassName查询 -->
    <select id="findByJobClassName" resultMap="BaseResultMap">
        SELECT
            <include refid="cols"/>
        FROM <include refid="tname"/>
        WHERE job_class_name = #{jobClassName}
    </select>
</mapper>