<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.common.mapper.EmSensorMapper">
    <!-- 表名称 -->
    <sql id="tname">em_sensor es</sql>

    <!-- 字段名称 -->
    <sql id="cols">es.id, es.code, es.name, es.sn, install_time, es.monitor_index_template_id,
            es.energy_type, es.report_freq, es.is_valid, es.is_online, es.is_ignore_online, es.is_bind_equips, es.init_data_val,
            es.measure_method, es.is_valve_open, es.sys_park_id, es.sensor_category, es.remark, es.use_classify_id, es.equips_group_id,
            es.is_bind_gateway_device, es.gateway_device_id, es.is_device_itself, es.is_change_magnification, es.change_magnification_time, es.is_deleted,
            es.create_by, es.create_time, es.update_by, es.update_time</sql>

    <resultMap id="BaseResultMap" type="org.jeecg.modules.common.entity.EmSensor">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="sn" jdbcType="VARCHAR" property="sn"/>
        <result column="install_time" jdbcType="TIMESTAMP" property="installTime"/>
        <result column="monitor_index_template_id" jdbcType="VARCHAR" property="monitorIndexTemplateId"/>
        <result column="energy_type" jdbcType="INTEGER" property="energyType"/>
        <result column="energy_price_strategy_id" jdbcType="VARCHAR" property="energyPriceStrategyId"/>
        <result column="report_freq" jdbcType="INTEGER" property="reportFreq"/>
        <result column="is_valid" jdbcType="BOOLEAN" property="valid"/>
        <result column="is_online" jdbcType="BOOLEAN" property="online"/>
        <result column="is_bind_equips" jdbcType="BOOLEAN" property="bindEquips"/>
        <result column="init_data_val" jdbcType="DECIMAL" property="initDataVal" />
        <result column="measure_method" jdbcType="SMALLINT" property="measureMethod" />
        <result column="is_valve_open" jdbcType="BOOLEAN" property="valveOpen" />
        <result column="sys_park_id" jdbcType="VARCHAR" property="sysParkId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="sensor_category" jdbcType="INTEGER" property="sensorCategory"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

</mapper>