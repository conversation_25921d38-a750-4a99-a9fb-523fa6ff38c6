package org.jeecg.modules.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 网关b
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
public class EmGatewayBDataDTO implements Serializable {
    private static final long serialVersionUID = 8404429107628463392L;
    /**
     * 发送时间
     */
    @JSONField(name = "time", format = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;
    /**
     * 前置机终端地址
     */
    @JSONField(name = "sn")
    private String deviceAddr;
    /**
     * 前置机jcd_id
     */
    @JSONField(name = "machineid")
    private String jcdId;
    /**
     * 类型
     */
    private String type;
    /**
     * 任务标识
     */
    private String taskDi;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 接收数据
     */
    private String data;
}
