package org.jeecg.modules.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * mqtt消息
 *
 * <AUTHOR>
 * @date 2024-01-09
 */
@Data
public class HeaderDTO implements Serializable {
    private static final long serialVersionUID = -7597598328052175029L;
    /**
     * appId
     */
    @JSONField(name = "app_id")
    private String appId;
    /**
     * 设备
     */
    @JSONField(name = "device_id")
    private String deviceId;
    /**
     * 节点
     */
    @JSONField(name = "node_id")
    private String nodeId;
    /**
     * 产品
     */
    @JSONField(name = "product_id")
    private String productId;
    /**
     * 网关
     */
    @JSONField(name = "gateway_id")
    private String gatewayId;
}
