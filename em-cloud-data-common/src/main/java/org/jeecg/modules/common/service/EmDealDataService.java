package org.jeecg.modules.common.service;

/**
 * 数据处理-Service接口
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
public interface EmDealDataService {

    /**
     * 保存定时数据
     *
     * @param topic   主题
     * @param content 内容
     */
    void saveData(String topic, String content);

    /**
     * 保存设备准入Id,明文数据
     *
     * @param topic   主题
     * @param content 内容
     */
    void saveDeviceId(String topic, String content);

    /**
     * 解密数据
     *
     * @param accessId  设备准入ID
     * @param accessKey 设备密钥
     * @param data      需解密数据
     * @return 解密完数据
     */
    String securityDecrypt(String accessId, String accessKey, String data);

    /**
     * 获取设备秘钥
     *
     * @param accessId 设备准入ID
     * @return 密钥
     */
    String getSecretkey(String accessId);

    /**
     * 解密数据
     *
     * @param topic   主题
     * @param content 内容
     */
    void decryptData(String topic, String content);

    /**
     * 加密数据
     *
     * @param deviceSn 主题
     * @param content  内容
     */
    String encryptData(String deviceSn, String content);

    /**
     * 保存红外数据
     *
     * @param content 数据内容
     */
    void saveInfraredData(String content);

    /**
     * 网关B数据
     *
     * @param topic 主题
     * @param content 数据内容
     */
    void saveGatewayBRealData(String topic, String content);
}
