<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.common.mapper.EmSecuritySensorMapper">
    <resultMap id="BaseResultMap" type="org.jeecg.modules.common.entity.EmSecuritySensor">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="device_addr" jdbcType="VARCHAR" property="deviceAddr"/>
        <result column="access_id" jdbcType="VARCHAR" property="accessId"/>
        <result column="access_key" jdbcType="VARCHAR" property="accessKey"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="getALLAndDetailMap" type="org.jeecg.modules.common.dto.TaskDTO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="task_di" jdbcType="VARCHAR" property="taskDi"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <collection property="diList"
                    ofType="org.jeecg.modules.common.dto.TaskDiDTO"
                    select="org.jeecg.modules.common.mapper.EmSecuritySensorMapper.selectByTaskId"
                    column="id"/>
    </resultMap>

    <select id="selectAllUpdateRedis" resultMap="getALLAndDetailMap">
        SELECT
            ept.id,
            ept.task_di,
            ept.task_name
        FROM em_process_task ept
        WHERE
            ept.is_deleted = 0
    </select>

    <resultMap id="TaskDiMap" type="org.jeecg.modules.common.dto.TaskDiDTO">
        <result column="di" jdbcType="VARCHAR" property="di" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="pn" jdbcType="INTEGER" property="pn" />
    </resultMap>

    <select id="selectByTaskId" resultMap="TaskDiMap">
        SELECT
            eptd.di, eptd.name, eptd.code, eptd.pn
        FROM
            em_process_task_di eptd,
            em_process_task_relation_di eptrd
        WHERE eptd.id = eptrd.task_di_id
            AND eptrd.is_deleted = 0
            AND eptd.is_deleted = 0
            AND eptrd.task_id = #{taskId}
    </select>
</mapper>