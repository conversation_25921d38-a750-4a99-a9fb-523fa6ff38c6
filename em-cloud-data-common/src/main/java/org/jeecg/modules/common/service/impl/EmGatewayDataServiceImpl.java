package org.jeecg.modules.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jeecg.common.constant.DatabaseConstants;
import org.jeecg.common.constant.RabbitMqConstants;
import org.jeecg.common.constant.StrPool;
import org.jeecg.common.util.CollectionUtil;
import org.jeecg.modules.common.config.EmPrefixProperties;
import org.jeecg.modules.common.entity.EmGatewayData;
import org.jeecg.modules.common.entity.EmSecuritySensor;
import org.jeecg.modules.common.entity.EmSensorOriginData;
import org.jeecg.modules.common.repository.EmGatewayDataRepository;
import org.jeecg.modules.common.service.EmGatewayDataService;
import org.jeecg.modules.common.service.EmSecuritySensorService;
import org.jeecg.modules.common.service.EmSensorOriginDataService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 能源监测-网关数据Service实现
 *
 * <AUTHOR>
 * @date 2022-04-24 14:33
 */
@Slf4j
@Service
public class EmGatewayDataServiceImpl implements EmGatewayDataService {

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private EmPrefixProperties emPrefixProperties;
    @Autowired
    private EmSecuritySensorService emSecuritySensorService;
    @Autowired
    private EmGatewayDataRepository emGatewayDataRepository;
    @Autowired
    private EmSensorOriginDataService emSensorOriginDataService;
    /**
     * 电表编号key
     */
    private static final String DEVICE_CODE_KEY = "E0800002";

    @Override
    @Transactional(value = "mongoTransactionManager", rollbackFor = Exception.class)
    public List<EmSensorOriginData> insert(EmGatewayData gatewayData) {
        gatewayData.setDataDate(DateUtil.formatDate(DateUtil.parseDate(gatewayData.getSendTime())));
        gatewayData.setCreateBy(DatabaseConstants.SYS_ADMIN);
        gatewayData.setCreateTime(new Date());
        // 网关数据直接入库，入库后不再做任何操作
        emGatewayDataRepository.insert(gatewayData);
        // 新增传感器原始数据
        return emSensorOriginDataService.insert(gatewayData);
    }

    @Async
    @Override
    public void reSendData(String deviceAddr, String sdt, String edt) {
        // 日期格式化
        Date startDt = DateUtil.parseDate(sdt);
        Date endDt = DateUtil.parseDate(edt);
        while (startDt.compareTo(endDt) <= 0) {
            log.info("网关数据日期，dataDate → " + DateUtil.formatDate(startDt));
            // 发送数据
            this.sendMsg(deviceAddr, startDt);
            // 开始日期加一
            startDt = DateUtils.addDays(startDt, 1);
        }
    }

    @Async
    @Override
    public void reSendData(List<String> deviceAddrList, String sdt, String edt) {
        // 日期格式化
        Date startDt = DateUtil.parseDate(sdt);
        Date endDt = DateUtil.parseDate(edt);
        while (startDt.compareTo(endDt) <= 0) {
            log.info("网关数据日期，dataDate → " + DateUtil.formatDate(startDt));
            for (String deviceAddr : deviceAddrList) {
                // 发送数据
                this.sendMsg(deviceAddr, startDt);
            }
            // 开始日期加一
            startDt = DateUtils.addDays(startDt, 1);
        }
    }

    @Async
    @Override
    public void reSendData(String sdt, String edt) {
        List<EmSecuritySensor> emSecuritySensorList = emSecuritySensorService.selectAll();
        if (CollUtil.isNotEmpty(emSecuritySensorList)) {
            emSecuritySensorList.forEach(emSecuritySensor -> reSendData(emSecuritySensor.getDeviceAddr(), sdt, edt));
        }
    }

    @Override
    public void batchReSendInfraredData(List<String> deviceAddrList, String sdt, String edt) {
        // 日期格式化
        Date startDt = DateUtil.parseDate(sdt);
        Date endDt = DateUtil.parseDate(edt);
        while (startDt.compareTo(endDt) <= 0) {
            log.info("网关数据日期，dataDate → " + DateUtil.formatDate(startDt));
            // 发送数据
            for (String deviceAddr : deviceAddrList) {
                // 发送数据
                this.sendInfraredMsg(deviceAddr, startDt);
            }
            // 开始日期加一
            startDt = DateUtils.addDays(startDt, 1);
        }
    }

    @Override
    public void specialDealInfraredGatewayCode(EmGatewayData emGatewayData) {
        // 特殊处理网关编号
        List<EmSensorOriginData> emSensorOriginDataList = JSON.parseArray(emGatewayData.getDataContent(), EmSensorOriginData.class);
        Map<String, String> valueMap = CollectionUtil.convertMap(emSensorOriginDataList, EmSensorOriginData::getDi, EmSensorOriginData::getValue);
        // 电表编号
        String deviceCode = valueMap.get(DEVICE_CODE_KEY);
        if (StringUtils.isBlank(deviceCode)) {
            log.error("红外数据获取电表编号为空，网关编号为：{}", emGatewayData.getDeviceAddr());
        }
        // 原始数据设备编号
        String originDataDeviceAddr = emGatewayData.getDeviceAddr() + StrPool.UNDER_LINE + deviceCode;
        // 更新原始数据设备编号
        emSensorOriginDataList.forEach(emSensorOriginData -> emSensorOriginData.setDeviceAddr(originDataDeviceAddr));
        emGatewayData.setDataContent(JSON.toJSONString(emSensorOriginDataList));
    }

    /**
     * 发送数据（特殊处理红外数据）
     *
     * @param deviceAddr 网关编号
     * @param dataDate   日期
     */
    private void sendInfraredMsg(String deviceAddr, Date dataDate) {
        List<EmGatewayData> emGatewayDataList = emGatewayDataRepository.findByDeviceAddrAndDataDate(deviceAddr, DateUtil.formatDate(dataDate));
        emGatewayDataList.forEach(emGatewayData -> {
            specialDealInfraredGatewayCode(emGatewayData);
            // 传感器原始数据列表
            List<EmSensorOriginData> emSensorOriginDataAddList = emSensorOriginDataService.insert(emGatewayData);
            log.info("传感器原始数据 → " + JSON.toJSONString(emSensorOriginDataAddList));
            rabbitTemplate.convertAndSend(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.DIRECT_EXCHANGE, RabbitMqConstants.SENSOR_DATA_QUEUE, JSON.toJSONString(emSensorOriginDataAddList));
        });
    }

    /**
     * 发送数据
     *
     * @param deviceAddr
     * @param dataDate
     */
    private void sendMsg(String deviceAddr, Date dataDate) {
        List<EmGatewayData> emGatewayDataList = emGatewayDataRepository.findByDeviceAddrAndDataDate(deviceAddr, DateUtil.formatDate(dataDate));
        emGatewayDataList.forEach(emGatewayData -> {
            // 传感器原始数据列表
            List<EmSensorOriginData> emSensorOriginDataList = emSensorOriginDataService.insert(emGatewayData);
            log.info("传感器原始数据 → " + JSON.toJSONString(emSensorOriginDataList));
            rabbitTemplate.convertAndSend(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.DIRECT_EXCHANGE, RabbitMqConstants.SENSOR_DATA_QUEUE, JSON.toJSONString(emSensorOriginDataList));
        });
    }
}
