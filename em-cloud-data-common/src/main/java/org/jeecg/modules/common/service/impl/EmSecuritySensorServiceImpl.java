package org.jeecg.modules.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.RedisKeyConstants;
import org.jeecg.common.constant.SysConstants;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtils;
import org.jeecg.modules.common.dto.TaskDTO;
import org.jeecg.modules.common.entity.EmSecuritySensor;
import org.jeecg.modules.common.mapper.EmSecuritySensorMapper;
import org.jeecg.modules.common.service.EmSecuritySensorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * 能源监测-数据处理-设备Service实现
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Slf4j
@Service
public class EmSecuritySensorServiceImpl extends ServiceImpl<EmSecuritySensorMapper, EmSecuritySensor> implements EmSecuritySensorService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private EmSecuritySensorMapper emSecuritySensorMapper;

    /**
     * 初始化 taskDi
     */
    @PostConstruct
    public void initTaskAndTaskDi() {
        List<TaskDTO> taskDtoList = emSecuritySensorMapper.selectAllUpdateRedis();
        if (CollUtil.isNotEmpty(taskDtoList)) {
            taskDtoList.forEach(taskDTO -> {
                String value = JSON.toJSONString(taskDTO);
                redisUtils.strSet(CharSequenceUtil.format(RedisKeyConstants.TaskDi.MQ_TASK_DI_KEY, taskDTO.getTaskDi()), value);
            });
        }
    }

    @Override
    public List<EmSecuritySensor> selectAll() {
        return emSecuritySensorMapper.selectList(new LambdaQueryWrapper<EmSecuritySensor>().eq(EmSecuritySensor::getDeleted, SysConstants.IsStatus.NO)
                .eq(EmSecuritySensor::getStatus, Boolean.TRUE));
    }

    @Override
    public EmSecuritySensor getOneByDeviceAddr(String deviceAddr) {
        return emSecuritySensorMapper.selectOne(new LambdaQueryWrapper<EmSecuritySensor>().eq(EmSecuritySensor::getDeviceAddr, deviceAddr)
                .eq(EmSecuritySensor::getStatus, Boolean.TRUE));
    }

    @Override
    public EmSecuritySensor getOneByAccessId(String accessId) {
        return emSecuritySensorMapper.selectOne(new LambdaQueryWrapper<EmSecuritySensor>()
                .eq(EmSecuritySensor::getDeleted, SysConstants.IsStatus.NO).eq(EmSecuritySensor::getAccessId, accessId)
                .eq(EmSecuritySensor::getStatus, Boolean.TRUE));
    }

    @Override
    public void updateKeyByAccessId(String accessId, String accessKey) {
        LambdaUpdateWrapper<EmSecuritySensor> updateWrapper = new LambdaUpdateWrapper<EmSecuritySensor>()
                .eq(EmSecuritySensor::getAccessId, accessId)
                .eq(EmSecuritySensor::getStatus, Boolean.TRUE);
        if (Objects.nonNull(accessKey)) {
            updateWrapper.set(EmSecuritySensor::getAccessKey, accessKey);
        }
        this.update(updateWrapper);
    }

    @Override
    public void addSecuritySensor(EmSecuritySensor emSecuritySensor) {
        checkSecuritySensor(emSecuritySensor);
        this.save(emSecuritySensor);
    }

    /**
     * 校验网关信息
     *
     * @param emSecuritySensor
     */
    private void checkSecuritySensor(EmSecuritySensor emSecuritySensor) {
        if (ObjectUtil.isEmpty(emSecuritySensor)) {
            throw new JeecgBootException("新增数据不能为空！");
        }
        EmSecuritySensor existEmSecuritySensor = this.getOneByDeviceAddr(emSecuritySensor.getDeviceAddr());
        if (Objects.nonNull(existEmSecuritySensor)) {
            throw new JeecgBootException("该网关已存在！");
        }
    }

    @Override
    public void removeByDeviceAddr(String deviceAddr) {
        this.remove(new LambdaQueryWrapper<EmSecuritySensor>().eq(EmSecuritySensor::getDeviceAddr, deviceAddr));
    }

    @Override
    public void removeByDeviceAddr(List<String> deviceAddrList) {
        this.remove(new LambdaQueryWrapper<EmSecuritySensor>().in(EmSecuritySensor::getDeviceAddr, deviceAddrList));
    }

    @Override
    public void setAccessId(String deviceAddr, String accessId) {
        if (StringUtils.isBlank(deviceAddr) || StringUtils.isBlank(accessId)) {
            log.error("设备准入id赋值失败！deviceAddr → {} ，accessId → {}", deviceAddr, accessId);
            return;
        }
        EmSecuritySensor emSecuritySensor = this.getOneByDeviceAddr(deviceAddr);
        if (Objects.isNull(emSecuritySensor)) {
            // fixme 当网关未初始化，是否初始化
        } else {
            emSecuritySensor.setAccessId(accessId);
            this.updateById(emSecuritySensor);
        }
    }

}
