package org.jeecg.modules.common.entity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.jeecg.modules.mongo.entity.SysMongoBaseEntity;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 能源监测-网关数据
 *
 * <AUTHOR>
 * @date 2022/1/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(value = "em_gateway_data")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "em_gateway_data对象", description = "能源监测-网关数据")
@CompoundIndex(useGeneratedName = true, def = "{'device_addr':1, 'data_date':1}", background = true)
public class EmGatewayData extends SysMongoBaseEntity {
    private static final long serialVersionUID = -128688391820250074L;
    /**
     * 记录日期
     */
    @Field("data_date")
    private String dataDate;
    /**
     * 发送时间
     */
    @Field("send_time")
    private String sendTime;
    /**
     * 终端地址
     */
    @Field("device_addr")
    private String deviceAddr;
    /**
     * jcdId
     */
    @Field("jcd_id")
    private String jcdId;
    /**
     * 任务di
     */
    @Field("task_di")
    private String taskDi;
    /**
     * 任务名称
     */
    @Field("task_name")
    private String taskName;
    /**
     * 数据内容
     */
    @Field("data_content")
    private String dataContent;
}
