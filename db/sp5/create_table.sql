CREATE TABLE `em_security_sensor` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `device_addr` varchar(32) NOT NULL COMMENT '终端地址',
  `access_id` varchar(32) DEFAULT NULL COMMENT '设备准入ID',
  `access_key` varchar(32) DEFAULT NULL COMMENT '设备密钥',
  `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `create_by` varchar(32) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_device_addr` (`device_addr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='能源监测-数据处理-设备';

