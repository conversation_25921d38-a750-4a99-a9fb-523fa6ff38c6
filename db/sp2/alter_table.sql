-- 网关
ALTER TABLE em_process_sensor
ADD COLUMN `recall_start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '回溯数据开始时间' AFTER latest_com_time;

ALTER TABLE em_process_sensor
ADD COLUMN  `recall_end_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '回溯数据结束时间' AFTER recall_start_time;

ALTER TABLE em_process_sensor
ADD COLUMN `is_valve_open` tinyint(1) NOT NULL DEFAULT '1' COMMENT '阀控是否开启：0-否，1-是' AFTER data_freq;



-- 执行sql后 启动项目
-- 定时任务包名变动，需要删掉，删所有
-- https://dev.bonahl.com/em-system/data/testOne/deleteAllJob?jobClassName=org.jeecg.modules.job.GatewayDataJob

-- 初始化回溯数据时间 latest_com_time 赋给end，start = latest_com_time -3小时
-- https://dev.bonahl.com/em-system/data/testOne/initReCallDate

-- 删完之后恢复定时任务
-- https://dev.bonahl.com/em-system/data/testOne/dealOldSensorJob

ALTER TABLE em_process_sensor
ADD COLUMN `init_data_val` decimal(20, 4) NOT NULL DEFAULT '0' COMMENT '初始用量' AFTER data_freq;

