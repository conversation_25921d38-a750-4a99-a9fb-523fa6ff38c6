-- 网关
ALTER TABLE em_security_sensor
ADD COLUMN `name` varchar(64) NOT NULL COMMENT '网关名称' AFTER id,
ADD COLUMN `need_decrypt` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '需要解密：0否 1是' AFTER access_key,
ADD COLUMN `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0无效 1有效' AFTER need_decrypt;

ALTER TABLE em_process_sensor
    CHANGE `type` `type` smallint(5) unsigned COMMENT '设备类型：1.水，2.电';
UPDATE em_process_sensor set type = 2  where type = 1;

ALTER TABLE em_security_sensor
    ADD COLUMN `supplier_code` varchar(64) DEFAULT NULL COMMENT '供应商资产编号' AFTER status;

INSERT INTO `em_process_task` (`id`, `task_di`, `task_name`, `is_deleted`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1483390647996842000', 'acrel0020', '获取用户名下某个水表信息', 0, 'system', '2024-03-20 18:47:45', NULL, '2024-03-20 11:03:09');

INSERT INTO `em_process_task_di` (`id`, `di`, `name`, `code`, `pn`, `is_deleted`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1483647747490640014', 'ForceMode', '分合闸状态:1是开阀，2是关阀', 'ForceMode', 0, 0, 'system', '2022-01-19 11:49:22', NULL, '2023-03-22 09:36:37');

INSERT INTO `em_process_task_relation_di` (`id`, `task_id`, `task_di_id`, `is_deleted`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('148361235649200014', '1483390647996842000', '1483647747490640014', 0, 'system', '2024-03-16 17:28:39', NULL, '2024-03-16 17:07:09');

-- 网关b数据转换
INSERT INTO `em_process_task_relation_di` (`id`, `task_id`, `task_di_id`, `is_deleted`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('148361235649200015', '1483390647996842001', '1483647747490640015', 0, 'system', '2024-03-16 17:28:39', NULL, '2024-03-16 17:07:09');

INSERT INTO `em_process_task_relation_di` (`id`, `task_id`, `task_di_id`, `is_deleted`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('148361235649200016', '1483390647996842001', '1483647747490640016', 0, 'system', '2024-03-16 17:28:39', NULL, '2024-03-16 17:07:09');

INSERT INTO `em_process_task` (`id`, `task_di`, `task_name`, `is_deleted`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1483390647996842001', 'gatewayBData', '网关B监测数据', 0, 'system', '2024-03-20 18:47:45', NULL, '2024-03-20 11:03:09');

INSERT INTO `em_process_task_di` (`id`, `di`, `name`, `code`, `pn`, `is_deleted`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1483647747490640015', 'kWh Import', '正向有功总电能', 'pos-ept', 0, 0, 'system', '2022-01-19 11:49:22', NULL, '2025-05-09 14:28:41');
INSERT INTO `em_process_task_di` (`id`, `di`, `name`, `code`, `pn`, `is_deleted`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1483647747490640016', 'kWh output', '反向有功总电能', 'neg-ept', 0, 0, 'system', '2022-01-19 11:49:22', NULL, '2025-05-09 14:39:40');
