package org.jeecg.modules.dlt645.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 网关上报DTO
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GatewayReportDTO implements Serializable {
    private static final long serialVersionUID = -3054616318134600845L;
    /**
     * 网关编号
     */
    private String code;
    /**
     * 开始时间
     */
    private String sdt;
    /**
     * 结束时间
     */
    private String edt;
}
