package org.jeecg.modules.dlt645.api.fallback;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.dlt645.api.IEmDataDlt645Api;
import org.jeecg.modules.dlt645.dto.GatewayReportDTO;
import org.jeecg.modules.mqtt.dto.GatewayDeviceDTO;

/**
 * 进入fallback的方法 检查是否token未设置
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
@Slf4j
@Setter
public class EmDataDlt645ApiFallback implements IEmDataDlt645Api {
    private Throwable cause;

    @Override
    public Result<String> saveOrUpdate(GatewayDeviceDTO gatewayDeviceDTO) {
        log.error("新增网关出错 → {}", cause.getMessage());
        return Result.error("新增网关失败！");
    }

    @Override
    public Result<String> cancel(String id) {
        log.error("取消网关出错 → {}", cause.getMessage());
        return Result.error("取消网关失败！");
    }

    @Override
    public Result<String> report(GatewayReportDTO reportDTO) {
        log.error("网关上报数据出错 → {}", cause.getMessage());
        return Result.error("网关上报数据失败！");
    }
}
