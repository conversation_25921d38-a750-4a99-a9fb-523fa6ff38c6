package org.jeecg.modules.dlt645.api;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.dlt645.api.factory.EmDataDlt645ApiFallbackFactory;
import org.jeecg.modules.dlt645.dto.GatewayReportDTO;
import org.jeecg.modules.mqtt.dto.GatewayDeviceDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * DLT645网关API
 *
 * <AUTHOR>
 * @date 2022-08-22 10:23
 */
@Component
@FeignClient(contextId = "emDataMqttApi",
        value = "em-data-dlt645",
        fallbackFactory = EmDataDlt645ApiFallbackFactory.class)
public interface IEmDataDlt645Api {
    /**
     * 新增网关
     *
     * @param gatewayDeviceDTO
     * @return
     */
    @PostMapping("/dataDlt645/api/saveOrUpdate")
    Result<String> saveOrUpdate(@RequestBody GatewayDeviceDTO gatewayDeviceDTO);

    /**
     * 作废网关
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/dataMqtt/api/cancel")
    Result<String> cancel(@RequestParam("id") String id);

    /**
     * 上报数据
     *
     * @param reportDTO
     * @return
     */
    @PostMapping(value = "/dataMqtt/api/report")
    Result<String> report(@RequestBody GatewayReportDTO reportDTO);
}
