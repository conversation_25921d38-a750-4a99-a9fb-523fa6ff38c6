package org.jeecg.modules.dlt645.api.factory;

import org.jeecg.modules.dlt645.api.IEmDataDlt645Api;
import org.jeecg.modules.dlt645.api.fallback.EmDataDlt645ApiFallback;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 回调工厂
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
@Component
public class EmDataDlt645ApiFallbackFactory implements FallbackFactory<IEmDataDlt645Api> {
    @Override
    public IEmDataDlt645Api create(Throwable throwable) {
        EmDataDlt645ApiFallback fallback = new EmDataDlt645ApiFallback();
        fallback.setCause(throwable);
        return fallback;
    }
}
