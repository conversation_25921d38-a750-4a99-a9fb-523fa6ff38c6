package org.jeecg.modules.data.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 电费充值
 *
 * <AUTHOR>
 * @date 2022-01-13 15:23
 */
@Data
public class SalePowerReqDTO {
    /**
     * 电表编号或者商铺号（推荐用电表编号防止重复）
     */
    @NotBlank(message = "电表编号或者商铺号不能为空！")
    private String str;
    /**
     * 充值金额，单位：分
     */
    @NotBlank(message = "充值金额不能为空！")
    private String saleMoney;
    /**
     * 充值类型（微信支付 = 7,支付宝支付 = 8,充正=-1，退电=-2）
     */
    @NotBlank(message = "充值类型不能为空！")
    private String buyType;
    /**
     * 订单号，没有特定规则，确保不重复，最大长度nvarchar(200)
     */
    @NotBlank(message = "订单号不能为空！")
    private String saleId;
}
