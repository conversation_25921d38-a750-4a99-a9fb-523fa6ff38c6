package org.jeecg.modules.data.api.fallback;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.data.api.IEmDataApi;
import org.jeecg.modules.data.dto.EmProcessSensorDTO;
import org.jeecg.modules.data.dto.EmSensorRerunDataDTO;
import org.jeecg.modules.data.dto.MeterControlReqDTO;

import java.util.List;

/**
 * 进入fallback的方法 检查是否token未设置
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
@Slf4j
public class EmDataApiFallback implements IEmDataApi {
    @Setter
    private Throwable cause;

    @Override
    public Result<String> addBatch(List<EmProcessSensorDTO> emProcessSensorList) {
        log.error("新增网关出错 → {}", cause.getMessage());
        return Result.error("新增网关失败！");
    }

    @Override
    public Result<String> meterControl(MeterControlReqDTO reqDTO) {
        log.error("开关阀出错 → {}", cause.getMessage());
        return Result.error("开关阀出错！");
    }

    @Override
    public Result<String> rerunOriginData(EmSensorRerunDataDTO emSensorRerunDataDTO) {
        log.error("重跑origin数据 → {}", cause.getMessage());
        return Result.error("数据重跑出错！");
    }

    @Override
    public Result<String> deleteJobAddAgain(EmSensorRerunDataDTO emSensorRerunDataDTO) {
        log.error("传感器编号变更、上报频率变更 → {}", cause.getMessage());
        return Result.error("传感器编号变更、上报频率变更,数据重跑出错！");
    }

    @Override
    public Result<String> deleteProcessSensor(List<String> codeList) {
        log.error("删除网关出错 → {}", cause.getMessage());
        return Result.error("删除网关出错！");
    }

    @Override
    public Result<String> initSysParkJob(String sysParkId, Boolean checkParkFlag) {
        log.error("数据网关-初始化园区网关出错 → {}", cause.getMessage());
        return Result.error("初始化园区网关出错！");
    }

    @Override
    public Result<String> getRealData(String deviceAddr) {
        log.error("安科瑞抄表出错 → {}", cause.getMessage());
        return Result.error("安科瑞抄表出错！");
    }
}
