package org.jeecg.modules.data.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 重跑origin数据DTO
 *
 * <AUTHOR>
 * @date 2022-11-1
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmSensorRerunDataDTO {
    /**
     * 传感器编码
     */
    private String sensorCode;
    /**
     * 安装时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date installTime;
    /**
     * 安装时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    /**
     * 数据来源  1 安科瑞，2 前置机，3 模拟数据
     */
    private Integer dataSources;
    /**
     * 数据频率
     */
    private Integer dataFreq;
    /**
     * 旧传感器编码
     */
    private String oldSensorCode;
    /**
     * 初始化数据
     **/
    private BigDecimal initDataVal;
    /**
     * 是否安装时间变更
     **/
    private Boolean changeInstallTime;
    
    /** 园区id **/
    private String sysParkId;

    /**
     * 构造方法
     *
     * @param sensorCode
     * @param installTime
     * @param endDate
     * @param dataSources
     * @param dataFreq
     * @param initDataVal
     */
    public EmSensorRerunDataDTO(String sensorCode, Date installTime, Date endDate, Integer dataSources, Integer dataFreq, BigDecimal initDataVal) {
        this.sensorCode = sensorCode;
        this.installTime = installTime;
        this.endDate = endDate;
        this.dataSources = dataSources;
        this.dataFreq = dataFreq;
        this.initDataVal = initDataVal;
        this.changeInstallTime = Boolean.FALSE;
    }

    /**
     * 构造方法
     *
     * @param sensorCode
     * @param installTime
     * @param endDate
     * @param dataSources
     * @param dataFreq
     * @param initDataVal
     */
    public EmSensorRerunDataDTO(String sensorCode, Date installTime, Date endDate, Integer dataSources, Integer dataFreq, BigDecimal initDataVal, String sysParkId) {
        this.sensorCode = sensorCode;
        this.installTime = installTime;
        this.endDate = endDate;
        this.dataSources = dataSources;
        this.dataFreq = dataFreq;
        this.initDataVal = initDataVal;
        this.changeInstallTime = Boolean.FALSE;
        this.sysParkId = sysParkId;
    }
}
