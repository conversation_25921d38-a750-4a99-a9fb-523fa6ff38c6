package org.jeecg.modules.data.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jeecg.common.constant.enums.DataSourcesEnum;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 数据处理-设备DTO
 *
 * <AUTHOR>
 * @date 2022-08-22 10:29
 */
@Data
@NoArgsConstructor
public class EmProcessSensorDTO implements Serializable {
    private static final long serialVersionUID = -3054616318134600845L;
    /**
     * 终端地址
     */
    private String deviceAddr;
    /**
     * 最近通讯时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestComTime;
    /**
     * 数据来源  1 安科瑞，2 前置机，3 模拟数据
     */
    private Integer dataSources;

    /**
     * 数据频率
     */
    private Integer dataFreq;

    /**
     * 初始化数据
     */
    private BigDecimal initDataVal;

    /**
     * 园区id
     */
    private String sysParkId;

    /**
     * 设备类型：1.电，2.水
     */
    private Integer type;

    /**
     * 构造方法
     *
     * @param deviceAddr
     * @param latestComTime
     */
//    public EmProcessSensorDTO(String deviceAddr, Date latestComTime, Integer dataSources, Integer dataFreq, BigDecimal initDataVal) {
//        this(deviceAddr, latestComTime, dataSources, dataFreq, initDataVal, "");
//    }

    public EmProcessSensorDTO(String deviceAddr, Date latestComTime, Integer dataSources, Integer dataFreq, BigDecimal initDataVal, String sysParkId, Integer type) {
        this.deviceAddr = deviceAddr;
        this.latestComTime = latestComTime;
        this.dataSources = dataSources;
        this.dataFreq = dataFreq;
        this.initDataVal = initDataVal;
        this.sysParkId = sysParkId;
        this.type = type;
    }
}
