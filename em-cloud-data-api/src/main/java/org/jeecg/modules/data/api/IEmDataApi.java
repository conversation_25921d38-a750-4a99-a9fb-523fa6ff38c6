package org.jeecg.modules.data.api;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.ServiceNameConstants;
import org.jeecg.modules.data.api.factory.EmDataApiFallbackFactory;
import org.jeecg.modules.data.dto.EmProcessSensorDTO;
import org.jeecg.modules.data.dto.EmSensorRerunDataDTO;
import org.jeecg.modules.data.dto.MeterControlReqDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 数据网关API
 *
 * <AUTHOR>
 * @date 2022-08-22 10:23
 */
@Component
@FeignClient(contextId = "emDataApi",
        value = ServiceNameConstants.EM_DATA_SERVICE,
        fallbackFactory = EmDataApiFallbackFactory.class)
public interface IEmDataApi {
    /**
     * 新增传感器
     *
     * @param emProcessSensorList
     * @return
     */
    @PostMapping("/data/api/addBatch")
    Result<String> addBatch(@RequestBody List<EmProcessSensorDTO> emProcessSensorList);

    /**
     * 分合闸控制（管理员权限）
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/data/api/meterControl")
    Result<String> meterControl(@RequestBody MeterControlReqDTO reqDTO);

    /**
     * 重跑origin数据
     *
     * @param emSensorRerunDataDTO
     * @return
     */
    @PostMapping(value = "/data/api/rerunOriginData")
    Result<String> rerunOriginData(@RequestBody EmSensorRerunDataDTO emSensorRerunDataDTO);

    /**
     * 传感器编号变更、上报频率变更
     *
     * @param emSensorRerunDataDTO
     * @return
     */
    @PostMapping(value = "/data/api/deleteJobAddAgain")
    Result<String> deleteJobAddAgain(@RequestBody EmSensorRerunDataDTO emSensorRerunDataDTO);

    /**
     * 删除网关
     *
     * @param codeList
     * @return
     */
    @DeleteMapping(value = "/data/api/deleteProcessSensor")
    Result<String> deleteProcessSensor(@RequestParam("codeList") List<String> codeList);

    /**
     * 初始化园区网关定时任务
     *
     * @param sysParkId 园区
     * @return org.jeecg.common.api.vo.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023年09月08日 11:09:34
     */
    @GetMapping(value = "/data/api/initSysParkJob")
    Result<String> initSysParkJob(@RequestParam(value = "sysParkId", required = false) String sysParkId,
                                  @RequestParam(name = "checkParkFlag", required = false) Boolean checkParkFlag);

    /**
     * 抄表
     *
     * @param deviceAddr
     * @return
     */
    @PostMapping(value = "/data/api/getRealData")
    Result<String> getRealData(@NotBlank(message = "网关编号不能为空！") @RequestParam(name = "deviceAddr") String deviceAddr);
}
