package org.jeecg.modules.data.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 能源监测-数据处理-用户
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Data
public class UserReqDTO {
    /**
     * id
     */
    private String id;
    /**
     * 用户名
     */
    @NotBlank(message = "用户id不能为空！")
    private String userId;
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空！")
    private String userName;
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空！")
    private String password;
    /**
     * 电话
     */
    @NotBlank(message = "电话不能为空！")
    private String tel;
    /**
     * 地址
     */
    private String address;
    /**
     * 备注
     */
    private String remark;
}
