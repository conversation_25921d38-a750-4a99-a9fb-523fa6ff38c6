package org.jeecg.modules.data.api.factory;

import org.jeecg.modules.data.api.IEmDataApi;
import org.jeecg.modules.data.api.fallback.EmDataApiFallback;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 回调工厂
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
@Component
public class EmDataApiFallbackFactory implements FallbackFactory<IEmDataApi> {
    @Override
    public IEmDataApi create(Throwable throwable) {
        EmDataApiFallback fallback = new EmDataApiFallback();
        fallback.setCause(throwable);
        return fallback;
    }
}