package org.jeecg.modules.data.dto;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.StrPool;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 分合闸控制
 *
 * <AUTHOR>
 * @date 2022-01-13 15:23
 */
@Data
@Getter
@NoArgsConstructor
public class MeterControlReqDTO {
    /**
     * 电表编号或者商铺号（推荐用电表编号防止重复）
     */
    @NotBlank(message = "电表编号或者商铺号不能为空！")
    private String str;
    /**
     * 1：强制合闸，2：强制分闸，3：恢复预付费
     */
    @NotNull(message = "分合闸控制类型不能为空！")
    private Integer codeType;
    /**
     * 映射表id
     */
    private String mapSensorId;
    /**
     * 需要通知的用户id
     */
    private String userId;

    public void setStr(String str) {
        if (str.contains(StrPool.UNDER_LINE)) {
            this.str = StringUtils.substringBeforeLast(str, StrPool.UNDER_LINE);
        } else {
            this.str = str;
        }
    }

    public void setCodeType(Integer codeType) {
        this.codeType = codeType;
    }

    /**
     * 构造方法
     *
     * @param str
     * @param codeType
     * @param mapSensorId
     * @param userId
     */
    public MeterControlReqDTO(String str, Integer codeType, String mapSensorId, String userId) {
        if (str.contains(StrPool.UNDER_LINE)) {
            this.str = StringUtils.substringBeforeLast(str, StrPool.UNDER_LINE);
        } else {
            this.str = str;
        }
        this.codeType = codeType;
        this.mapSensorId = mapSensorId;
        this.userId = userId;
    }
}
