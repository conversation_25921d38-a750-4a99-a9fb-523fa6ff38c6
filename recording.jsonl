{"instruction": "你是一个 DDD（领域驱动设计）专家，正在从代码库中构建一个业务上的中英字典作为索引。你需要从给定的代码片段中提取出重要的概念，以便于其它人理解和使用。\n\n- 它不是公共的库 API（如 Spring 等标准库或者平台的 API）、三方库 API（如 OkHttp、Retrofit 等），也不是常用的类名（如 List、Map 等）\n- 它是关键业务概念、无法理解的单词或者拼音缩写\n- 代码翻译中，不包含任何技术词汇，比如：Controller、Exception、Request、Response、Code、Service、Repository、Mapper、DTO、VO、PO 等\n\n项目的 README 文件信息如下：\n\n\n\n你的返回格式：\n\n| 中文 | 代码翻译 | 描述 |\n\n请根据以下文件名片段，提取出重要的概念：\n\nEmProcessUserService, EmGatewayDataService, EmProcessSensorService, QuartzJobService, EmDataSourcesService, TaskGetService, EmDataProfileService, Dlt645Decoder, EmProcessDataLogRepository, EmSensorOriginDataRepository, EmGatewayDataRepository, DataConstants, AcrelDataConstants, Dlt645FrameConvert, DataInterfaceEnum, QuartzJobEnum, EmProcessTaskDiService, GetDataService, EmProcessDataLogService, EmProcessTaskService, EmSensorOriginDataService, EmProcessUserServiceImpl, EmDataProfileProdServiceImpl, EmDataSourcesServiceImpl, EmProcessTaskServiceImpl, EmDataProfileDevServiceImpl, QuartzJobServiceImpl, GetDataServiceImpl, EmGatewayDataServiceImpl, EmProcessDataLogServiceImpl, EmSensorOriginDataServiceImpl, EmProcessSensorServiceImpl, Dlt645DeviceLogService, Dlt645DeviceService, Dlt645GatewayService, Dlt645Encoder, Dlt645Handler, TaskGetServiceImpl, EmProcessTaskDiServiceImpl, EmSensorCacheValidateReceiver, ExTaskReceiver, Dlt645DiCode, Dlt645PrioritizedTask, Dlt645Constants, Dlt645TaskPriority, DataFormat, Dlt645DiGroupCode, Dlt645Command, ControlResCode, Dlt645MeterModel, ControlReqCode, Dlt645FrameBuilderService, Dlt645DataService, AcrelDataConstants, QuartzJobEnum, Dlt645DeviceServiceImpl, DataInterfaceEnum, Dlt645DataServiceImpl, Dlt645FrameBuilderServiceImpl, Dlt645GatewayServiceImpl, Dlt645DeviceLogServiceImpl, ProviderLoadedRequest, SessionResumedRequestOrBuilder, SessionSubscribedRequestOrBuilder, ClientAuthorizeRequest, SessionDiscardedRequestOrBuilder, ValuedResponseOrBuilder, BrokerInfo, BrokerInfoOrBuilder, ClientSubscribeRequest, TopicFilter, ClientSubscribeRequestOrBuilder, ProviderUnloadedRequest, HookSpec, ConnInfo, MessagePublishRequest, EmptySuccessOrBuilder, SessionTakenoverRequestOrBuilder, ClientConnectRequest, SessionTakenoverRequest, MessageDeliveredRequest, LoadedResponse, ClientAuthenticateRequest, SessionUnsubscribedRequest, SubOpts, SessionTerminatedRequestOrBuilder, SessionCreatedRequestOrBuilder, ConnInfoOrBuilder, MessageAckedRequest, ProviderLoadedRequestOrBuilder, HookSpecOrBuilder, ClientInfo, TopicFilterOrBuilder, SessionCreatedRequest, MessageAckedRequestOrBuilder, ClientDisconnectedRequestOrBuilder, SubOptsOrBuilder, EmqxExHookProto, SessionDiscardedRequest, ProviderUnloadedRequestOrBuilder, EmptySuccess, ClientConnectRequestOrBuilder, MessageDeliveredRequestOrBuilder, ClientAuthenticateRequestOrBuilder, ClientAuthorizeRequestOrBuilder, ClientInfoOrBuilder, Message, Property, ClientConnectedRequestOrBuilder, MessageDroppedRequestOrBuilder, ClientConnackRequestOrBuilder, ClientDisconnectedRequest, ClientUnsubscribeRequestOrBuilder, SessionSubscribedRequest, SessionUnsubscribedRequestOrBuilder, PropertyOrBuilder, MessageOrBuilder, SessionResumedRequest, MessageDroppedRequest, RequestMetaOrBuilder, LoadedResponseOrBuilder, SessionTerminatedRequest, ClientConnectedRequest, ValuedResponse, ClientConnackRequest, Dlt645DeviceRepository, Dlt645DeviceLogRepository, Dlt645DataRepository, HookProviderGrpc, MessagePublishRequestOrBuilder, RequestMeta, ClientUnsubscribeRequest, EmGatewayDataController, GetDataController, EmSensorOriginDataController, RtdCallSubListener, ControlValveSubListener, Dlt645Exception, EmProcessDataLogRepository, EmSensorOriginDataRepository, EmGatewayDataRepository, EmDataMqttCloudApplication, Dlt645FrameConvertImpl, EmDataDlt645CloudApplication, IEmDataDlt645Api, IEmDataApi, TcpServer, EmDataCloudApplication, Base64Decoder, EmDataApiFallbackFactory, EmDataDlt645ApiFallbackFactory, DevDataDTO, EmAuthSensorOriginDataDTO, PayloadDTO, EmDataMqttHookCloudApplication, IEmDataMqttApi, EmDataDlt645ApiFallback, EmDataApiController, EmDataMqttApiController, RabbitMqConfig, SalePowerReqDTO, GatewayUtil, MeterControlReqDTO, EmProcessSensorDTO, EmSensorRerunDataDTO, UserReqDTO, EmDataDlt645ApiController, DeviceSecretKeyDTO, HeaderDTO, BodyDTO, AuthResultDTO, IotPlatformDTO, EmExSensorOriginDataDTO, BodyDTO, DecryptDataDTO, DeviceDataInfoDTO, TaskDTO, TaskDiDTO, NotifyDataDTO, EmDataApiFallback, EmExGatewayDataDTO, EmDataMqttApiFallback, FastJsonLocalDateTimeDeserializer, EmGatewayService, EmPrefixProperties, EmAuthRunner, UserAndMeterResDTO, TaskDTO, LoginReqDTO, MockDataDTO, GatewayDeviceDTO, EmDataMqttApiFallbackFactory, GatewayCallDTO, EmGatewayData, GatewayDataJob, Dlt645DataDTO, QueueConstants, Dlt645RealTimeDataDTO, SysRedisKeyConstants, Dlt645ResDataDTO, Dlt645MqDTO, Dlt645ReqDataDTO, Dlt645DeviceDTO, Dlt645FrameDTO, Dlt645ReqDataItemDTO, EmSensorCacheValidateDTO, EmSensorDTO, EmExSensorOriginDataDTO, TaskDiDTO, MeterResDTO, EmExGatewayDataDTO, GatewayDeviceDTO, EmSensorRerunCIMCDataDTO, EmqxHookToRabbitMqServiceImpl, EmqxHookToRocketMqServiceImpl, EmSensor, EmGatewayServiceImpl, EmSensorOriginData, EmSecuritySensor, SysQuartzJob, EmProcessDataLog, QuartzJobMapper, RabbitMqCetListener, MqttClientSubscribeListener, MqttClientConnectListener, Dlt645Utils, EmPrefixProperties, ProcessDataConfig, Dlt645DataItemDTO, Dlt645ResDataItemDTO, Dlt645ControlValveDTO, RabbitMqListener, TopicSuffixEnum, RequestDataUtil, RocketMqListener, CronUtil, DateUtil, Dlt645Data, Dlt645Device, Dlt645DeviceLog, EmSecuritySensorMapper, EmGatewayData, EmProcessSensor, EmProcessTask, EmProcessDataLog, SysQuartzJob, EmDealDataService, QuartzJobService, EmGatewayDataService, EmSecuritySensorService, EmProcessDataLogService, EmSensorOriginDataService, HashedWheelTimerThreadFactory, DateToSecondsSerializer, EmSensorOriginData, EmSensor, EmProcessTaskDi, EmProcessUser, EmSensorOriginDataServiceImpl, QuartzConfig, EmProcessSensorMapper, EmProcessUserMapper, QuartzJobMapper, EmProcessTaskDiMapper, EmProcessTaskMapper, Dlt645BroadcastDatetimeJob, EmBnDealDataServiceImpl, Dlt645ReadDataJob, EmProcessDataLogServiceImpl, EmSecuritySensorServiceImpl, QuartzJobServiceImpl, EmGatewayDataServiceImpl\n", "output": "\n\n| 中文 | 代码翻译 | 描述 |\n|------|----------|------|\n| 电表协议 | Dlt645 | 基于DL/T645标准的电表通信协议，用于数据帧编解码和设备交互 |\n| 网关数据 | GatewayData | 与物理网关设备交互的核心数据实体，包含设备状态和通信信息 |\n| 传感器原始数据 | SensorOriginData | 直接从物联网传感器采集的未加工数据记录 |\n| 数据剖面 | DataProfile | 对设备数据的结构化描述，包含设备元数据和测量指标定义 |\n| 定时任务 | QuartzJob | 基于业务规则配置的周期性执行任务（如数据同步） |\n| 设备密钥 | DeviceSecretKey | 物联网设备的加密认证凭证，用于安全通信 |\n| 实时数据 | Rtd | 需要即时处理的设备测量值，通常用于监控大屏展示 |\n| 任务指令 | TaskDi | 下发给设备的具体操作指令配置（如阀门控制） |\n| 电表控制 | MeterControl | 对电力计量设备的远程操作指令（如费率切换） |\n| 数据解码 | Decoder | 将原始通信报文转换为结构化业务数据的处理模块 |\n| 广播校时 | BroadcastDatetime | 通过DLT645协议向多个电表同步系统时间的任务 |\n| 设备优先级 | PrioritizedTask | 定义不同设备数据采集任务的执行优先级策略 |\n| 数据常量 | DataConstants | 业务系统通用的数据格式定义（如浮点数精度） |\n| 指令分组 | DiGroupCode | DLT645协议中设备指令的分组编码体系 |\n| 计量模型 | MeterModel | 电表设备的型号规格参数配置 |\n| 缓存验证 | CacheValidate | 对传感器数据缓存有效性的校验机制 |\n| 重跑数据 | RerunData | 因数据异常需要重新处理的传感器历史数据 |\n| 云平台对接 | MqttCloud | 与物联网云平台通过MQTT协议的数据对接模块 |\n| 设备日志 | DeviceLog | 记录设备通信过程和异常事件的日志实体 |\n| 消息钩子 | Hook | 消息中间件事件触发的业务处理扩展点（如设备上下线） |\n\n提取原则：\n1. 聚焦电力物联网核心领域：电表协议(DLT645)、传感器、网关等设备交互相关概念\n2. 保留业务专属缩写：如Rtd(实时数据)、Di(设备指令)等非通用缩写\n3. 突出数据处理流程：从原始数据采集到解码、存储、重跑等完整链路\n4. 包含安全认证机制：设备密钥、通信加密等安全相关概念\n5. 排除技术实现细节：所有*Service/*DTO/*Listener等后缀均被过滤，保留核心业务名词"}