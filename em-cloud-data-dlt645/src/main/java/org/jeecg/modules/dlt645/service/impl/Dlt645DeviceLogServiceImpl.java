package org.jeecg.modules.dlt645.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.dlt645.pojo.entity.Dlt645DeviceLog;
import org.jeecg.modules.dlt645.repository.Dlt645DeviceLogRepository;
import org.jeecg.modules.dlt645.service.Dlt645DeviceLogService;
import org.jeecg.modules.mongo.annotation.MongoTransactional;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * DL/T645设备日志Service实现
 *
 * <AUTHOR>
 * @date 2025-01-08 10:13
 */
@Slf4j
@Service
public class Dlt645DeviceLogServiceImpl implements Dlt645DeviceLogService {
    @Resource
    private Dlt645DeviceLogRepository dlt645DeviceLogRepository;

    @Override
    @MongoTransactional
    public void insert(String address, Boolean isOnline) {
        Dlt645DeviceLog dlt645DeviceLog = new Dlt645DeviceLog();
        dlt645DeviceLog.setAddress(address);
        dlt645DeviceLog.setDataTime(new Date());
        dlt645DeviceLog.setOnline(isOnline);
        dlt645DeviceLogRepository.insert(dlt645DeviceLog);
    }
}
