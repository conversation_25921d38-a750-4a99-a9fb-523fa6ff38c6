package org.jeecg.modules.dlt645.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Arrays;

/**
 * DL/T645 协议从站应答帧控制码枚举
 *
 * 该类定义了 DL/T645 协议中从站响应的控制码及其描述信息。
 * 每个枚举实例包含控制码的字节值、描述信息和对应的主站请求控制码，并支持根据字节值查找对应的枚举实例。
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
@Getter
@ToString
@Accessors(fluent = true)
@RequiredArgsConstructor
public enum ControlResCode {
    // 设备注册 (0x00)
    REGISTER_DEVICE_MSG((byte) 0x00, "设备注册", ControlReqCode.REGISTER_DEVICE),

    // 设备心跳 (0x61)
    HEART_BEAT_MSG((byte) 0x61, "设备心跳", ControlReqCode.HEART_BEAT),

    // 读数据 (0x11)
    READ_DATA_NO_FOLLOWING((byte) 0x91, "读数据无后续数据帧", ControlReqCode.READ_DATA),
    READ_DATA_FOLLOWING((byte) 0xB1, "读数据有后续数据帧", ControlReqCode.READ_DATA),
    READ_DATA_ERROR((byte) 0xD1, "读数据错误", ControlReqCode.READ_DATA),

    // 写数据 (0x14)
    WRITE_DATA_SUCCESS((byte) 0x94, "写数据成功", ControlReqCode.WRITE_DATA),
    WRITE_DATA_ERROR((byte) 0xD4, "写数据错误", ControlReqCode.WRITE_DATA),

    // 读通信地址 (0x13)
    READ_ADDRESS_SUCCESS((byte) 0x93, "读通信地址成功", ControlReqCode.READ_ADDRESS),

    // 写通信地址 (0x15)
    WRITE_ADDRESS_SUCCESS((byte) 0x95, "写通信地址成功", ControlReqCode.WRITE_ADDRESS),

    // 冻结命令 (0x16)
    FREEZE_COMMAND_SUCCESS((byte) 0x96, "冻结命令成功", ControlReqCode.FREEZE_COMMAND),
    FREEZE_COMMAND_ERROR((byte) 0xD6, "冻结命令错误", ControlReqCode.FREEZE_COMMAND),

    // 更改通信速率 (0x17)
    MODIFY_BAUD_RATE_SUCCESS((byte) 0x97, "修改通信速率成功", ControlReqCode.MODIFY_BAUD_RATE),
    MODIFY_BAUD_RATE_ERROR((byte) 0xD7, "修改通信速率错误", ControlReqCode.MODIFY_BAUD_RATE),

    // 修改密码 (0x18)
    MODIFY_PASSWORD_SUCCESS((byte) 0x98, "修改密码成功", ControlReqCode.MODIFY_PASSWORD),
    MODIFY_PASSWORD_ERROR((byte) 0xD8, "修改密码错误", ControlReqCode.MODIFY_PASSWORD),

    // 清零最大需量 (0x19)
    CLEAR_MAX_DEMAND_SUCCESS((byte) 0x99, "清零最大需量成功", ControlReqCode.CLEAR_MAX_DEMAND),
    CLEAR_MAX_DEMAND_ERROR((byte) 0xD9, "清零最大需量错误", ControlReqCode.CLEAR_MAX_DEMAND),

    // 清零电表 (0x1A)
    CLEAR_METER_SUCCESS((byte) 0x9A, "清零电表成功", ControlReqCode.CLEAR_METER),
    CLEAR_METER_ERROR((byte) 0xDA, "清零电表错误", ControlReqCode.CLEAR_METER),

    // 事件清零 (0x1B)
    CLEAR_EVENTS_SUCCESS((byte) 0x9B, "事件清零成功", ControlReqCode.CLEAR_EVENTS),
    CLEAR_EVENTS_ERROR((byte) 0xDB, "事件清零错误", ControlReqCode.CLEAR_EVENTS),

    // 跳合闸/保电控制 (0x1C)
    TRIP_CLOSE_POWER_PROTECTION_SUCCESS((byte) 0x9C, "跳合闸/保电控制成功", ControlReqCode.TRIP_CLOSE_POWER_PROTECTION),
    TRIP_CLOSE_POWER_PROTECTION_ERROR((byte) 0xDC, "跳合闸/保电控制错误", ControlReqCode.TRIP_CLOSE_POWER_PROTECTION),

    // 多功能端子输出控制 (0x1D)
    MULTIFUNCTION_TERMINAL_OUTPUT_SUCCESS((byte) 0x9D, "多功能端子输出控制成功", ControlReqCode.MULTIFUNCTION_TERMINAL_OUTPUT),
    MULTIFUNCTION_TERMINAL_OUTPUT_ERROR((byte) 0xDD, "多功能端子输出控制错误", ControlReqCode.MULTIFUNCTION_TERMINAL_OUTPUT),

    // 未知控制码
    UNKNOWN((byte) -1, "未知控制码", null);

    /**
     * 控制码的字节值
     */
    private final byte code;

    /**
     * 控制码的描述信息
     */
    private final String description;

    /**
     * 对应的主站请求控制码
     */
    private final ControlReqCode controlReqCode;

    /**
     * 根据字节值获取对应的 ControlResCode 枚举实例
     *
     * @param code 控制码的字节值
     * @return 对应的 ControlResCode 枚举实例，如果找不到则返回 UNKNOWN
     */
    public static ControlResCode fromByte(byte code) {
        return Arrays.stream(values())
                .filter(responseCode -> responseCode.code() == code)
                .findFirst()
                .orElse(UNKNOWN);
    }

    /**
     * 判断当前控制码是否为未知控制码
     *
     * @return 如果是未知控制码则返回 true，否则返回 false
     */
    public boolean isUnknown() {
        return this == UNKNOWN;
    }

    /**
     * 获取控制码的十六进制字符串表示
     *
     * @return 控制码的十六进制字符串
     */
    public String toHexString() {
        return String.format("0x%02X", code);
    }

    /**
     * 判断当前控制码是否为错误码
     *
     * @return 如果是错误码则返回 true，否则返回 false
     */
    public boolean isError() {
        return description.endsWith("错误");
    }
}
