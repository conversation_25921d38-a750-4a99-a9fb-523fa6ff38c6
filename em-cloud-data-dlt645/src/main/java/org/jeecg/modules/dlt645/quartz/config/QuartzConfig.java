package org.jeecg.modules.dlt645.quartz.config;

import org.jeecg.modules.dlt645.quartz.job.Dlt645BroadcastDatetimeJob;
import org.jeecg.modules.dlt645.quartz.job.Dlt645ReadDataJob;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Quartz配置
 *
 * <AUTHOR>
 * @date 2025-01-07 14:16
 */
@Configuration
public class QuartzConfig {
    /**
     * 上报数据cron
     */
    @Value("${energy-monitor.quartz.report-data-cron}")
    private String reportDataCron;

    /**
     * 上报数据cron
     */
    @Value("${energy-monitor.quartz.broadcast-datetime-cron}")
    private String broadcastDatetimeCron;

    @Bean
    public JobDetail dlt645ReadDataJobDetail() {
        return JobBuilder.newJob(Dlt645ReadDataJob.class)
                .withIdentity("dlt645ReadDataJobDetail")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger dlt645ReadDataJobTrigger() {
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(reportDataCron)
                .withMisfireHandlingInstructionDoNothing();
        return TriggerBuilder.newTrigger()
                .forJob(dlt645ReadDataJobDetail())
                .withIdentity("dlt645ReadDataJobTrigger")
                .withSchedule(scheduleBuilder)
                .build();
    }

    @Bean
    public JobDetail dlt645BroadcastDatetimeJobDetail() {
        return JobBuilder.newJob(Dlt645BroadcastDatetimeJob.class)
                .withIdentity("dlt645BroadcastDatetimeJobDetail")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger dlt645BroadcastDatetimeJobTrigger() {
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(broadcastDatetimeCron)
                .withMisfireHandlingInstructionDoNothing();
        return TriggerBuilder.newTrigger()
                .forJob(dlt645BroadcastDatetimeJobDetail())
                .withIdentity("dlt645BroadcastDatetimeJob")
                .withSchedule(scheduleBuilder)
                .build();
    }
}
