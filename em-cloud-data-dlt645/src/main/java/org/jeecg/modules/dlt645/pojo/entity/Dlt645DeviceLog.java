package org.jeecg.modules.dlt645.pojo.entity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.jeecg.modules.mongo.converter.DataDateTimeValueConverter;
import org.jeecg.modules.mongo.entity.SysMongoBaseEntity;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.data.convert.ValueConverter;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * DL/T645设备日志
 *
 * <AUTHOR>
 * @date 2025-01-07 09:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Document(value = "dlt645_device_log")
@ApiModel(value = "dlt645_device_log对象", description = "能源监测-DL/T645设备日志")
@CompoundIndex(useGeneratedName = true, unique = true, def = "{'address':1, 'sn':1, 'data_time':1}", background = true)
public class Dlt645DeviceLog extends SysMongoBaseEntity {
    private static final long serialVersionUID = 348495859997964386L;
    /**
     * 通讯地址
     */
    @Field("address")
    private String address;
    /**
     * 在线状态
     */
    @Field("is_online")
    private Boolean online;
    /**
     * 数据时间
     */
    @ValueConverter(value = DataDateTimeValueConverter.class)
    @Excel(name = "数据时间")
    @Field("data_time")
    private Date dataTime;
}
