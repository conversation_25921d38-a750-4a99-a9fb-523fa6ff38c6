package org.jeecg.modules.dlt645.convert;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import org.jeecg.modules.dlt645.constant.ControlResCode;
import org.jeecg.modules.dlt645.constant.Dlt645DiCode;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645FrameDTO;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ResDataDTO;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ResDataItemDTO;
import org.jeecg.modules.dlt645.exception.Dlt645Exception;
import org.jeecg.modules.dlt645.util.Dlt645Utils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * Dlt645Frame转换器
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Mapper
public interface Dlt645FrameConvert {

    Dlt645FrameConvert INSTANCE = Mappers.getMapper(Dlt645FrameConvert.class);

    @Named("byteArrayToBcdString")
    default String byteArrayToBcdString(byte[] address) {
        return Dlt645Utils.convertBcdBytesToAddressString(address);
    }

    @Named("byteToControlResCode")
    default ControlResCode byteToControlResCode(byte controlCode) throws Dlt645Exception {
        ControlResCode code = ControlResCode.fromByte(controlCode);
        if (ObjUtil.isNull(code)) {
            throw new Dlt645Exception("无效的控制码: " + String.format("0x%02X", controlCode));
        }
        return code;
    }

    @Named("byteArrayToDataItemList")
    default List<Dlt645ResDataItemDTO> byteArrayToDataItemList(byte[] data) throws Dlt645Exception {
        if (ArrayUtil.isEmpty(data)) {
            return new ArrayList<>();
        }

        List<Dlt645ResDataItemDTO> dataItemList = new ArrayList<>();
        int offset = 0;
        int dataLength = data.length;
        // 至少需要 4 个字节读取 DI
        while (offset + 4 <= dataLength) {
            // 读取 DI（4 个字节）
            byte[] diBytes = new byte[4];
            System.arraycopy(data, offset, diBytes, 0, 4);
            // 反转字节数组
            diBytes = Dlt645Utils.reverseByteArray(diBytes);
            offset += 4;
            // 将 DI 转换为十六进制字符串
            String di = Dlt645Utils.byteArrayToHexString(diBytes);
            // 根据 DI 查询 Dlt645DiCode
            Dlt645DiCode energyDiCode = Dlt645DiCode.getByDi(di);
            if (ObjUtil.isNull(energyDiCode)) {
                throw new Dlt645Exception("未知的数据项标识: " + di);
            }
            // 获取数据长度
            int valueLength = energyDiCode.getLength();
            // 检查剩余字节是否足够
            if (offset + valueLength > dataLength) {
                throw new Dlt645Exception("数据长度不完整，剩余字节不足以读取数据项: " + di);
            }
            // 读取数据值
            byte[] valueBytes = new byte[valueLength];
            System.arraycopy(data, offset, valueBytes, 0, valueLength);
            // 反转字节数组
            valueBytes = Dlt645Utils.reverseByteArray(valueBytes);
            offset += valueLength;
            // 根据 dataFormat 选择解析方式
            String value;
            switch (energyDiCode.getDataFormat()) {
                case BCD:
                    value = Dlt645Utils.convertBcdBytesToFormatString(di, valueBytes);
                    break;
                case HEX:
                    value = Dlt645Utils.convertHexByteArrayToBinaryString(valueBytes);
                    break;
                case ASCII:
                    value = Dlt645Utils.convertByteArrayToAscii(valueBytes);
                    break;
                default:
                    throw new Dlt645Exception("未知的数据格式: " + energyDiCode.getDataFormat());
            }
            // 添加到结果列表
            dataItemList.add(new Dlt645ResDataItemDTO(di, energyDiCode.getDiByte(), energyDiCode.getCode(), StrUtil.trim(value)));
        }

        if (offset < dataLength) {
            throw new Dlt645Exception("数据长度不完整，剩余字节: " + (dataLength - offset));
        }
        return dataItemList;
    }

    /**
     * Dlt645Frame转Dlt645ResMsg
     *
     * @param dlt645Frame
     * @return
     * @throws Dlt645Exception
     */
    @Mappings({
            @Mapping(source = "address", target = "address", qualifiedByName = "byteArrayToBcdString"),
            @Mapping(source = "controlCode", target = "controlCode", qualifiedByName = "byteToControlResCode"),
            @Mapping(source = "data", target = "dataItemList", qualifiedByName = "byteArrayToDataItemList")
    })
    Dlt645ResDataDTO convert(Dlt645FrameDTO dlt645Frame) throws Dlt645Exception;
}
