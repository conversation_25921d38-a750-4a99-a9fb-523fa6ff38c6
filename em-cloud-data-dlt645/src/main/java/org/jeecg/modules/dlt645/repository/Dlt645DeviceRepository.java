package org.jeecg.modules.dlt645.repository;

import org.jeecg.modules.dlt645.pojo.entity.Dlt645Device;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Update;
import org.springframework.data.repository.query.Param;

import java.util.Date;

/**
 * DL/T645设备
 *
 * <AUTHOR>
 * @date 2025-01-08 12:46
 */
public interface Dlt645DeviceRepository extends MongoRepository<Dlt645Device, String> {
    /**
     * 根据设备地址，获取设备
     *
     * @param address
     * @return
     */
    @Query("{ 'address': ?0 }")
    Dlt645Device findOneByAddress(@Param("address") String address);

    /**
     * 根据设备序列号，获取设备
     *
     * @param sn
     * @return
     */
    @Query("{ 'sn': ?0 }")
    Dlt645Device findOneBySn(@Param("sn") String sn);

    /**
     * 根据设备地址，更新在线状态
     *
     * @param address
     * @param online
     */
    @Query("{ 'address': ?0 }")
    @Update("{ '$set' : { 'is_online' : ?1 , 'update_time': ?2} }")
    void updateOnlineByAddress(String address, Boolean online, Date updateTime);
}
