package org.jeecg.modules.dlt645.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jeecg.modules.dlt645.config.DateToSecondsSerializer;
import org.jeecg.modules.mongo.converter.DataDateTimeValueConverter;
import org.springframework.data.convert.ValueConverter;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

/**
 * DLT645数据明细DTO
 *
 * <AUTHOR>
 * @date 2025-01-09 16:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Dlt645DataItemDTO implements Serializable {
    private static final long serialVersionUID = -8241438726333192279L;
    /**
     * 指标Code
     */
    @Field("di_code")
    private String m;
    /**
     * 指标值
     */
    @Field("value")
    private String v;
    /**
     * 时间戳
     */
    @ValueConverter(value = DataDateTimeValueConverter.class)
    @JSONField(serializeUsing = DateToSecondsSerializer.class)
    @Field("data_time")
    private Date ts;
}
