package org.jeecg.modules.dlt645.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.jeecg.modules.dlt645.config.DateToSecondsSerializer;
import org.jeecg.modules.mongo.converter.DataDateTimeValueConverter;
import org.springframework.data.convert.ValueConverter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * DLT645设备DTO
 *
 * <AUTHOR>
 * @date 2025-01-09 16:01
 */
@Data
public class Dlt645DeviceDTO implements Serializable {
    private static final long serialVersionUID = 7118327678283113274L;
    /**
     * 设备名
     */
    private String dev;
    /**
     * 系统ID
     */
    private String sysId;
    /**
     * 时间戳
     */
    @ValueConverter(value = DataDateTimeValueConverter.class)
    @JSONField(serializeUsing = DateToSecondsSerializer.class)
    private Date ts;
    /**
     * 数据列表
     */
    private List<Dlt645DataItemDTO> d;
}
