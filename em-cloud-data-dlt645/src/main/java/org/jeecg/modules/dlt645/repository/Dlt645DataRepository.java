package org.jeecg.modules.dlt645.repository;

import org.jeecg.modules.dlt645.pojo.dto.Dlt645DataItemDTO;
import org.jeecg.modules.dlt645.pojo.entity.Dlt645Data;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * DL/T645数据
 *
 * <AUTHOR>
 * @date 2022-01-20 12:46
 */
public interface Dlt645DataRepository extends MongoRepository<Dlt645Data, String> {
    /**
     * 根据序列号和数据时间获取数据列表
     *
     * @param sn
     * @param dataTime
     * @return
     */
    @Query("{ 'sn': ?0, 'data_time': ?1 }")
    List<Dlt645Data> getList(String sn, Date dataTime);

    /**
     * 根据序列号和数据时间获取数据列表，并返回 DTO
     *
     * @param sn       序列号
     * @param dataStartTime 数据开始时间
     * @param dataEndTime 数据结束时间
     * @return DTO 列表
     */
    @Query(value = "{ 'sn': ?0, 'data_time': {$gte: ?1, $lte: ?2}}", fields = "{ 'di_code': 1, 'data_time': 1, 'value': 1 }")
    List<Dlt645DataItemDTO> getListAsDTO(String sn, String dataStartTime, String dataEndTime);
}
