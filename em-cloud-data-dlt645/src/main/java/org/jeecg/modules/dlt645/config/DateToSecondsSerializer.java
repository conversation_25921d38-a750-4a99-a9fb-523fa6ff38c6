package org.jeecg.modules.dlt645.config;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;

import java.lang.reflect.Type;
import java.util.Date;

/**
 * 序列化器，Date类型转换成Long
 *
 * <AUTHOR>
 * @date 2025-01-09 17:59
 */
public class DateToSecondsSerializer implements ObjectSerializer {
    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) {
        if (object instanceof Date) {
            Date date = (Date) object;
            // 将毫秒转换为秒
            long seconds = date.getTime() / 1000;
            serializer.write(seconds);
        }
    }
}
