package org.jeecg.modules.dlt645.service;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.dlt645.dto.GatewayReportDTO;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ResDataDTO;

/**
 * DLT645数据解析Service
 *
 * <AUTHOR>
 * @date 2024-12-23 14:28
 */
public interface Dlt645DataService {
    /**
     * 数据解析和回复
     *
     * @param dlt645ResMessage DLT645响应消息
     */
    void parse(Dlt645ResDataDTO dlt645ResMessage);

    /**
     * 处理设备注册包
     *
     * @param ctx
     * @param in
     * @return
     */
    void parseRegisterMsg(ChannelHandlerContext ctx, ByteBuf in);

    /**
     * 上报数据
     *
     * @param reportDTO
     * @return
     */
    Result<String> reportData(GatewayReportDTO reportDTO);
}
