package org.jeecg.modules.dlt645.constant;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 数据标识DI分组
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public final class Dlt645DiGroupCode {
    /**
     * 读数据 DI 字节码（不可变队列）
     */
    private static final ImmutableList<byte[]> PMC_320_W_ENERGY_DI_QUEUE = ImmutableList.of(
            Dlt645DiCode.A_PHASE_VOLTAGE.getDiByte(),
            Dlt645DiCode.A_PHASE_CURRENT.getDiByte(),
            Dlt645DiCode.INSTANTANEOUS_TOTAL_APPARENT_POWER.getDiByte(),
            Dlt645DiCode.TOTAL_POWER_FACTOR.getDiByte(),
            Dlt645DiCode.CURRENT_FORWARD_ACTIVE_TOTAL_ENERGY.getDiByte(),
            Dlt645DiCode.CURRENT_REVERSE_ACTIVE_TOTAL_ENERGY.getDiByte(),
            Dlt645DiCode.CURRENT_COMBINED_ACTIVE_TOTAL_ENERGY.getDiByte(),
            Dlt645DiCode.METER_STATUS_WORD_3.getDiByte(),
            Dlt645DiCode.DATA_NETWORK_SIGNAL_STRENGTH.getDiByte()
    );

    private static final ImmutableList<byte[]> PMC_340_W_ENERGY_DI_QUEUE = ImmutableList.of(
            Dlt645DiCode.A_PHASE_VOLTAGE.getDiByte(),
            Dlt645DiCode.B_PHASE_VOLTAGE.getDiByte(),
            Dlt645DiCode.C_PHASE_VOLTAGE.getDiByte(),
            Dlt645DiCode.A_PHASE_CURRENT.getDiByte(),
            Dlt645DiCode.B_PHASE_CURRENT.getDiByte(),
            Dlt645DiCode.C_PHASE_CURRENT.getDiByte(),
            Dlt645DiCode.INSTANTANEOUS_TOTAL_APPARENT_POWER.getDiByte(),
            Dlt645DiCode.TOTAL_POWER_FACTOR.getDiByte(),
            Dlt645DiCode.CURRENT_FORWARD_ACTIVE_TOTAL_ENERGY.getDiByte(),
            Dlt645DiCode.CURRENT_REVERSE_ACTIVE_TOTAL_ENERGY.getDiByte(),
            Dlt645DiCode.CURRENT_COMBINED_ACTIVE_TOTAL_ENERGY.getDiByte(),
            Dlt645DiCode.METER_STATUS_WORD_3.getDiByte(),
            Dlt645DiCode.DATA_NETWORK_SIGNAL_STRENGTH.getDiByte()
    );

    /**
     * 能源DI队列Map
     */
    private static final ImmutableMap<String, ImmutableList<byte[]>> ENERGY_DI_QUEUE_MAP;

    static {
        ImmutableMap.Builder<String, ImmutableList<byte[]>> builder = new ImmutableMap.Builder<>();
        builder.put(Dlt645MeterModel.PMC_320_W.code(), PMC_320_W_ENERGY_DI_QUEUE);
        builder.put(Dlt645MeterModel.PMC_340_W.code(), PMC_340_W_ENERGY_DI_QUEUE);
        ENERGY_DI_QUEUE_MAP = builder.build();
    }

    /**
     * 设备 DI 字节码（不可变队列）
     */
    private static final ImmutableList<byte[]> DEVICE_DI_QUEUE = ImmutableList.of(
            Dlt645DiCode.METER_NUMBER.getDiByte(),
            Dlt645DiCode.RATED_VOLTAGE.getDiByte(),
            Dlt645DiCode.RATED_CURRENT.getDiByte(),
            Dlt645DiCode.MAXIMUM_CURRENT.getDiByte(),
            Dlt645DiCode.METER_MODEL.getDiByte(),
            Dlt645DiCode.MANUFACTURE_DATE.getDiByte(),
            Dlt645DiCode.PROTOCOL_VERSION.getDiByte(),
            Dlt645DiCode.SERIAL_NUMBER.getDiByte()
    );

    /**
     * 获取 ENERGY_DI_QUEUE 的可变副本
     */
    public static Queue<byte[]> getEnergyDiQueueCopy(String meterModelCode) {
        return new ConcurrentLinkedQueue<>(ENERGY_DI_QUEUE_MAP.get(meterModelCode));
    }

    /**
     * 获取 ENERGY_DI_QUEUE 的可变副本
     */
    public static Queue<byte[]> getEnergyDiQueueCopy() {
        return new ConcurrentLinkedQueue<>(PMC_340_W_ENERGY_DI_QUEUE);
    }

    /**
     * 获取 DEVICE_DI_QUEUE 的可变副本
     */
    public static Queue<byte[]> getDeviceDiQueueCopy() {
        return new ConcurrentLinkedQueue<>(DEVICE_DI_QUEUE);
    }
}
