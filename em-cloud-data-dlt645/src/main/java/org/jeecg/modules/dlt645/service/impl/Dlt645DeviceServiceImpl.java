package org.jeecg.modules.dlt645.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ResDataItemDTO;
import org.jeecg.modules.dlt645.pojo.entity.Dlt645Device;
import org.jeecg.modules.dlt645.repository.Dlt645DeviceRepository;
import org.jeecg.modules.dlt645.service.Dlt645DeviceLogService;
import org.jeecg.modules.dlt645.service.Dlt645DeviceService;
import org.jeecg.modules.dlt645.service.Dlt645GatewayService;
import org.jeecg.modules.dlt645.util.Dlt645Utils;
import org.jeecg.modules.mongo.annotation.MongoTransactional;
import org.jeecg.modules.mqtt.dto.GatewayDeviceDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Queue;

/**
 * 设备管理服务实现类
 * 用于维护设备与通信信道的映射关系，并使用 Caffeine 缓存替换 ConcurrentHashMap。
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@Slf4j
@Service
public class Dlt645DeviceServiceImpl implements Dlt645DeviceService {
    /**
     * DL/T645网关Repository
     */
    @Resource
    private Dlt645GatewayService dlt645GatewayService;

    /**
     * DL/T645设备日志Service
     */
    @Resource
    private Dlt645DeviceLogService dlt645DeviceLogService;

    /**
     * DL/T645设备Repository
     */
    @Resource
    private Dlt645DeviceRepository dlt645DeviceRepository;

    /**
     * 设备地址关联设备缓存
     */
    private final Cache<String, Dlt645Device> addressToDeviceCache = Caffeine.newBuilder()
            .removalListener((key, value, cause) -> {
                log.info("设备地址 {} 到设备信息缓存已移除，原因: {}", key, cause);
            })
            // 最大缓存10000个设备
            .maximumSize(10000)
            .build();

    /**
     * 设备序列号关联设备缓存
     */
    private final Cache<String, Dlt645Device> snToDeviceCache = Caffeine.newBuilder()
            .removalListener((key, value, cause) -> {
                log.info("设备序列号 {} 到设备信息缓存已移除，原因: {}", key, cause);
            })
            // 最大缓存10000个设备
            .maximumSize(10000)
            .build();

    /**
     * 设备地址到通信信道上下文的缓存
     */
    private final Cache<String, ChannelHandlerContext> deviceAddressToChannelCache = Caffeine.newBuilder()
            .removalListener((key, value, cause) -> {
                log.info("设备地址 {} 到通信信道缓存已移除，原因: {}", key, cause);
            })
            // 最大缓存10000个设备
            .maximumSize(10000)
            .build();

    /**
     * 通信信道上下文到设备地址的缓存
     */
    private final Cache<ChannelHandlerContext, String> channelToDeviceAddressCache = Caffeine.newBuilder()
            .removalListener((key, value, cause) -> {
                log.info("通信信道上下文到设备地址 {} 的信道缓存已移除，原因: {}", key, cause);
            })
            // 最大缓存10000个设备
            .maximumSize(10000)
            .build();

    @Override
    @MongoTransactional
    public void saveOrUpdate(GatewayDeviceDTO gatewayDeviceDTO) {
        // 根据ID查询
        String id = gatewayDeviceDTO.getId();
        Optional<Dlt645Device> optional = dlt645DeviceRepository.findById(id);
        Dlt645Device dlt645Device;
        if(optional.isPresent()) {
            // 更新名称和是否加解密
            dlt645Device = optional.get();
        } else {
            dlt645Device = new Dlt645Device();
            dlt645Device.setId(id);
            dlt645Device.setSn(gatewayDeviceDTO.getCode());
            dlt645Device.setOnline(Boolean.FALSE);
        }
        dlt645Device.setName(gatewayDeviceDTO.getName());
        dlt645Device.setEncDec(gatewayDeviceDTO.getNeedDecrypt());
        this.save(dlt645Device);
    }

    @Override
    @MongoTransactional
    public void save(Dlt645Device dlt645Device) {
        // 保存
        dlt645DeviceRepository.save(dlt645Device);
        // 更新缓存
        addressToDeviceCache.put(dlt645Device.getAddress(), dlt645Device);
        // 序列号关联设备信息
        snToDeviceCache.put(dlt645Device.getSn(), dlt645Device);
    }

    @Override
    @MongoTransactional
    public void save(String address, Dlt645ResDataItemDTO dataItem) {
        // 查询设备
        Dlt645Device dlt645Device = dlt645DeviceRepository.findOneByAddress(address);
        if (ObjUtil.isEmpty(dlt645Device)) {
            // 如果设备不存在，创建新设备
            dlt645Device = new Dlt645Device();
        }
        // 设置在线状态
        dlt645Device.setOnline(Boolean.TRUE);
        // 通过反射动态设置字段值
        Dlt645Utils.setFieldValue(dlt645Device, dataItem);
        // 保存或更新设备
        dlt645DeviceRepository.save(dlt645Device);
        // 设置缓存
        addressToDeviceCache.put(address, dlt645Device);
        // 序列号
        String sn = dlt645Device.getSn();
        // 将设备地址与序列号上下文关联
        if (StrUtil.isNotBlank(sn)) {
            // 序列号关联设备信息
            snToDeviceCache.put(sn, dlt645Device);
        }
    }

    @Override
    @MongoTransactional
    public void save(String address, ChannelHandlerContext ctx) {
        // 通过通讯地址，查询设备是否存在
        Optional<Dlt645Device> optional = this.getOneByAddress(address);
        // 如果不存在，则新增设备且发送读取设备信息的消息，否则更新在线状态
        if(optional.isPresent()) {
            log.info("心跳包 → 地址：{}", address);
            // 注册设备
            this.registerDevice(address, ctx);
            // 更新在线状态
            this.updateOnline(address);
            // 设备信息
            Dlt645Device dlt645Device = optional.get();
            // 校验设备信息是否完整
            Queue<byte[]> deviceDiQueue = Dlt645Utils.getDeviceDiQueue(dlt645Device);
            if(CollUtil.isNotEmpty(deviceDiQueue)) {
                // 发送抄设备信息指令
                dlt645GatewayService.sendReadDeviceInfoMsg(address, deviceDiQueue);
            }
        } else {
            log.info("心跳包 → 地址：{}，该设备没有通过网关添加，不处理！", address);
        }

    }

    @Override
    @MongoTransactional
    public void insert(String address) {
        // 设备信息
        Dlt645Device dlt645Device = new Dlt645Device();
        dlt645Device.setAddress(address);
        dlt645Device.setOnline(Boolean.TRUE);
        dlt645DeviceRepository.save(dlt645Device);
        // 设备日志
        dlt645DeviceLogService.insert(address, Boolean.TRUE);
    }

    @Override
    public ChannelHandlerContext getChannelByAddress(String address) {
        return deviceAddressToChannelCache.getIfPresent(address);
    }

    @Override
    public Optional<String> getAddressBySn(String sn) {
        // 根据序列号获取设备信息
        Optional<Dlt645Device> optional = this.getOneBySn(sn);
        // 返回设备的地址（Optional）
        return optional.map(Dlt645Device::getAddress);
    }

    @Override
    public Optional<Dlt645Device> getOneBySn(String sn) {
        // 根据序列号从缓存中获取设备信息
        Dlt645Device dlt645Device = snToDeviceCache.getIfPresent(sn);
        if(ObjUtil.isNull(dlt645Device)) {
            dlt645Device = dlt645DeviceRepository.findOneBySn(sn);
            // 处理缓存
            if(ObjUtil.isNotNull(dlt645Device)) {
                snToDeviceCache.put(sn, dlt645Device);
            }
        }
        return Optional.ofNullable(dlt645Device);
    }

    @Override
    public Optional<String> getSnByAddress(String address) {
        // 根据地址获取设备信息
        Optional<Dlt645Device> optional = this.getOneByAddress(address);
        // 返回设备的序列号（Optional）
        return optional.map(Dlt645Device::getSn);
    }

    @Override
    public Optional<String> getMeterModelByAddress(String address) {
        // 根据地址获取设备信息
        Optional<Dlt645Device> optional = this.getOneByAddress(address);
        // 返回设备的序列号（Optional）
        return optional.map(Dlt645Device::getMeterModel);
    }

    @Override
    public List<Dlt645Device> getList() {
        return dlt645DeviceRepository.findAll();
    }

    @Override
    public Optional<Dlt645Device> getOneByAddress(String address) {
        // 根据地址从缓存中获取设备信息
        Dlt645Device dlt645Device = addressToDeviceCache.getIfPresent(address);
        if(ObjUtil.isNull(dlt645Device)) {
            dlt645Device = dlt645DeviceRepository.findOneByAddress(address);
            // 处理缓存
            if(ObjUtil.isNotNull(dlt645Device)) {
                addressToDeviceCache.put(address, dlt645Device);
            }
        }
        return Optional.ofNullable(dlt645Device);
    }

    @Override
    public String getDeviceAddressByChannel(ChannelHandlerContext ctx) {
        return channelToDeviceAddressCache.getIfPresent(ctx);
    }

    @Override
    @MongoTransactional
    public void updateOffline(ChannelHandlerContext ctx) {
        // 设备地址
        String address = this.getDeviceAddressByChannel(ctx);
        if (StrUtil.isNotBlank(address)) {
            // 更新在线状态
            dlt645DeviceRepository.updateOnlineByAddress(address, Boolean.FALSE, DateUtil.date());
            // 新增日志
            dlt645DeviceLogService.insert(address, Boolean.FALSE);
            // 取消注册
            this.unregisterDevice(address);
        }
    }

    @Override
    @MongoTransactional
    public void updateOnline(ChannelHandlerContext ctx) {
        // 地址
        String address = this.getDeviceAddressByChannel(ctx);
        if (StrUtil.isNotBlank(address)) {
            // 更新在线状态
            dlt645DeviceRepository.updateOnlineByAddress(address, Boolean.TRUE, DateUtil.date());
            // 新增日志
            dlt645DeviceLogService.insert(address, Boolean.TRUE);
        }
    }

    @Override
    @MongoTransactional
    public void updateOnline(String address) {
        // 更新设备在线状态
        dlt645DeviceRepository.updateOnlineByAddress(address, Boolean.TRUE, DateUtil.date());
        // 新增设备日志
        dlt645DeviceLogService.insert(address, Boolean.TRUE);
    }

    @Override
    @MongoTransactional
    public void delete(String id) {
        // 根据ID查询设备
        Optional<Dlt645Device> optional = dlt645DeviceRepository.findById(id);
        if(optional.isPresent()) {
            // 设备信息
            Dlt645Device dlt645Device = optional.get();
            log.error("删除设备，地址 → {}", dlt645Device.getAddress());
            // 注销设备
            this.unregisterDevice(dlt645Device.getAddress());
            // 删除缓存
            addressToDeviceCache.invalidate(dlt645Device.getAddress());
            snToDeviceCache.invalidate(dlt645Device.getSn());
            // 删除数据
            dlt645DeviceRepository.deleteById(id);
        }
    }

    @Override
    public Map<String, ChannelHandlerContext> getAddressToChannel() {
        return deviceAddressToChannelCache.asMap();
    }

    @Override
    public void registerDevice(String address, ChannelHandlerContext ctx) {
        validateInput(address, "设备地址");
        validateInput(ctx, "通信信道上下文");

        // 将设备地址与通信信道上下文关联
        deviceAddressToChannelCache.put(address, ctx);
        // 将设备加入到对应的信道组
        channelToDeviceAddressCache.put(ctx, address);
        log.info("设备注册成功: Address={}, ChannelId={}", address, ctx.channel().id().asLongText());
    }

    @Override
    public void unregisterDevice(String address) {
        validateInput(address, "设备地址");
        // 从设备映射中移除
        ChannelHandlerContext ctx = deviceAddressToChannelCache.getIfPresent(address);
        if (ObjUtil.isNotNull(ctx)) {
            // 从信道映射中移除
            channelToDeviceAddressCache.invalidate(ctx);
            // 从设备映射中移除
            deviceAddressToChannelCache.invalidate(address);
            log.info("设备注销成功: Address={}, ChannelId={}", address, ctx.channel().id().asLongText());
        }
    }

    /**
     * 验证输入参数是否为空
     *
     * @param input 输入参数
     * @param field 字段名称
     * @throws IllegalArgumentException 如果输入参数为空
     */
    private void validateInput(Object input, String field) {
        if (input == null) {
            throw new IllegalArgumentException(field + "不能为空！");
        }
    }
}
