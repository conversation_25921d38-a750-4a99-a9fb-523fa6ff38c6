package org.jeecg.modules.dlt645;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.dlt645.decoder.Dlt645Decoder;
import org.jeecg.modules.dlt645.encoder.Dlt645Encoder;
import org.jeecg.modules.dlt645.handler.Dlt645Handler;
import org.jeecg.modules.dlt645.service.Dlt645DataService;
import org.jeecg.modules.dlt645.service.Dlt645DeviceService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * TCP Server
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
@Slf4j
@Component
public class TcpServer {
    /**
     * 从配置文件中读取端口号，默认值为 8888
     */
    @Value("${netty.port:8888}")
    private int port;
    /**
     * DL/T645数据Service
     */
    @Resource
    private Dlt645DataService dlt645DataService;
    /**
     * DL/T645设备Service
     */
    @Resource
    private Dlt645DeviceService dlt645DeviceService;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        int retryCount = 3;
        while (retryCount > 0) {
            try {
                this.init();
                break;
            } catch (Exception e) {
                log.error("Failed to start TCP server, retrying...", e);
                retryCount--;
                if (retryCount == 0) {
                    log.error("Failed to start TCP server after retries.");
                }
            }
        }
    }

    public void init() throws Exception {
        log.info("Starting TCP server on port: {}", port);

        // 根据 CPU 核心数设置线程池大小
        int bossThreads = Runtime.getRuntime().availableProcessors();
        int workerThreads = bossThreads * 2;

        NioEventLoopGroup boss = new NioEventLoopGroup(bossThreads);
        NioEventLoopGroup work = new NioEventLoopGroup(workerThreads);

        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(boss, work)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) {
                            // 添加IdleStateHandler，设置心跳超时时间
                            ch.pipeline().addLast(new IdleStateHandler(60, 60, 3600));
                            // 添加日志处理器，记录调试信息
                            ch.pipeline().addLast("logging", new LoggingHandler("DEBUG"));
                            // 添加编码器，用于发送消息
                            ch.pipeline().addLast("encode", new Dlt645Encoder());
                            // 添加解码器，用于接收消息
                            ch.pipeline().addLast("decode", new Dlt645Decoder(dlt645DataService));
                            // 添加业务处理器，处理接收到的消息
                            ch.pipeline().addLast("handler", new Dlt645Handler(dlt645DataService, dlt645DeviceService));
                            // 添加全局异常处理器，捕获并记录异常
                            ch.pipeline().addLast(new ChannelInboundHandlerAdapter() {
                                @Override
                                public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
                                    log.error("Channel exception: ", cause);
                                    ctx.close();
                                }
                            });
                        }
                    })
                    // 设置连接队列大小
                    .option(ChannelOption.SO_BACKLOG, 20480)
                    // 启用 TCP KeepAlive 机制
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    // 动态调整接收缓冲区大小
                    .childOption(ChannelOption.RCVBUF_ALLOCATOR, new AdaptiveRecvByteBufAllocator(64, 1024, 65536))
                    // 禁用 Nagle 算法，减少延迟
                    .childOption(ChannelOption.TCP_NODELAY, true)
                    // 允许地址重用
                    .childOption(ChannelOption.SO_REUSEADDR, true);
            // 绑定端口并启动服务器
            ChannelFuture future = bootstrap.bind(port).sync();
            log.info("TCP server started successfully on port: {}", port);
            // 等待服务器关闭
            future.channel().closeFuture().sync();
        } finally {
            // 优雅关闭线程组
            work.shutdownGracefully();
            boss.shutdownGracefully();
            log.info("TCP server shutdown gracefully.");
        }
    }
}
