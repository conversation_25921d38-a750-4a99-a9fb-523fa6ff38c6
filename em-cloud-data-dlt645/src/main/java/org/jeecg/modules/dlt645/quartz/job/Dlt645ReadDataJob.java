package org.jeecg.modules.dlt645.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.dlt645.service.Dlt645GatewayService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * DL/T645读数据任务
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@Component
public class Dlt645ReadDataJob implements Job {
    @Resource
    private Dlt645GatewayService dlt645GatewayService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        // DL/T645集抄数据
        dlt645GatewayService.batchSendReadDataMsg();
    }
}
