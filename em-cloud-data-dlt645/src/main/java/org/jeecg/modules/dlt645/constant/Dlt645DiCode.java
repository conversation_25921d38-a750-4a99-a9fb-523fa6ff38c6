package org.jeecg.modules.dlt645.constant;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * DLT645 数据项标识枚举
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@Getter
@AllArgsConstructor
public enum Dlt645DiCode {
    // (当前)组合有功总电能
    CURRENT_COMBINED_ACTIVE_TOTAL_ENERGY(new byte[]{0x00, 0x00, 0x00, 0x00}, "00000000", "当前组合有功总电能", "kW·h", 4, "######.##", "kWh Total", DataFormat.BCD),

    // (当前)组合有功费率1 电能
    CURRENT_COMBINED_ACTIVE_TARIFF_1_ENERGY(new byte[]{0x00, 0x00, 0x01, 0x00}, "00000100", "当前组合有功费率1电能", "kW·h", 4, "######.##", "PowerTariff1", DataFormat.BCD),

    // (当前)组合有功费率2 电能
    CURRENT_COMBINED_ACTIVE_TARIFF_2_ENERGY(new byte[]{0x00, 0x00, 0x02, 0x00}, "00000200", "当前组合有功费率2电能", "kW·h", 4, "######.##", "PowerTariff2", DataFormat.BCD),

    // (当前)组合有功费率3 电能
    CURRENT_COMBINED_ACTIVE_TARIFF_3_ENERGY(new byte[]{0x00, 0x00, 0x03, 0x00}, "00000300", "当前组合有功费率3电能", "kW·h", 4, "######.##", "PowerTariff3", DataFormat.BCD),

    // (当前)组合有功费率4 电能
    CURRENT_COMBINED_ACTIVE_TARIFF_4_ENERGY(new byte[]{0x00, 0x00, 0x04, 0x00}, "00000400", "当前组合有功费率4电能", "kW·h", 4, "######.##", "PowerTariff4", DataFormat.BCD),

    // (当前)组合有功费率5 电能
    CURRENT_COMBINED_ACTIVE_TARIFF_5_ENERGY(new byte[]{0x00, 0x00, 0x05, 0x00}, "00000500", "当前组合有功费率5电能", "kW·h", 4, "######.##", "PowerTariff5", DataFormat.BCD),

    // (当前)组合有功电能数据块
    CURRENT_COMBINED_ACTIVE_ENERGY_BLOCK(new byte[]{0x00, 0x00, (byte) 0xFF, 0x00}, "0000FF00", "当前组合有功电能数据块", "kW·h", 4, "######.##", "PowerBlock", DataFormat.BCD),

    // (当前)正向有功总电能
    CURRENT_FORWARD_ACTIVE_TOTAL_ENERGY(new byte[]{0x00, 0x01, 0x00, 0x00}, "00010000", "当前正向有功总电能", "kW·h", 4, "######.##", "kWh Import", DataFormat.BCD),

    // (当前)正向有功费率1 电能
    CURRENT_FORWARD_ACTIVE_TARIFF_1_ENERGY(new byte[]{0x00, 0x01, 0x01, 0x00}, "00010100", "当前正向有功费率1电能", "kW·h", 4, "######.##", "ForwardActiveTariff1", DataFormat.BCD),

    // (当前)正向有功费率2 电能
    CURRENT_FORWARD_ACTIVE_TARIFF_2_ENERGY(new byte[]{0x00, 0x01, 0x02, 0x00}, "00010200", "当前正向有功费率2电能", "kW·h", 4, "######.##", "ForwardActiveTariff2", DataFormat.BCD),

    // (当前)正向有功费率3 电能
    CURRENT_FORWARD_ACTIVE_TARIFF_3_ENERGY(new byte[]{0x00, 0x01, 0x03, 0x00}, "00010300", "当前正向有功费率3电能", "kW·h", 4, "######.##", "ForwardActiveTariff3", DataFormat.BCD),

    // (当前)正向有功费率4 电能
    CURRENT_FORWARD_ACTIVE_TARIFF_4_ENERGY(new byte[]{0x00, 0x01, 0x04, 0x00}, "00010400", "当前正向有功费率4电能", "kW·h", 4, "######.##", "ForwardActiveTariff4", DataFormat.BCD),

    // (当前)正向有功费率5 电能
    CURRENT_FORWARD_ACTIVE_TARIFF_5_ENERGY(new byte[]{0x00, 0x01, 0x05, 0x00}, "00010500", "当前正向有功费率5电能", "kW·h", 4, "######.##", "ForwardActiveTariff5", DataFormat.BCD),

    // (当前)正向有功电能数据块
    CURRENT_FORWARD_ACTIVE_ENERGY_BLOCK(new byte[]{0x00, 0x01, (byte) 0xFF, 0x00}, "0001FF00", "当前正向有功电能数据块", "kW·h", 4, "######.##", "ForwardActiveBlock", DataFormat.BCD),

    // (当前)反向有功总电能
    CURRENT_REVERSE_ACTIVE_TOTAL_ENERGY(new byte[]{0x00, 0x02, 0x00, 0x00}, "00020000", "当前反向有功总电能", "kW·h", 4, "######.##", "kWh Reverse Import", DataFormat.BCD),

    // (当前)反向有功费率1 电能
    CURRENT_REVERSE_ACTIVE_TARIFF_1_ENERGY(new byte[]{0x00, 0x02, 0x01, 0x00}, "00020100", "当前反向有功费率1电能", "kW·h", 4, "######.##", "ReverseActiveTariff1", DataFormat.BCD),

    // (当前)反向有功费率2 电能
    CURRENT_REVERSE_ACTIVE_TARIFF_2_ENERGY(new byte[]{0x00, 0x02, 0x02, 0x00}, "00020200", "当前反向有功费率2电能", "kW·h", 4, "######.##", "ReverseActiveTariff2", DataFormat.BCD),

    // (当前)反向有功费率3 电能
    CURRENT_REVERSE_ACTIVE_TARIFF_3_ENERGY(new byte[]{0x00, 0x02, 0x03, 0x00}, "00020300", "当前反向有功费率3电能", "kW·h", 4, "######.##", "ReverseActiveTariff3", DataFormat.BCD),

    // (当前)反向有功费率4 电能
    CURRENT_REVERSE_ACTIVE_TARIFF_4_ENERGY(new byte[]{0x00, 0x02, 0x04, 0x00}, "00020400", "当前反向有功费率4电能", "kW·h", 4, "######.##", "ReverseActiveTariff4", DataFormat.BCD),

    // (当前)反向有功费率5 电能
    CURRENT_REVERSE_ACTIVE_TARIFF_5_ENERGY(new byte[]{0x00, 0x02, 0x05, 0x00}, "00020500", "当前反向有功费率5电能", "kW·h", 4, "######.##", "ReverseActiveTariff5", DataFormat.BCD),

    // (当前)反向有功电能数据块
    CURRENT_REVERSE_ACTIVE_ENERGY_BLOCK(new byte[]{0x00, 0x02, (byte) 0xFF, 0x00}, "0002FF00", "当前反向有功电能数据块", "kW·h", 4, "######.##", "ReverseActiveBlock", DataFormat.BCD),

    // (当前)组合无功 1 总电能
    CURRENT_COMBINED_REACTIVE_1_TOTAL_ENERGY(new byte[]{0x00, 0x03, 0x00, 0x00}, "00030000", "当前组合无功 1 总电能", "kvarh", 4, "######.##", "CombinedReactive1Total", DataFormat.BCD),

    // (当前)组合无功 2 总电能
    CURRENT_COMBINED_REACTIVE_2_TOTAL_ENERGY(new byte[]{0x00, 0x04, 0x00, 0x00}, "00040000", "当前组合无功 2 总电能", "kvarh", 4, "######.##", "CombinedReactive2Total", DataFormat.BCD),

    // (当前)第一象限无功总电能
    CURRENT_QUADRANT_1_REACTIVE_TOTAL_ENERGY(new byte[]{0x00, 0x05, 0x00, 0x00}, "00050000", "当前第一象限无功总电能", "kvarh", 4, "######.##", "Quadrant1ReactiveTotal", DataFormat.BCD),

    // (当前)第二象限无功总电能
    CURRENT_QUADRANT_2_REACTIVE_TOTAL_ENERGY(new byte[]{0x00, 0x06, 0x00, 0x00}, "00060000", "当前第二象限无功总电能", "kvarh", 4, "######.##", "Quadrant2ReactiveTotal", DataFormat.BCD),

    // (当前)第三象限无功总电能
    CURRENT_QUADRANT_3_REACTIVE_TOTAL_ENERGY(new byte[]{0x00, 0x07, 0x00, 0x00}, "00070000", "当前第三象限无功总电能", "kvarh", 4, "######.##", "Quadrant3ReactiveTotal", DataFormat.BCD),

    // (当前)第四象限无功总电能
    CURRENT_QUADRANT_4_REACTIVE_TOTAL_ENERGY(new byte[]{0x00, 0x08, 0x00, 0x00}, "00080000", "当前第四象限无功总电能", "kvarh", 4, "######.##", "Quadrant4ReactiveTotal", DataFormat.BCD),

    // (当前)正向视在总电能
    CURRENT_FORWARD_APPARENT_TOTAL_ENERGY(new byte[]{0x00, 0x09, 0x00, 0x00}, "00090000", "当前正向视在总电能", "kVAh", 4, "######.##", "ForwardApparentTotal", DataFormat.BCD),

    // (当前)反向视在总电能
    CURRENT_REVERSE_APPARENT_TOTAL_ENERGY(new byte[]{0x00, 0x0A, 0x00, 0x00}, "000A0000", "当前反向视在总电能", "kVAh", 4, "######.##", "ReverseApparentTotal", DataFormat.BCD),

    // (上 1 结算日)正向有功总电能
    PREVIOUS_SETTLEMENT_DAY_FORWARD_ACTIVE_TOTAL_ENERGY(new byte[]{0x00, 0x01, 0x00, 0x01}, "00010001", "上 1 结算日正向有功总电能", "kW·h", 4, "######.##", "PrevSettlementDayForwardActiveTotal", DataFormat.BCD),

    // (上 1 结算日)正向有功费率1 电能
    PREVIOUS_SETTLEMENT_DAY_FORWARD_ACTIVE_TARIFF_1_ENERGY(new byte[]{0x00, 0x01, 0x01, 0x01}, "00010101", "上 1 结算日正向有功费率1电能", "kW·h", 4, "######.##", "PrevSettlementDayForwardActiveTariff1", DataFormat.BCD),

    // (上 1 结算日)正向有功费率2 电能
    PREVIOUS_SETTLEMENT_DAY_FORWARD_ACTIVE_TARIFF_2_ENERGY(new byte[]{0x00, 0x01, 0x02, 0x01}, "00010201", "上 1 结算日正向有功费率2电能", "kW·h", 4, "######.##", "PrevSettlementDayForwardActiveTariff2", DataFormat.BCD),

    // (上 1 结算日)正向有功费率3 电能
    PREVIOUS_SETTLEMENT_DAY_FORWARD_ACTIVE_TARIFF_3_ENERGY(new byte[]{0x00, 0x01, 0x03, 0x01}, "00010301", "上 1 结算日正向有功费率3电能", "kW·h", 4, "######.##", "PrevSettlementDayForwardActiveTariff3", DataFormat.BCD),

    // (上 1 结算日)正向有功费率4 电能
    PREVIOUS_SETTLEMENT_DAY_FORWARD_ACTIVE_TARIFF_4_ENERGY(new byte[]{0x00, 0x01, 0x04, 0x01}, "00010401", "上 1 结算日正向有功费率4电能", "kW·h", 4, "######.##", "PrevSettlementDayForwardActiveTariff4", DataFormat.BCD),

    // (上 1 结算日)正向有功费率5 电能
    PREVIOUS_SETTLEMENT_DAY_FORWARD_ACTIVE_TARIFF_5_ENERGY(new byte[]{0x00, 0x01, 0x05, 0x01}, "00010501", "上 1 结算日正向有功费率5电能", "kW·h", 4, "######.##", "PrevSettlementDayForwardActiveTariff5", DataFormat.BCD),

    // (上 1 结算日)正向有功电能数据块
    PREVIOUS_SETTLEMENT_DAY_FORWARD_ACTIVE_ENERGY_BLOCK(new byte[]{0x00, 0x01, (byte) 0xFF, 0x01}, "0001FF01", "上 1 结算日正向有功电能数据块", "kW·h", 4, "######.##", "PrevSettlementDayForwardActiveBlock", DataFormat.BCD),

    // (上 1 结算日)反向有功总电能
    PREVIOUS_SETTLEMENT_DAY_REVERSE_ACTIVE_TOTAL_ENERGY(new byte[]{0x00, 0x02, 0x00, 0x01}, "00020001", "上 1 结算日反向有功总电能", "kW·h", 4, "######.##", "PrevSettlementDayReverseActiveTotal", DataFormat.BCD),

    // (上 1 结算日)组合无功 1 总电能
    PREVIOUS_SETTLEMENT_DAY_COMBINED_REACTIVE_1_TOTAL_ENERGY(new byte[]{0x00, 0x03, 0x00, 0x01}, "00030001", "上 1 结算日组合无功 1 总电能", "kvarh", 4, "######.##", "PrevSettlementDayCombinedReactive1Total", DataFormat.BCD),

    // (上 1 结算日)组合无功 2 总电能
    PREVIOUS_SETTLEMENT_DAY_COMBINED_REACTIVE_2_TOTAL_ENERGY(new byte[]{0x00, 0x04, 0x00, 0x01}, "00040001", "上 1 结算日组合无功 2 总电能", "kvarh", 4, "######.##", "PrevSettlementDayCombinedReactive2Total", DataFormat.BCD),

    // 剩余金额
    REMAINING_AMOUNT(new byte[]{0x00, (byte) 0x90, 0x02, 0x00}, "00900200", "剩余金额", "元", 4, "######.##", "RemainingAmount", DataFormat.BCD),

    // 透支金额
    OVERDRAFT_AMOUNT(new byte[]{0x00, (byte) 0x90, 0x02, 0x01}, "00900201", "透支金额", "元", 4, "######.##", "OverdraftAmount", DataFormat.BCD),

    // 剩余免费金额
    REMAINING_FREE_AMOUNT(new byte[]{0x00, (byte) 0x90, 0x02, 0x20}, "00900220", "剩余免费金额", "元", 4, "######.##", "RemainingFreeAmount", DataFormat.BCD),

    // (当前)正向有功总最大需量及发生时间
    CURRENT_FORWARD_ACTIVE_TOTAL_MAX_DEMAND(new byte[]{0x01, 0x01, 0x00, 0x00}, "01010000", "当前正向有功总最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "CurrentForwardActiveTotalMaxDemand", DataFormat.BCD),

    // (当前)正向有功费率1 最大需量及发生时间
    CURRENT_FORWARD_ACTIVE_TARIFF_1_MAX_DEMAND(new byte[]{0x01, 0x01, 0x01, 0x00}, "01010100", "当前正向有功费率1 最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "CurrentForwardActiveTariff1MaxDemand", DataFormat.BCD),

    // (当前)正向有功费率2 最大需量及发生时间
    CURRENT_FORWARD_ACTIVE_TARIFF_2_MAX_DEMAND(new byte[]{0x01, 0x01, 0x02, 0x00}, "01010200", "当前正向有功费率2 最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "CurrentForwardActiveTariff2MaxDemand", DataFormat.BCD),

    // (当前)正向有功费率3 最大需量及发生时间
    CURRENT_FORWARD_ACTIVE_TARIFF_3_MAX_DEMAND(new byte[]{0x01, 0x01, 0x03, 0x00}, "01010300", "当前正向有功费率3 最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "CurrentForwardActiveTariff3MaxDemand", DataFormat.BCD),

    // (当前)正向有功费率4 最大需量及发生时间
    CURRENT_FORWARD_ACTIVE_TARIFF_4_MAX_DEMAND(new byte[]{0x01, 0x01, 0x04, 0x00}, "01010400", "当前正向有功费率4 最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "CurrentForwardActiveTariff4MaxDemand", DataFormat.BCD),

    // (当前)正向有功费率5 最大需量及发生时间
    CURRENT_FORWARD_ACTIVE_TARIFF_5_MAX_DEMAND(new byte[]{0x01, 0x01, 0x05, 0x00}, "01010500", "当前正向有功费率5 最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "CurrentForwardActiveTariff5MaxDemand", DataFormat.BCD),

    // (当前)正向有功最大需量及发生时间数据块
    CURRENT_FORWARD_ACTIVE_MAX_DEMAND_BLOCK(new byte[]{0x01, 0x01, (byte) 0xFF, 0x00}, "0101FF00", "当前正向有功最大需量及发生时间数据块", "kW", 8, "XX.XXXX YYMMDDhhmm", "CurrentForwardActiveMaxDemandBlock", DataFormat.BCD),

    // (当前)组合无功1 总最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_1_TOTAL_MAX_DEMAND(new byte[]{0x01, 0x03, 0x00, 0x00}, "01030000", "当前组合无功1 总最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive1TotalMaxDemand", DataFormat.BCD),

    // (当前)组合无功1 费率1 最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_1_TARIFF_1_MAX_DEMAND(new byte[]{0x01, 0x03, 0x01, 0x00}, "01030100", "当前组合无功1 费率1 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive1Tariff1MaxDemand", DataFormat.BCD),

    // (当前)组合无功1 费率2 最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_1_TARIFF_2_MAX_DEMAND(new byte[]{0x01, 0x03, 0x02, 0x00}, "01030200", "当前组合无功1 费率2 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive1Tariff2MaxDemand", DataFormat.BCD),

    // (当前)组合无功1 费率3 最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_1_TARIFF_3_MAX_DEMAND(new byte[]{0x01, 0x03, 0x03, 0x00}, "01030300", "当前组合无功1 费率3 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive1Tariff3MaxDemand", DataFormat.BCD),

    // (当前)组合无功1 费率4 最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_1_TARIFF_4_MAX_DEMAND(new byte[]{0x01, 0x03, 0x04, 0x00}, "01030400", "当前组合无功1 费率4 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive1Tariff4MaxDemand", DataFormat.BCD),

    // (当前)组合无功1 费率5 最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_1_TARIFF_5_MAX_DEMAND(new byte[]{0x01, 0x03, 0x05, 0x00}, "01030500", "当前组合无功1 费率5 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive1Tariff5MaxDemand", DataFormat.BCD),

    // (当前)组合无功1 最大需量及发生时间数据块
    CURRENT_COMBINED_REACTIVE_1_MAX_DEMAND_BLOCK(new byte[]{0x01, 0x03, (byte) 0xFF, 0x00}, "0103FF00", "当前组合无功1 最大需量及发生时间数据块", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive1MaxDemandBlock", DataFormat.BCD),

    // (当前)组合无功2 总最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_2_TOTAL_MAX_DEMAND(new byte[]{0x01, 0x04, 0x00, 0x00}, "01040000", "当前组合无功2 总最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive2TotalMaxDemand", DataFormat.BCD),

    // (当前)组合无功2 费率1 最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_2_TARIFF_1_MAX_DEMAND(new byte[]{0x01, 0x04, 0x01, 0x00}, "01040100", "当前组合无功2 费率1 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive2Tariff1MaxDemand", DataFormat.BCD),

    // (当前)组合无功2 费率2 最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_2_TARIFF_2_MAX_DEMAND(new byte[]{0x01, 0x04, 0x02, 0x00}, "01040200", "当前组合无功2 费率2 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive2Tariff2MaxDemand", DataFormat.BCD),

    // (当前)组合无功2 费率3 最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_2_TARIFF_3_MAX_DEMAND(new byte[]{0x01, 0x04, 0x03, 0x00}, "01040300", "当前组合无功2 费率3 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive2Tariff3MaxDemand", DataFormat.BCD),

    // (当前)组合无功2 费率4 最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_2_TARIFF_4_MAX_DEMAND(new byte[]{0x01, 0x04, 0x04, 0x00}, "01040400", "当前组合无功2 费率4 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive2Tariff4MaxDemand", DataFormat.BCD),

    // (当前)组合无功2 费率5 最大需量及发生时间
    CURRENT_COMBINED_REACTIVE_2_TARIFF_5_MAX_DEMAND(new byte[]{0x01, 0x04, 0x05, 0x00}, "01040500", "当前组合无功2 费率5 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive2Tariff5MaxDemand", DataFormat.BCD),

    // (当前)组合无功2 最大需量及发生时间数据块
    CURRENT_COMBINED_REACTIVE_2_MAX_DEMAND_BLOCK(new byte[]{0x01, 0x04, (byte) 0xFF, 0x00}, "0104FF00", "当前组合无功2 最大需量及发生时间数据块", "kvar", 8, "XX.XXXX YYMMDDhhmm", "CurrentCombinedReactive2MaxDemandBlock", DataFormat.BCD),

    // (上1 结算日)正向有功总最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_FORWARD_ACTIVE_TOTAL_MAX_DEMAND(new byte[]{0x01, 0x01, 0x00, 0x01}, "01010001", "上1 结算日正向有功总最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1ForwardActiveTotalMaxDemand", DataFormat.BCD),

    // (上1 结算日)正向有功费率1 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_FORWARD_ACTIVE_TARIFF_1_MAX_DEMAND(new byte[]{0x01, 0x01, 0x01, 0x01}, "01010101", "上1 结算日正向有功费率1 最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1ForwardActiveTariff1MaxDemand", DataFormat.BCD),

    // (上1 结算日)正向有功费率2 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_FORWARD_ACTIVE_TARIFF_2_MAX_DEMAND(new byte[]{0x01, 0x01, 0x02, 0x01}, "01010201", "上1 结算日正向有功费率2 最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1ForwardActiveTariff2MaxDemand", DataFormat.BCD),

    // (上1 结算日)正向有功费率3 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_FORWARD_ACTIVE_TARIFF_3_MAX_DEMAND(new byte[]{0x01, 0x01, 0x03, 0x01}, "01010301", "上1 结算日正向有功费率3 最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1ForwardActiveTariff3MaxDemand", DataFormat.BCD),

    // (上1 结算日)正向有功费率4 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_FORWARD_ACTIVE_TARIFF_4_MAX_DEMAND(new byte[]{0x01, 0x01, 0x04, 0x01}, "01010401", "上1 结算日正向有功费率4 最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1ForwardActiveTariff4MaxDemand", DataFormat.BCD),

    // (上1 结算日)正向有功费率5 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_FORWARD_ACTIVE_TARIFF_5_MAX_DEMAND(new byte[]{0x01, 0x01, 0x05, 0x01}, "01010501", "上1 结算日正向有功费率5 最大需量及发生时间", "kW", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1ForwardActiveTariff5MaxDemand", DataFormat.BCD),

    // (上1 结算日)正向有功最大需量及发生时间数据块
    PREVIOUS_SETTLEMENT_DAY_1_FORWARD_ACTIVE_MAX_DEMAND_BLOCK(new byte[]{0x01, 0x01, (byte) 0xFF, 0x01}, "0101FF01", "上1 结算日正向有功最大需量及发生时间数据块", "kW", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1ForwardActiveMaxDemandBlock", DataFormat.BCD),

    // (上1 结算日)组合无功1 总最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_1_TOTAL_MAX_DEMAND(new byte[]{0x01, 0x03, 0x00, 0x01}, "01030001", "上1 结算日组合无功1 总最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive1TotalMaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功1 费率1 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_1_TARIFF_1_MAX_DEMAND(new byte[]{0x01, 0x03, 0x01, 0x01}, "01030101", "上1 结算日组合无功1 费率1 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive1Tariff1MaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功1 费率2 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_1_TARIFF_2_MAX_DEMAND(new byte[]{0x01, 0x03, 0x02, 0x01}, "01030201", "上1 结算日组合无功1 费率2 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive1Tariff2MaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功1 费率3 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_1_TARIFF_3_MAX_DEMAND(new byte[]{0x01, 0x03, 0x03, 0x01}, "01030301", "上1 结算日组合无功1 费率3 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive1Tariff3MaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功1 费率4 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_1_TARIFF_4_MAX_DEMAND(new byte[]{0x01, 0x03, 0x04, 0x01}, "01030401", "上1 结算日组合无功1 费率4 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive1Tariff4MaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功1 费率5 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_1_TARIFF_5_MAX_DEMAND(new byte[]{0x01, 0x03, 0x05, 0x01}, "01030501", "上1 结算日组合无功1 费率5 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive1Tariff5MaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功1 最大需量及发生时间数据块
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_1_MAX_DEMAND_BLOCK(new byte[]{0x01, 0x03, (byte) 0xFF, 0x01}, "0103FF01", "上1 结算日组合无功1 最大需量及发生时间数据块", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive1MaxDemandBlock", DataFormat.BCD),

    // (上1 结算日)组合无功2 总最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_2_TOTAL_MAX_DEMAND(new byte[]{0x01, 0x04, 0x00, 0x01}, "01040001", "上1 结算日组合无功2 总最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive2TotalMaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功2 费率1 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_2_TARIFF_1_MAX_DEMAND(new byte[]{0x01, 0x04, 0x01, 0x01}, "01040101", "上1 结算日组合无功2 费率1 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive2Tariff1MaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功2 费率2 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_2_TARIFF_2_MAX_DEMAND(new byte[]{0x01, 0x04, 0x02, 0x01}, "01040201", "上1 结算日组合无功2 费率2 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive2Tariff2MaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功2 费率3 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_2_TARIFF_3_MAX_DEMAND(new byte[]{0x01, 0x04, 0x03, 0x01}, "01040301", "上1 结算日组合无功2 费率3 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive2Tariff3MaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功2 费率4 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_2_TARIFF_4_MAX_DEMAND(new byte[]{0x01, 0x04, 0x04, 0x01}, "01040401", "上1 结算日组合无功2 费率4 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive2Tariff4MaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功2 费率5 最大需量及发生时间
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_2_TARIFF_5_MAX_DEMAND(new byte[]{0x01, 0x04, 0x05, 0x01}, "01040501", "上1 结算日组合无功2 费率5 最大需量及发生时间", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive2Tariff5MaxDemand", DataFormat.BCD),

    // (上1 结算日)组合无功2 最大需量及发生时间数据块
    PREVIOUS_SETTLEMENT_DAY_1_COMBINED_REACTIVE_2_MAX_DEMAND_BLOCK(new byte[]{0x01, 0x04, (byte) 0xFF, 0x01}, "0104FF01", "上1 结算日组合无功2 最大需量及发生时间数据块", "kvar", 8, "XX.XXXX YYMMDDhhmm", "PreviousSettlementDay1CombinedReactive2MaxDemandBlock", DataFormat.BCD),

    // A 相电压
    A_PHASE_VOLTAGE(new byte[]{0x02, 0x01, 0x01, 0x00}, "02010100", "A 相电压", "V", 2, "###.#", "Ua", DataFormat.BCD),

    // B 相电压
    B_PHASE_VOLTAGE(new byte[]{0x02, 0x01, 0x02, 0x00}, "02010200", "B 相电压", "V", 2, "###.#", "Ub", DataFormat.BCD),

    // C 相电压
    C_PHASE_VOLTAGE(new byte[]{0x02, 0x01, 0x03, 0x00}, "02010300", "C 相电压", "V", 2, "###.#", "Uc", DataFormat.BCD),

    // 电压数据块
    VOLTAGE_DATA_BLOCK(new byte[]{0x02, 0x01, (byte) 0xFF, 0x00}, "0201FF00", "电压数据块", "V", 2, "####.#", "VoltageDataBlock", DataFormat.BCD),

    // A 相电流
    A_PHASE_CURRENT(new byte[]{0x02, 0x02, 0x01, 0x00}, "02020100", "A 相电流", "A", 3, "###.###", "Ia", DataFormat.BCD),

    // B 相电流
    B_PHASE_CURRENT(new byte[]{0x02, 0x02, 0x02, 0x00}, "02020200", "B 相电流", "A", 3, "###.###", "Ib", DataFormat.BCD),

    // C 相电流
    C_PHASE_CURRENT(new byte[]{0x02, 0x02, 0x03, 0x00}, "02020300", "C 相电流", "A", 3, "###.###", "Ic", DataFormat.BCD),

    // 电流数据块
    CURRENT_DATA_BLOCK(new byte[]{0x02, 0x02, (byte) 0xFF, 0x00}, "0202FF00", "电流数据块", "A", 3, "###.###", "CurrentDataBlock", DataFormat.BCD),

    // 瞬时总有功功率
    INSTANTANEOUS_TOTAL_ACTIVE_POWER(new byte[]{0x02, 0x03, 0x00, 0x00}, "02030000", "瞬时总有功功率", "kW", 4, "######.##", "InstantaneousTotalActivePower", DataFormat.BCD),

    // 瞬时 A 相有功功率
    INSTANTANEOUS_A_PHASE_ACTIVE_POWER(new byte[]{0x02, 0x03, 0x01, 0x00}, "02030100", "瞬时 A 相有功功率", "kW", 4, "######.##", "InstantaneousAPhaseActivePower", DataFormat.BCD),

    // 瞬时 B 相有功功率
    INSTANTANEOUS_B_PHASE_ACTIVE_POWER(new byte[]{0x02, 0x03, 0x02, 0x00}, "02030200", "瞬时 B 相有功功率", "kW", 4, "######.##", "InstantaneousBPhaseActivePower", DataFormat.BCD),

    // 瞬时 C 相有功功率
    INSTANTANEOUS_C_PHASE_ACTIVE_POWER(new byte[]{0x02, 0x03, 0x03, 0x00}, "02030300", "瞬时 C 相有功功率", "kW", 4, "######.##", "InstantaneousCPhaseActivePower", DataFormat.BCD),

    // 瞬时有功功率数据块
    INSTANTANEOUS_ACTIVE_POWER_DATA_BLOCK(new byte[]{0x02, 0x03, (byte) 0xFF, 0x00}, "0203FF00", "瞬时有功功率数据块", "kW", 12, "######.##", "InstantaneousActivePowerDataBlock", DataFormat.BCD),

    // 瞬时总无功功率
    INSTANTANEOUS_TOTAL_REACTIVE_POWER(new byte[]{0x02, 0x04, 0x00, 0x00}, "02040000", "瞬时总无功功率", "kvar", 4, "######.##", "InstantaneousTotalReactivePower", DataFormat.BCD),

    // 瞬时 A 相无功功率
    INSTANTANEOUS_A_PHASE_REACTIVE_POWER(new byte[]{0x02, 0x04, 0x01, 0x00}, "02040100", "瞬时 A 相无功功率", "kvar", 4, "######.##", "InstantaneousAPhaseReactivePower", DataFormat.BCD),

    // 瞬时 B 相无功功率
    INSTANTANEOUS_B_PHASE_REACTIVE_POWER(new byte[]{0x02, 0x04, 0x02, 0x00}, "02040200", "瞬时 B 相无功功率", "kvar", 4, "######.##", "InstantaneousBPhaseReactivePower", DataFormat.BCD),

    // 瞬时 C 相无功功率
    INSTANTANEOUS_C_PHASE_REACTIVE_POWER(new byte[]{0x02, 0x04, 0x03, 0x00}, "02040300", "瞬时 C 相无功功率", "kvar", 4, "######.##", "InstantaneousCPhaseReactivePower", DataFormat.BCD),

    // 瞬时无功功率数据块
    INSTANTANEOUS_REACTIVE_POWER_DATA_BLOCK(new byte[]{0x02, 0x04, (byte) 0xFF, 0x00}, "0204FF00", "瞬时无功功率数据块", "kvar", 12, "######.##", "InstantaneousReactivePowerDataBlock", DataFormat.BCD),

    // 瞬时总视在功率
    INSTANTANEOUS_TOTAL_APPARENT_POWER(new byte[]{0x02, 0x05, 0x00, 0x00}, "02050000", "瞬时总视在功率", "kVA", 3, "##.####", "kVA Total", DataFormat.BCD),

    // 瞬时 A 相视在功率
    INSTANTANEOUS_A_PHASE_APPARENT_POWER(new byte[]{0x02, 0x05, 0x01, 0x00}, "02050100", "瞬时 A 相视在功率", "kVA", 4, "######.##", "InstantaneousAPhaseApparentPower", DataFormat.BCD),

    // 瞬时 B 相视在功率
    INSTANTANEOUS_B_PHASE_APPARENT_POWER(new byte[]{0x02, 0x05, 0x02, 0x00}, "02050200", "瞬时 B 相视在功率", "kVA", 4, "######.##", "InstantaneousBPhaseApparentPower", DataFormat.BCD),

    // 瞬时 C 相视在功率
    INSTANTANEOUS_C_PHASE_APPARENT_POWER(new byte[]{0x02, 0x05, 0x03, 0x00}, "02050300", "瞬时 C 相视在功率", "kVA", 4, "######.##", "InstantaneousCPhaseApparentPower", DataFormat.BCD),

    // 瞬时视在功率数据块
    INSTANTANEOUS_APPARENT_POWER_DATA_BLOCK(new byte[]{0x02, 0x05, (byte) 0xFF, 0x00}, "0205FF00", "瞬时视在功率数据块", "kVA", 12, "######.##", "InstantaneousApparentPowerDataBlock", DataFormat.BCD),

    // 总功率因数
    TOTAL_POWER_FACTOR(new byte[]{0x02, 0x06, 0x00, 0x00}, "02060000", "总功率因数", "", 2, "#.###", "PF", DataFormat.BCD),

    // A 相功率因数
    A_PHASE_POWER_FACTOR(new byte[]{0x02, 0x06, 0x01, 0x00}, "02060100", "A 相功率因数", "", 2, "##.##", "APhasePowerFactor", DataFormat.BCD),

    // B 相功率因数
    B_PHASE_POWER_FACTOR(new byte[]{0x02, 0x06, 0x02, 0x00}, "02060200", "B 相功率因数", "", 2, "##.##", "BPhasePowerFactor", DataFormat.BCD),

    // C 相功率因数
    C_PHASE_POWER_FACTOR(new byte[]{0x02, 0x06, 0x03, 0x00}, "02060300", "C 相功率因数", "", 2, "##.##", "CPhasePowerFactor", DataFormat.BCD),

    // 功率因数数据块
    POWER_FACTOR_DATA_BLOCK(new byte[]{0x02, 0x06, (byte) 0xFF, 0x00}, "0206FF00", "功率因数数据块", "", 6, "##.##", "PowerFactorDataBlock", DataFormat.BCD),

    // A 相电压波形失真度
    A_PHASE_VOLTAGE_THD(new byte[]{0x02, 0x08, 0x01, 0x00}, "02080100", "A 相电压波形失真度", "%", 2, "##.##", "APhaseVoltageTHD", DataFormat.BCD),

    // B 相电压波形失真度
    B_PHASE_VOLTAGE_THD(new byte[]{0x02, 0x08, 0x02, 0x00}, "02080200", "B 相电压波形失真度", "%", 2, "##.##", "BPhaseVoltageTHD", DataFormat.BCD),

    // C 相电压波形失真度
    C_PHASE_VOLTAGE_THD(new byte[]{0x02, 0x08, 0x03, 0x00}, "02080300", "C 相电压波形失真度", "%", 2, "##.##", "CPhaseVoltageTHD", DataFormat.BCD),

    // 电压波形失真度数据块
    VOLTAGE_THD_DATA_BLOCK(new byte[]{0x02, 0x08, (byte) 0xFF, 0x00}, "0208FF00", "电压波形失真度数据块", "%", 6, "######.##", "VoltageTHDDataBlock", DataFormat.BCD),

    // A 相电流波形失真度
    A_PHASE_CURRENT_THD(new byte[]{0x02, 0x09, 0x01, 0x00}, "02090100", "A 相电流波形失真度", "%", 2, "##.##", "APhaseCurrentTHD", DataFormat.BCD),

    // B 相电流波形失真度
    B_PHASE_CURRENT_THD(new byte[]{0x02, 0x09, 0x02, 0x00}, "02090200", "B 相电流波形失真度", "%", 2, "##.##", "BPhaseCurrentTHD", DataFormat.BCD),

    // C 相电流波形失真度
    C_PHASE_CURRENT_THD(new byte[]{0x02, 0x09, 0x03, 0x00}, "02090300", "C 相电流波形失真度", "%", 2, "##.##", "CPhaseCurrentTHD", DataFormat.BCD),

    // 电流波形失真度数据块
    CURRENT_THD_DATA_BLOCK(new byte[]{0x02, 0x09, (byte) 0xFF, 0x00}, "0209FF00", "电流波形失真度数据块", "%", 6, "######.##", "CurrentTHDDataBlock", DataFormat.BCD),

    // A 相电压奇次谐波含量
    A_PHASE_VOLTAGE_ODD_HARMONIC(new byte[]{0x02, 0x0C, 0x01, 0x01}, "020C0101", "A 相电压奇次谐波含量", "%", 2, "##.##", "APhaseVoltageOddHarmonic", DataFormat.BCD),

    // B 相电压奇次谐波含量
    B_PHASE_VOLTAGE_ODD_HARMONIC(new byte[]{0x02, 0x0C, 0x02, 0x01}, "020C0201", "B 相电压奇次谐波含量", "%", 2, "##.##", "BPhaseVoltageOddHarmonic", DataFormat.BCD),

    // C 相电压奇次谐波含量
    C_PHASE_VOLTAGE_ODD_HARMONIC(new byte[]{0x02, 0x0C, 0x03, 0x01}, "020C0301", "C 相电压奇次谐波含量", "%", 2, "##.##", "CPhaseVoltageOddHarmonic", DataFormat.BCD),

    // 电压奇次谐波含量数据块
    VOLTAGE_ODD_HARMONIC_DATA_BLOCK(new byte[]{0x02, 0x0C, (byte) 0xFF, 0x01}, "020CFF01", "电压奇次谐波含量数据块", "%", 6, "######.##", "VoltageOddHarmonicDataBlock", DataFormat.BCD),

    // A 相电压偶次谐波含量
    A_PHASE_VOLTAGE_EVEN_HARMONIC(new byte[]{0x02, 0x0C, 0x01, 0x02}, "020C0102", "A 相电压偶次谐波含量", "%", 2, "##.##", "APhaseVoltageEvenHarmonic", DataFormat.BCD),

    // B 相电压偶次谐波含量
    B_PHASE_VOLTAGE_EVEN_HARMONIC(new byte[]{0x02, 0x0C, 0x02, 0x02}, "020C0202", "B 相电压偶次谐波含量", "%", 2, "##.##", "BPhaseVoltageEvenHarmonic", DataFormat.BCD),

    // C 相电压偶次谐波含量
    C_PHASE_VOLTAGE_EVEN_HARMONIC(new byte[]{0x02, 0x0C, 0x03, 0x02}, "020C0302", "C 相电压偶次谐波含量", "%", 2, "##.##", "CPhaseVoltageEvenHarmonic", DataFormat.BCD),

    // 电压偶次谐波含量数据块
    VOLTAGE_EVEN_HARMONIC_DATA_BLOCK(new byte[]{0x02, 0x0C, (byte) 0xFF, 0x02}, "020CFF02", "电压偶次谐波含量数据块", "%", 6, "######.##", "VoltageEvenHarmonicDataBlock", DataFormat.BCD),

    // A 相电流奇次谐波含量
    A_PHASE_CURRENT_ODD_HARMONIC(new byte[]{0x02, 0x0D, 0x01, 0x01}, "020D0101", "A 相电流奇次谐波含量", "%", 2, "##.##", "APhaseCurrentOddHarmonic", DataFormat.BCD),

    // B 相电流奇次谐波含量
    B_PHASE_CURRENT_ODD_HARMONIC(new byte[]{0x02, 0x0D, 0x02, 0x01}, "020D0201", "B 相电流奇次谐波含量", "%", 2, "##.##", "BPhaseCurrentOddHarmonic", DataFormat.BCD),

    // C 相电流奇次谐波含量
    C_PHASE_CURRENT_ODD_HARMONIC(new byte[]{0x02, 0x0D, 0x03, 0x01}, "020D0301", "C 相电流奇次谐波含量", "%", 2, "##.##", "CPhaseCurrentOddHarmonic", DataFormat.BCD),

    // 电流奇次谐波含量数据块
    CURRENT_ODD_HARMONIC_DATA_BLOCK(new byte[]{0x02, 0x0D, (byte) 0xFF, 0x01}, "020DFF01", "电流奇次谐波含量数据块", "%", 6, "######.##", "CurrentOddHarmonicDataBlock", DataFormat.BCD),

    // A 相电流偶次谐波含量
    A_PHASE_CURRENT_EVEN_HARMONIC(new byte[]{0x02, 0x0D, 0x01, 0x02}, "020D0102", "A 相电流偶次谐波含量", "%", 2, "##.##", "APhaseCurrentEvenHarmonic", DataFormat.BCD),

    // B 相电流偶次谐波含量
    B_PHASE_CURRENT_EVEN_HARMONIC(new byte[]{0x02, 0x0D, 0x02, 0x02}, "020D0202", "B 相电流偶次谐波含量", "%", 2, "##.##", "BPhaseCurrentEvenHarmonic", DataFormat.BCD),

    // C 相电流偶次谐波含量
    C_PHASE_CURRENT_EVEN_HARMONIC(new byte[]{0x02, 0x0D, 0x03, 0x02}, "020D0302", "C 相电流偶次谐波含量", "%", 2, "##.##", "CPhaseCurrentEvenHarmonic", DataFormat.BCD),

    // 电流偶次谐波含量数据块
    CURRENT_EVEN_HARMONIC_DATA_BLOCK(new byte[]{0x02, 0x0D, (byte) 0xFF, 0x02}, "020DFF02", "电流偶次谐波含量数据块", "%", 6, "######.##", "CurrentEvenHarmonicDataBlock", DataFormat.BCD),

    // 零线电流
    NEUTRAL_CURRENT(new byte[]{0x02, (byte) 0x80, 0x00, 0x01}, "02800001", "零线电流", "A", 2, "###.###", "NeutralCurrent", DataFormat.BCD),

    // 电网频率
    GRID_FREQUENCY(new byte[]{0x02, (byte) 0x80, 0x00, 0x02}, "02800002", "电网频率", "Hz", 2, "##.##", "GridFrequency", DataFormat.BCD),

    // 当前有功需量
    CURRENT_ACTIVE_DEMAND(new byte[]{0x02, (byte) 0x80, 0x00, 0x04}, "02800004", "当前有功需量", "kW", 4, "##.####", "CurrentActiveDemand", DataFormat.BCD),

    // 当前无功需量
    CURRENT_REACTIVE_DEMAND(new byte[]{0x02, (byte) 0x80, 0x01, 0x05}, "02800105", "当前无功需量", "kvar", 4, "##.####", "CurrentReactiveDemand", DataFormat.BCD),

    // 当前视在需量
    CURRENT_APPARENT_DEMAND(new byte[]{0x02, (byte) 0x80, 0x02, 0x06}, "02800206", "当前视在需量", "kVA", 4, "##.####", "CurrentApparentDemand", DataFormat.BCD),

    // 当前电价
    CURRENT_ELECTRICITY_PRICE(new byte[]{0x02, (byte) 0x80, 0x00, 0x20}, "02800020", "当前电价", "元/kWh", 4, "####.####", "CurrentElectricityPrice", DataFormat.BCD),

    // 电压不平衡度
    VOLTAGE_UNBALANCE(new byte[]{0x02, (byte) 0x90, 0x00, 0x01}, "02900001", "电压不平衡度", "%", 2, "##.##", "VoltageUnbalance", DataFormat.BCD),

    // 电流不平衡度
    CURRENT_UNBALANCE(new byte[]{0x02, (byte) 0x90, 0x01, 0x02}, "02900102", "电流不平衡度", "%", 2, "##.##", "CurrentUnbalance", DataFormat.BCD),

    // 通信地址
    COMMUNICATION_ADDRESS(new byte[]{0x04, 0x00, 0x04, 0x01}, "04000401", "通信地址", "", 6, "############", "address", DataFormat.BCD),

    // 表号
    METER_NUMBER(new byte[]{0x04, 0x00, 0x04, 0x02}, "04000402", "表号", "", 6, "############", "meter_number", DataFormat.BCD),

    // 额定电压
    RATED_VOLTAGE(new byte[]{0x04, 0x00, 0x04, 0x04}, "04000404", "额定电压", "", 6, "############", "rated_voltage", DataFormat.ASCII),

    // 额定电流/基本电流
    RATED_CURRENT(new byte[]{0x04, 0x00, 0x04, 0x05}, "04000405", "额定电流/基本电流", "", 6, "############", "rated_current", DataFormat.ASCII),

    // 最大电流
    MAXIMUM_CURRENT(new byte[]{0x04, 0x00, 0x04, 0x06}, "04000406", "最大电流", "A", 6, "############", "max_current", DataFormat.ASCII),

    // 有功准确度等级
    ACTIVE_ACCURACY_CLASS(new byte[]{0x04, 0x00, 0x04, 0x07}, "04000407", "有功准确度等级", "imp/kWh", 4, "########", "accuracy_class", DataFormat.ASCII),

    // 电表有功常数
    ACTIVE_ENERGY_METER_CONSTANT(new byte[]{0x04, 0x00, 0x04, 0x09}, "04000409", "电表有功常数", "imp/kWh", 3, "######", "active_energy_meter_constant", DataFormat.ASCII),

    // 电表型号
    METER_MODEL(new byte[]{0x04, 0x00, 0x04, 0x0B}, "0400040B", "电表型号", "", 10, "10位ASCII", "meter_model", DataFormat.ASCII),

    // 生产日期
    MANUFACTURE_DATE(new byte[]{0x04, 0x00, 0x04, 0x0C}, "0400040C", "生产日期", "", 10, "YYYYMMDD", "manu_date", DataFormat.ASCII),

    // 协议版本号
    PROTOCOL_VERSION(new byte[]{0x04, 0x00, 0x04, 0x0D}, "0400040D", "协议版本号", "", 16, "16位ASCII", "protocol_version", DataFormat.ASCII),

    // 客户编码
    CUSTOMER_CODE(new byte[]{0x04, 0x00, 0x04, 0x0E}, "0400040E", "客户编码", "", 6, "############", "customer_code", DataFormat.BCD),

    // 序列号(SN)
    SERIAL_NUMBER(new byte[]{0x04, 0x00, 0x04, (byte) 0x80}, "04000480", "序列号(SN)", "", 5, "#####", "sn", DataFormat.BCD),

    // 电表运行状态字3（Hex 码）--实时值
    METER_STATUS_WORD_3(new byte[]{0x04, 0x00, 0x05, 0x03}, "04000503", "电表运行状态字3", "", 2, "####", "OpenOrClose", DataFormat.HEX),

    // 电表运行状态字4（Hex 码）--实时值
    METER_STATUS_WORD_4(new byte[]{0x04, 0x00, 0x05, 0x04}, "04000504", "电表运行状态字4", "", 2, "####", "MeterStatusWord4", DataFormat.HEX),

    // 电表运行状态字5（Hex 码）--实时值
    METER_STATUS_WORD_5(new byte[]{0x04, 0x00, 0x05, 0x05}, "04000505", "电表运行状态字5", "", 2, "####", "MeterStatusWord5", DataFormat.HEX),

    // 电表运行状态字6（Hex 码）--实时值
    METER_STATUS_WORD_6(new byte[]{0x04, 0x00, 0x05, 0x06}, "04000506", "电表运行状态字6", "", 2, "####", "MeterStatusWord6", DataFormat.HEX),

    // 电表运行状态字7（Hex 码）--实时值
    METER_STATUS_WORD_7(new byte[]{0x04, 0x00, 0x05, 0x07}, "04000507", "电表运行状态字7", "", 2, "####", "MeterStatusWord7", DataFormat.HEX),

    // 电表跳闸状态字（Hex 码）--实时值
    METER_TRIP_STATUS_WORD(new byte[]{0x04, 0x00, 0x05, (byte) 0x80}, "04000580", "电表跳闸状态字", "", 2, "####", "MeterTripStatusWord", DataFormat.HEX),

    // 4G 信号强度
    DATA_NETWORK_SIGNAL_STRENGTH(new byte[]{0x02, (byte) 0x80, 0x00, (byte) 0x21}, "02800021", "4G 信号强度", "", 2, "####", "SignalStrength", DataFormat.BCD);

    /**
     * 数据项的唯一标识符字节数组形式，用于与电表通信时识别特定的数据项。
     */
    private final byte[] diByte;
    /**
     * 数据项的唯一标识符的字符串形式。
     */
    private final String di;
    /**
     * 数据项的描述信息，便于理解和调试。
     */
    private final String description;
    /**
     * 数据项的单位，例如 kW·h、kvarh、kVAh 等。
     */
    private final String unit;
    /**
     * 数据项的长度（字节数），表示该数据项占用的字节数。
     */
    private final int length;
    /**
     * 数据项的格式字符串，用于显示或解析数据项的值。
     */
    private final String format;
    /**
     * 数据项的代码名称，用于系统内部引用和映射。
     */
    private final String code;
    /**
     * 数据项的数据格式，例如 BCD、HEX、ASCII 等。
     */
    private final DataFormat dataFormat;

    /**
     * 根据提供的 di 获取对应的数据项格式字符串。
     *
     * @param di 数据项的唯一标识符
     * @return 数据项的格式字符串；如果未找到返回 null
     */
    public static String getFormat(String di) {
        for (Dlt645DiCode item : values()) {
            if (StrUtil.equals(item.di, di)) {
                return item.format;
            }
        }
        return null;
    }

    /**
     * 根据提供的 di 获取对应的数据项代码名称。
     *
     * @param di 数据项的唯一标识符
     * @return 数据项的代码名称；如果未找到返回 null
     */
    public static String getCode(byte[] di) {
        for (Dlt645DiCode item : values()) {
            if (Arrays.equals(item.diByte, di)) {
                return item.code;
            }
        }
        return null;
    }

    /**
     * 根据提供的 di 获取对应的 Dlt645DiCode 对象。
     *
     * @param di 数据项的唯一标识符
     * @return 对应的 Dlt645DiCode 对象；如果未找到返回 null
     */
    public static Dlt645DiCode getByDi(String di) {
        for (Dlt645DiCode item : values()) {
            if (StrUtil.equals(item.di, di)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据提供的 diString 获取对应的 Dlt645DiCode 对象。
     *
     * @param diString 数据项的唯一标识符的字符串形式
     * @return 对应的 Dlt645DiCode 对象；如果未找到返回 null
     */
    public static Dlt645DiCode getByDiString(String diString) {
        for (Dlt645DiCode item : values()) {
            if (item.di.equals(diString)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据提供的 diCode 获取对应的 Dlt645DiCode 对象。
     *
     * @param diCode 数据项的代码名称
     * @return 对应的 Dlt645DiCode 对象；如果未找到返回 null
     */
    public static Dlt645DiCode getByDiCode(String diCode) {
        for (Dlt645DiCode item : values()) {
            if (StrUtil.equals(item.code, diCode)) {
                return item;
            }
        }
        return null;
    }
}
