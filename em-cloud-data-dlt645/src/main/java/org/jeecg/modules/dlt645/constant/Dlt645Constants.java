package org.jeecg.modules.dlt645.constant;

/**
 * 帧常量
 *
 * <AUTHOR>
 * @date 2024-12-24 16:04
 */
public final class Dlt645Constants {
    /**
     * 私有构造方法，防止实例化
     */
    private Dlt645Constants() {}

    /**
     * 协议版本
     */
    public static final String PROTOCOL_VERSION = "1.7";

    /**
     * 密钥
     */
    public static final String P_KEY = "";

    /**
     * 注册包长度
     */
    public static final int REGISTER_PACKET_LENGTH = 5;

    /**
     * 心跳包长度
     */
    public static final int HEARTBEAT_PACKET_LENGTH = 1;

    /**
     * 帧起始符 68H
     */
    public static final byte FRAME_HEADER = 0x68;

    /**
     * 结束符 16H
     */
    public static final byte FRAME_TAIL = 0x16;

    /**
     * 偏移 33H
     */
    public static final byte FRAME_OFFSET = 0x33;

    /**
     * 用于按位与操作的掩码
     */
    public static final int MASK_BYTE = 0xFF;

    /**
     * 设置最大帧大小以防止恶意输入
     */
    public static final int MAX_FRAME_SIZE = 256;

    /**
     * 长度不足补足
     */
    public static final byte ZERO_BYTE = 0x00;

    /**
     * 校验BCD有效值
     */
    public static final byte VALID_BCD = 0x0F;

    /**
     * 前导字节 FEH（4 个字节）
     */
    public static final byte[] PREAMBLE_BYTE = new byte[]{(byte) 0xFE, (byte) 0xFE, (byte) 0xFE, (byte) 0xFE};

    /**
     * 默认密码
     */
    public static final byte[] DEFAULT_PASSWORD = new byte[]{(byte) 0x02, (byte) 0x02, (byte) 0x02, (byte) 0x02};

    /**
     * 默认操作者代码
     */
    public static final byte[] DEFAULT_USERNAME = new byte[]{(byte) 0x01, (byte) 0x01, (byte) 0x01, (byte) 0x01};

    /**
     * 广播校时地址
     */
    public static final String BROADCAST_DATETIME_ADDRESS = "999999999999";

    /**
     * 通讯地址地址域
     */
    public static final String BROADCAST_ADDRESS = "AAAAAAAAAAAA";

    /**
     * 时间格式
     */
    public static final String DATETIME_PATTERN = "YYMMDDHHmmss";

    /**
     * 延迟秒
     */
    public static final long DELAY_SECONDS = 2000;

    /**
     * 上报数据周期
     */
    public static final int REPORT_PERIOD = 5;

    /**
     * 过期时间（秒）
     */
    public static final long EXPIRED_TIME = 60;

    /**
     * EMQX消息交换机
     */
    public static final String EMQX_EXCHANGE = "emqx.exchange";

    /**
     * EMQX发布消息
     */
    public static final String MESSAGE_PUBLISH = "emqx.message.publish";

    /**
     * MQ主题
     */
    public static final String MQ_TOPIC = "/edge/{{platform}}/{{pKey}}/{{sn}}/rtg";
}
