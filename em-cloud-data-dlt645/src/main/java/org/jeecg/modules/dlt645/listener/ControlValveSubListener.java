package org.jeecg.modules.dlt645.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.iot.mqtt.codec.MqttQoS;
import net.dreamlu.iot.mqtt.spring.client.MqttClientSubscribe;
import org.jeecg.modules.dlt645.exception.Dlt645Exception;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ControlValveDTO;
import org.jeecg.modules.dlt645.service.Dlt645GatewayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 阀控监听器
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Slf4j
@Component
public class ControlValveSubListener {
    @Autowired
    private Dlt645GatewayService dlt645GatewayService;

    /**
     * 数据订阅
     *
     * @param topic
     * @param payload
     */
    @MqttClientSubscribe(value = "/4g/cloud/${pKey}/${sn}/cmd/set", qos = MqttQoS.QOS2)
    public void subCmdSet(String topic, byte[] payload) throws Dlt645Exception {
        String controlValvePayload = new String(payload, StandardCharsets.UTF_8);
        log.info("topic:{} payload:{}", topic, controlValvePayload);
        // 解析数据
        Dlt645ControlValveDTO controlValveDTO = JSON.parseObject(controlValvePayload, Dlt645ControlValveDTO.class);
        // 下发指令
        dlt645GatewayService.sendControlValve(controlValveDTO);
    }
}
