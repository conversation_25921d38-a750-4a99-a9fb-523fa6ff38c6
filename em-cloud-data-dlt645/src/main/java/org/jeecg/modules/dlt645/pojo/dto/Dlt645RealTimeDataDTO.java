package org.jeecg.modules.dlt645.pojo.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * DL/T645实时数据DTO
 *
 * <AUTHOR>
 * @date 2025-01-13 11:04
 */
@Data
public class Dlt645RealTimeDataDTO implements Serializable {
    private static final long serialVersionUID = 7761695817067322881L;
    /**
     * 协议版本
     */
    private String ver;
    /**
     * 供应商产品系列编号
     */
    private String pKey;
    /**
     * 物联网关编号
     */
    private String sn;
    /**
     * 包类型为 rtg，其它字段和上报实时数据一致，数量比较大时，可以分包传输。
     */
    private String type;
    /**
     * 缺省表示自动上报，有帧序号表示召读消息
     */
    private long seq;
    /**
     * 如果有这个字段，表示召读指定的设备实时数据，如果没有，表示召读所有设备的数据。
     */
    private String dev;
}
