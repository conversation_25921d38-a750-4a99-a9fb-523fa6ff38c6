package org.jeecg.modules.dlt645.constant;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * DLT645任务
 *
 * <AUTHOR>
 * @date 2025-01-17 16:31
 */
@Data
@AllArgsConstructor
public class Dlt645PrioritizedTask implements Runnable {
    /**
     * 任务
     */
    private final Runnable task;
    /**
     * 任务优先级
     */
    private final Dlt645TaskPriority priority;

    @Override
    public void run() {
        task.run();
    }
}
