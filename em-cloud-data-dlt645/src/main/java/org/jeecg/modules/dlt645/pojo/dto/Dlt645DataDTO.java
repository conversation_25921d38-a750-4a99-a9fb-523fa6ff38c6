package org.jeecg.modules.dlt645.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.jeecg.modules.dlt645.config.DateToSecondsSerializer;
import org.jeecg.modules.mongo.converter.DataDateTimeValueConverter;
import org.springframework.data.convert.ValueConverter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * DL/T645数据DTO
 *
 * <AUTHOR>
 * @date 2025-01-09 11:19
 */
@Data
public class Dlt645DataDTO implements Serializable {
    private static final long serialVersionUID = 3736348079830998448L;
    /**
     * 版本
     */
    private String ver;
    /**
     * 供应商产品系列编号
     */
    private String pKey;
    /**
     * 序列号
     */
    private String sn;
    /**
     * 时间戳
     */
    @ValueConverter(value = DataDateTimeValueConverter.class)
    @JSONField(serializeUsing = DateToSecondsSerializer.class)
    private Date ts;
    /**
     * 设备列表
     */
    private List<Dlt645DeviceDTO> devs;
}
