package org.jeecg.modules.dlt645.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import org.jeecg.modules.dlt645.constant.ControlReqCode;
import org.jeecg.modules.dlt645.constant.Dlt645DiCode;
import org.jeecg.modules.dlt645.constant.Dlt645Constants;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ReqDataDTO;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ReqDataItemDTO;
import org.jeecg.modules.dlt645.util.Dlt645Utils;

import java.util.*;

/**
 * DLT645 指令构建器接口
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
public interface Dlt645FrameBuilderService {
    /**
     * 构建 Dlt645Msg 对象
     *
     * @param address 地址域
     * @param controlCode 控制码
     * @param dataItemList 数据项列表
     * @return 构建的 Dlt645Msg 对象
     */
    Dlt645ReqDataDTO buildMessage(String address, ControlReqCode controlCode, List<Dlt645ReqDataItemDTO> dataItemList);

    /**
     * 构建读取单个数据标识的指令
     *
     * @param address 地址域
     * @param dataId 数据标识
     * @return 构建的 Dlt645Msg 对象
     */
    default Dlt645ReqDataDTO buildReadSingleDataCommand(String address, byte[] dataId) {
        return buildMessage(address, ControlReqCode.READ_DATA, Collections.singletonList(new Dlt645ReqDataItemDTO(dataId)));
    }

    /**
     * 构建读取多个数据标识的指令
     *
     * @param address 地址域
     * @param dataId  可变参数，支持传入多个数据标识字节数组
     * @return 构建的 Dlt645Msg 对象
     */
    default Dlt645ReqDataDTO buildReadMultipleDataCommand(String address, byte[]... dataId) {
        // 检查参数是否为空
        if (ArrayUtil.isNotEmpty(dataId)) {
            throw new IllegalArgumentException("dataId 参数不能为空");
        }

        // 构建 Dlt645ReqDataItemDTO 列表
        List<Dlt645ReqDataItemDTO> dataItemList = new ArrayList<>();

        // 遍历每个 byte[] 数组
        for (byte[] array : dataId) {
            // 检查当前数组是否为 null 或长度是否为 4 的倍数
            if (ArrayUtil.isNotEmpty(array) || array.length % 4 != 0) {
                throw new IllegalArgumentException("每个 dataId 的长度必须是 4 的倍数，且不能为 null");
            }

            // 按每 4 个字节分割
            for (int i = 0; i < array.length; i += 4) {
                // 截取 4 个字节
                byte[] currentDataId = Arrays.copyOfRange(array, i, i + 4);
                // 创建 Dlt645ReqDataItemDTO
                dataItemList.add(new Dlt645ReqDataItemDTO(currentDataId));
            }
        }

        // 构建并返回 Dlt645ReqDataDTO
        return buildMessage(address, ControlReqCode.READ_DATA, dataItemList);
    }

    /**
     * 构建跳合闸、保电指令
     *
     * @param address 地址域
     * @param controlCode 控制码
     * @param expiryTime 命令有效截止时间（格式：ssmmhhDDMMYY）
     * @return 构建的 Dlt645Msg 对象
     */
    default Dlt645ReqDataDTO buildControlValveCommand(String address, ControlReqCode controlCode, Date expiryTime) {
        // 操作密码
        Dlt645ReqDataItemDTO passwordDataItem = new Dlt645ReqDataItemDTO(null, Dlt645Constants.DEFAULT_PASSWORD);
        // 操作者代码
        Dlt645ReqDataItemDTO usernameDataItem = new Dlt645ReqDataItemDTO(null, Dlt645Constants.DEFAULT_USERNAME);
        // 控制命令类型（N1）
        byte n1 = controlCode.code();
        // 保留字段（N2）
        byte n2 = 0x00;
        // 命令有效截止时间（N3～N8）
        byte[] expiryTimeBytes = Dlt645Utils.convertStringToBcdBytesAndReverse(DateUtil.format(expiryTime, Dlt645Constants.DATETIME_PATTERN));
        // 数据域
        byte[] data = new byte[8];
        data[0] = n1;
        data[1] = n2;
        System.arraycopy(expiryTimeBytes, 0, data, 2, 6);
        // 构建 Dlt645ReqDataItemDTO
        Dlt645ReqDataItemDTO dataItem = new Dlt645ReqDataItemDTO(null, data);
        // 构建指令
        List<Dlt645ReqDataItemDTO> list = new ArrayList<>(3);
        list.add(passwordDataItem);
        list.add(usernameDataItem);
        list.add(dataItem);
        return buildMessage(address, ControlReqCode.TRIP_CLOSE_POWER_PROTECTION, list);
    }

    /**
     * 构建跳合闸、保电指令
     *
     * @param address 地址域
     * @param controlCode 控制码
     * @return 构建的 Dlt645Msg 对象
     */
    default Dlt645ReqDataDTO buildControlValveCommand(String address, ControlReqCode controlCode) {
        // 开关阀时间
        Date expiryTime = DateUtil.offsetSecond(DateUtil.date(), 1);
        return buildControlValveCommand(address, controlCode, expiryTime);
    }

    /**
     * 构建广播校时指令
     *
     * @param timestamp 时间戳（格式：YYMMDDhhmmss）
     * @return 构建的 Dlt645Msg 对象
     */
    default Dlt645ReqDataDTO buildBroadcastTimeSyncCommand(Date timestamp) {
        return buildMessage(Dlt645Constants.BROADCAST_DATETIME_ADDRESS, ControlReqCode.BROADCAST_TIME_SYNC, Collections.singletonList(new Dlt645ReqDataItemDTO(Dlt645Utils.convertStringToBcdBytes(DateUtil.format(timestamp, Dlt645Constants.DATETIME_PATTERN)))));
    }

    /**
     * 构建冻结命令
     *
     * @param address 地址域
     * @param timestamp 时间戳（格式：MMDDhhmm）
     * @return 构建的 Dlt645Msg 对象
     */
    default Dlt645ReqDataDTO buildFreezeCommand(String address, Date timestamp) {
        return buildMessage(address, ControlReqCode.FREEZE_COMMAND, Collections.singletonList(new Dlt645ReqDataItemDTO(Dlt645Utils.convertStringToBcdBytes(DateUtil.format(timestamp, Dlt645Constants.DATETIME_PATTERN)))));
    }

    /**
     * 构建读序列号指令
     *
     * @return 构建的 Dlt645Msg 对象
     */
    default Dlt645ReqDataDTO buildReadSnCommand(String address) {
        return buildMessage(address, ControlReqCode.READ_DATA, Collections.singletonList(new Dlt645ReqDataItemDTO(Dlt645DiCode.SERIAL_NUMBER.getDiByte(), null)));
    }
}
