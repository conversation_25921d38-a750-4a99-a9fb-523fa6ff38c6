package org.jeecg.modules.dlt645.pojo.dto;

import lombok.Data;
import org.jeecg.modules.dlt645.constant.ControlResCode;
import org.jeecg.modules.dlt645.util.Dlt645Utils;

import java.io.Serializable;
import java.util.List;

/**
 * DLT645 协议响应消息类
 *
 * <AUTHOR>
 * @date 2024-12-23
 */
@Data
public class Dlt645ResDataDTO implements Serializable {
    private static final long serialVersionUID = 3087651514185012659L;
    /**
     * 地址域 A0～A5
     */
    private String address;
    /**
     * 响应控制码
     */
    private ControlResCode controlCode;
    /**
     * 数据域
     */
    private List<Dlt645ResDataItemDTO> dataItemList;

    /**
     * 设置地址域，输入为 6 字节的 BCD 编码字节数组。
     * 如果输入的字节数组长度不足 6 字节，则高位用 0x00 补足。
     * 每个字节的高 4 位和低 4 位必须是有效的 BCD 码（0-9）。
     *
     * @param address 输入的 BCD 编码字节数组
     * @throws IllegalArgumentException 如果输入的字节数组不符合 BCD 编码规则
     */
    public void setAddressWithBytes(byte[] address) {
        this.address = Dlt645Utils.convertBcdBytesToAddressString(address);
    }

    public void setControlCodeWithByte(byte controlCode) {
        this.controlCode = ControlResCode.fromByte(controlCode);
    }
}
