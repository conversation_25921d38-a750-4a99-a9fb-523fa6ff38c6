package org.jeecg.modules.dlt645.service;

import io.netty.channel.ChannelHandlerContext;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ResDataItemDTO;
import org.jeecg.modules.dlt645.pojo.entity.Dlt645Device;
import org.jeecg.modules.mqtt.dto.GatewayDeviceDTO;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 设备管理服务接口
 * 用于维护设备与通信信道的映射关系
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
public interface Dlt645DeviceService {
    /**
     * 保存中电4G电表
     *
     * @param gatewayDeviceDTO
     */
    void saveOrUpdate(GatewayDeviceDTO gatewayDeviceDTO);

    /**
     * 保存
     *
     * @param dlt645Device
     */
    void save(Dlt645Device dlt645Device);

    /**
     * 保存
     *
     * @param address
     * @param dataItem
     */
    void save(String address, Dlt645ResDataItemDTO dataItem);

    /**
     * 保存
     *
     * @param address
     * @param ctx
     */
    void save(String address, ChannelHandlerContext ctx);

    /**
     * 新增
     *
     * @param address
     */
    void insert(String address);

    /**
     * 根据通信信道获取所有设备地址
     *
     * @param ctx 通信信道的上下文
     * @return 设备地址集合
     */
    String getDeviceAddressByChannel(ChannelHandlerContext ctx);

    /**
     * 根据设备序列号获取地址
     *
     * @param sn 设备序列号
     * @return 地址，如果设备不存在则返回 null
     */
    Optional<String> getAddressBySn(String sn);

    /**
     * 通过序列号获取设备
     *
     * @param sn 序列号
     * @return DL/T645设备
     */
    Optional<Dlt645Device> getOneBySn(String sn);

    /**
     * 根据地址查询
     *
     * @param address
     */
    Optional<Dlt645Device> getOneByAddress(String address);

    /**
     * 根据设备地址获取序列号
     *
     * @param address 设备地址
     * @return 设备序列号
     */
    Optional<String> getSnByAddress(String address);

    /**
     * 根据设备地址获取设备型号
     *
     * @param address 设备地址
     * @return 设备序列号
     */
    Optional<String> getMeterModelByAddress(String address);

    /**
     * 获取所有设备
     *
     * @return
     */
    List<Dlt645Device> getList();

    /**
     * 更新下线
     *
     * @param ctx
     */
    void updateOffline(ChannelHandlerContext ctx);

    /**
     * 更新上线
     *
     * @param ctx
     */
    void updateOnline(ChannelHandlerContext ctx);

    /**
     * 更新上线
     *
     * @param address
     */
    void updateOnline(String address);

    /**
     *
     *
     * @param id
     */
    void delete(String id);

    /**
     * 获取设备关联Channel
     *
     * @return
     */
    Map<String, ChannelHandlerContext> getAddressToChannel();

    /**
     * 注册设备及其对应的通信信道
     *
     * @param address 设备地址（12 位十进制数）
     * @param ctx     通信信道的上下文
     * @throws IllegalArgumentException 如果设备地址或通信信道为空
     */
    void registerDevice(String address, ChannelHandlerContext ctx);

    /**
     * 根据设备地址获取通信信道的上下文
     *
     * @param address 设备地址
     * @return 通信信道的上下文，如果设备不存在则返回 null
     */
    ChannelHandlerContext getChannelByAddress(String address);

    /**
     * 删除设备及其对应的通信信道
     *
     * @param address 设备地址
     * @throws IllegalArgumentException 如果设备地址为空
     */
    void unregisterDevice(String address);
}
