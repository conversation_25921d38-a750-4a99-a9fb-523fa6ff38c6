package org.jeecg.modules.dlt645.pojo.entity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.jeecg.modules.mongo.converter.DataDateTimeValueConverter;
import org.jeecg.modules.mongo.entity.SysMongoBaseEntity;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.data.convert.ValueConverter;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * DL/T645数据
 *
 * <AUTHOR>
 * @date 2025-01-07 09:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Document(value = "dlt645_data")
@ApiModel(value = "dlt645_data对象", description = "能源监测-DL/T645数据")
@CompoundIndex(useGeneratedName = true, unique = true, def = "{'sn':1, 'di_code':1, 'data_time':1}", background = true)
@CompoundIndex(useGeneratedName = true, def = "{'sn':1, 'data_time':1, 'value':1}", background = true)
public class Dlt645Data extends SysMongoBaseEntity {
    private static final long serialVersionUID = 348495859997964386L;
    /**
     * 通讯地址
     */
    @Field("address")
    private String address;
    /**
     * 序列号
     */
    @Field("sn")
    private String sn;
    /**
     * 数据唯一标识
     */
    @Field("di_code")
    private String diCode;
    /**
     * 数据时间
     */
    @ValueConverter(value = DataDateTimeValueConverter.class)
    @Excel(name = "记录时间")
    @Field("data_time")
    private Date dataTime;
    /**
     * 数据唯一标识值
     */
    @Field("value")
    private String value;
}
