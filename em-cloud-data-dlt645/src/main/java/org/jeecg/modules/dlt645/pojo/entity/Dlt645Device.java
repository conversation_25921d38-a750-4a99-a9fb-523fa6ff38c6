package org.jeecg.modules.dlt645.pojo.entity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.jeecg.modules.dlt645.util.Dlt645Utils;
import org.jeecg.modules.mongo.entity.SysMongoBaseEntity;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * DL/T645设备
 *
 * <AUTHOR>
 * @date 2025-01-07 09:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Document(value = "dlt645_device")
@ApiModel(value = "dlt645_device对象", description = "能源监测-DL/T645设备")
@CompoundIndex(useGeneratedName = true, unique = true, def = "{'address':1, 'sn':1}", background = true)
@CompoundIndex(useGeneratedName = true, unique = true, def = "{'sn':1, 'address':1}", background = true)
public class Dlt645Device extends SysMongoBaseEntity {
    private static final long serialVersionUID = 348495859997964386L;
    /**
     * 设备名称
     */
    @Field("name")
    private String name;
    /**
     * 通讯地址
     */
    @Field("address")
    private String address;
    /**
     * 表号
     */
    @Field("meter_number")
    private String meterNumber;
    /**
     * 额定电压
     */
    @Field("rated_voltage")
    private String ratedVoltage;
    /**
     * 额定电流
     */
    @Field("rated_current")
    private String ratedCurrent;
    /**
     * 最大电流
     */
    @Field("max_current")
    private String maxCurrent;
    /**
     * 电表型号
     */
    @Field("meter_model")
    private String meterModel;
    /**
     * 生产日期
     */
    @Field("manu_date")
    private String manuDate;
    /**
     * 协议版本号
     */
    @Field("protocol_version")
    private String protocolVersion;
    /**
     * 设备序列号
     */
    @Field("sn")
    private String sn;
    /**
     * 是否加解密
     */
    @Field("is_enc_dec")
    private Boolean encDec;
    /**
     * 在线状态
     */
    @Field("is_online")
    private Boolean online;

    /**
     * 设置序列号，根据序列号设置地址
     *
     * @param sn
     */
    public void setSn(String sn) {
        this.sn = sn;
        this.address = Dlt645Utils.formatAddressString(sn);
    }
}
