package org.jeecg.modules.dlt645.pojo.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * DLT645 请求数据项
 * 包含数据项标识和对应的数值
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class Dlt645ReqDataItemDTO implements Serializable {
    private static final long serialVersionUID = -7221844388469859867L;
    /**
     * 数据项标识（4 字节）
     */
    private byte[] di;
    /**
     * 数值部分
     */
    private byte[] value;

    /**
     * 构造函数
     *
     * @param di
     */
    public Dlt645ReqDataItemDTO(byte[] di) {
        this.di = di;
    }

    /**
     * 构造函数
     *
     * @param di
     * @param value
     */
    public Dlt645ReqDataItemDTO(byte[] di, byte[] value) {
        this.di = di;
        this.value = value;
    }
}
