package org.jeecg.modules.dlt645.service;

import org.jeecg.modules.dlt645.exception.Dlt645Exception;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ControlValveDTO;

import java.util.Queue;

/**
 * DLT645网关Service
 *
 * <AUTHOR>
 * @date 2024-12-23 14:28
 */
public interface Dlt645GatewayService {
    /**
     * 发送设备消息
     *
     * @param address
     */
    void sendReadDeviceInfoMsg(String address);

    /**
     * 发送抄设备信息指令
     *
     * @param address
     * @param deviceDiQueue
     */
    void sendReadDeviceInfoMsg(String address, Queue<byte[]> deviceDiQueue);

    /**
     * 批量读数据
     *
     */
    void batchSendReadDataMsg();

    /**
     * 读数据
     *
     * @param sn 序列号
     */
    void sendReadDataMsg(String sn);

    /**
     * 读数据
     *
     * @param address 通讯地址
     */
    void sendReadDataMsgByAddress(String address);

    /**
     * 发送广播校时消息
     */
    void sendBroadcastDatetimeMsg();

    /**
     * 发送跳闸指令
     *
     * @param address
     */
    void sendTripCommand(String address);

    /**
     * 发送合闸指令
     *
     * @param address
     */
    void sendCloseCommand(String address);

    /**
     * 发送阀控指令
     *
     * @param dlt645ControlValve
     */
    void sendControlValve(Dlt645ControlValveDTO dlt645ControlValve) throws Dlt645Exception;

    /**
     * 发送读取4G信号强度指令
     *
     * @param address
     */
    void sendReadSignalStrengthMsg(String address);

    /**
     * 缓存待执行的DI队列
     *
     * @param address
     * @return
     */
    Queue<byte[]> getPendingDiQueueByAddress(String address);
}
