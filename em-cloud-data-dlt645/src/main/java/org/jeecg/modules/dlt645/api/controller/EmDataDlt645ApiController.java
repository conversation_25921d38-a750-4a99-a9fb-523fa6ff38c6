package org.jeecg.modules.dlt645.api.controller;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.SysLog;
import org.jeecg.common.constant.SysTips;
import org.jeecg.modules.dlt645.api.IEmDataDlt645Api;
import org.jeecg.modules.dlt645.dto.GatewayReportDTO;
import org.jeecg.modules.dlt645.service.Dlt645DataService;
import org.jeecg.modules.dlt645.service.Dlt645DeviceService;
import org.jeecg.modules.mqtt.dto.GatewayDeviceDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * DLT645数据接口 Controller
 *
 * <AUTHOR>
 * @date 2025-01-20 14:39
 */
@RestController
@RequestMapping("/dataDlt645/api")
public class EmDataDlt645ApiController implements IEmDataDlt645Api {
    @Resource
    private Dlt645DataService dlt645DataService;
    @Resource
    private Dlt645DeviceService dlt645DeviceService;

    /**
     * 保存或更新中电4G电表设备信息
     * @param gatewayDeviceDTO 网关设备数据传输对象，包含设备详细信息
     * @return 操作结果，包含成功状态和提示信息
     */
    @Override
    @SysLog("中电4G电表-保存设备")
    @PostMapping("/saveOrUpdate")
    public Result<String> saveOrUpdate(@RequestBody GatewayDeviceDTO gatewayDeviceDTO) {
        // 调用服务层保存或更新设备信息
        dlt645DeviceService.saveOrUpdate(gatewayDeviceDTO);
        return Result.ok(SysTips.ADD_SUCCESS);
    }

    /**
     * 作废指定中电4G电表设备
     * @param id 要作废的设备唯一标识符
     * @return 操作结果，包含成功状态和提示信息
     */
    @Override
    @SysLog("中电4G电表-作废设备")
    @PostMapping(value = "/cancel")
    public Result<String> cancel(String id) {
        // 调用服务层执行设备作废逻辑
        dlt645DeviceService.delete(id);
        return Result.ok(SysTips.DEL_SUCCESS);
    }

    /**
     * 处理电表数据上报请求
     * @param reportDTO 数据上报传输对象，包含电表实时数据
     * @return 处理结果，包含数据接收状态和业务响应
     */
    @Override
    @PostMapping(value = "/report")
    public Result<String> report(GatewayReportDTO reportDTO) {
        // 将上报数据转发至服务层处理
        return dlt645DataService.reportData(reportDTO);
    }
}
