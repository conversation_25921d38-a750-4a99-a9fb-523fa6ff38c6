package org.jeecg.modules.dlt645.encoder;

import cn.hutool.core.util.ArrayUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.ThrowableUtils;
import org.jeecg.modules.dlt645.constant.Dlt645Constants;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ReqDataDTO;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ReqDataItemDTO;
import org.jeecg.modules.dlt645.util.Dlt645Utils;

import java.nio.ByteBuffer;
import java.util.Arrays;

/**
 * DLT645协议编码器
 * 将DLT645Message对象编码为字节流
 *
 * <AUTHOR>
 * @date 2024-12-23
 */
@Slf4j
public class Dlt645Encoder extends MessageToByteEncoder<Dlt645ReqDataDTO> {
    @Override
    protected void encode(ChannelHandlerContext ctx, Dlt645ReqDataDTO msg, ByteBuf out) {
        log.info("编码 DLT645 消息: {}", msg);
        try {
            // 1. 创建缓冲区
            ByteBuffer buffer = ByteBuffer.allocate(Dlt645Utils.calculateTotalLength(msg.getDataItemList()));
            // 2. 添加前导字节 (4 个 FEH)
            buffer.put(Dlt645Constants.PREAMBLE_BYTE);
            // 3. 添加第一个起始符 (68H)
            buffer.put(Dlt645Constants.FRAME_HEADER);
            // 4. 添加地址域 (6 字节)
            byte[] address = Dlt645Utils.reverseByteArray(msg.getAddress());
            buffer.put(address);
            // 5. 添加第二个起始符 (68H)
            buffer.put(Dlt645Constants.FRAME_HEADER);
            // 6. 添加控制码 (1 字节)
            buffer.put(msg.getControlCode());
            // 7. 计算数据长度 (1 字节)
            int dataLength = Dlt645Utils.calculateDataLength(msg.getDataItemList());
            buffer.put((byte) dataLength);
            // 8. 添加数据域 (可选，取决于具体指令)
            if (msg.getDataItemList() != null && !msg.getDataItemList().isEmpty()) {
                for (Dlt645ReqDataItemDTO item : msg.getDataItemList()) {
                    if (ArrayUtil.isNotEmpty(item.getDi())) {
                        byte[] di = Dlt645Utils.encodeAndReverse(item.getDi());
                        buffer.put(di);
                    }
                    if (ArrayUtil.isNotEmpty(item.getValue())) {
                        byte[] value = Dlt645Utils.encode(item.getValue());
                        buffer.put(value);
                    }
                }
            }
            // 9. 提取数据域部分的数据，默认空数组
            byte[] dataField = new byte[0];
            if (dataLength > 0) {
                int bufferPosition = buffer.position();
                // 前导符(4) + 起始符(1) + 地址域(6) + 起始符(1) + 控制码(1) + 数据长度(1)
                int dataStartIndex = 4 + 1 + 6 + 1 + 1 + 1;
                // 数据域的结束位置是当前写入位置
                dataField = Arrays.copyOfRange(buffer.array(), dataStartIndex, bufferPosition);
            }
            // 10. 计算校验码 (CS)
            byte checkCode = Dlt645Utils.calculateCheckCode(address, msg.getControlCode(), dataField.length, dataField);
            buffer.put(checkCode);
            // 11. 添加结束符 (16H)
            buffer.put(Dlt645Constants.FRAME_TAIL);
            // 12. 将缓冲区内容写入 ByteBuf
            out.writeBytes(buffer.array(), 0, buffer.position());
            log.info("编码完成，发送指令: {}", Dlt645Utils.byteArrayToHexString(buffer.array()));
        } catch (Exception e) {
            log.error("编码出错，msg → {}", ThrowableUtils.getStackTrace(e));
        }
    }
}
