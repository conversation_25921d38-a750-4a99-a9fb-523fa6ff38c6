package org.jeecg.modules.dlt645.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.StatTimeUtils;
import org.jeecg.common.util.ThrowableUtils;
import org.jeecg.modules.dlt645.constant.Dlt645Constants;
import org.jeecg.modules.dlt645.constant.Dlt645DiCode;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ReqDataItemDTO;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ResDataItemDTO;
import org.jeecg.modules.dlt645.pojo.entity.Dlt645Device;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * DL/T645 工具类
 *
 * 该类提供了 DL/T645 协议中常用的工具方法，包括数据编码、解码、校验码计算、地址转换等。
 * 所有方法均为静态方法，方便直接调用。
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
@Slf4j
public class Dlt645Utils {
    /**
     * 对数据域 DATA 进行编码（每个字节加上 0x33）。
     *
     * @param data 原始数据
     * @return 编码后的数据
     */
    public static byte[] encode(byte[] data) {
        if (data == null) {
            return null;
        }
        byte[] encodedData = new byte[data.length];
        for (int i = 0; i < data.length; i++) {
            encodedData[i] = encodeByte(data[i]);
        }
        return encodedData;
    }

    /**
     * 对数据域 DATA 进行编码（每个字节加上 0x33）且反转。
     *
     * @param data 原始数据
     * @return 编码后的数据
     */
    public static byte[] encodeAndReverse(byte[] data) {
        return reverseByteArray(encode(data));
    }


    /**
     * 对数据域 DATA 进行解码（每个字节减去 0x33）。
     *
     * @param data 编码后的数据
     * @return 解码后的数据
     */
    public static byte[] decode(byte[] data) {
        if (data == null) {
            return null;
        }
        byte[] decodedData = new byte[data.length];
        for (int i = 0; i < data.length; i++) {
            decodedData[i] = decodeByte(data[i]);
        }
        return decodedData;
    }

    /**
     * 对数据域 DATA 进行解码并反转字节顺序。
     *
     * @param data 编码后的数据
     * @return 解码并反转后的数据
     */
    public static byte[] decodeAndReverse(byte[] data) {
        return reverseByteArray(decode(data));
    }

    /**
     * 对单个字节进行编码（加上 0x33）。
     *
     * @param b 原始字节
     * @return 编码后的字节
     */
    public static byte encodeByte(byte b) {
        return (byte) ((b & Dlt645Constants.MASK_BYTE) + Dlt645Constants.FRAME_OFFSET);
    }

    /**
     * 对单个字节进行解码（减去 0x33）。
     *
     * @param b 编码后的字节
     * @return 解码后的字节
     */
    public static byte decodeByte(byte b) {
        return (byte) ((b & Dlt645Constants.MASK_BYTE) - Dlt645Constants.FRAME_OFFSET);
    }

    /**
     * 计算校验码 (Checksum)
     *
     * @param address     地址域
     * @param controlCode 控制码
     * @param dataLength  数据长度
     * @param data        数据域
     * @return 校验码
     */
    public static byte calculateCheckCode(byte[] address, byte controlCode, int dataLength, byte[] data) {
        if (address == null || address.length != 6) {
            throw new IllegalArgumentException("地址域必须为 6 字节");
        }
        if (dataLength < 0 || dataLength > 255) {
            throw new IllegalArgumentException("数据长度必须在 0 到 255 之间");
        }
        if (dataLength > 0 && (data == null || data.length < dataLength)) {
            throw new IllegalArgumentException("数据域长度与数据长度字段不一致");
        }

        int code = 0;

        // 加入第一个帧起始符 0x68
        code += 0x68;
        code &= Dlt645Constants.MASK_BYTE;

        // 计算地址域的和
        for (byte b : address) {
            code += (b & Dlt645Constants.MASK_BYTE);
            code &= Dlt645Constants.MASK_BYTE;
        }

        // 加入第二个帧起始符 0x68
        code += 0x68;
        code &= Dlt645Constants.MASK_BYTE;

        // 计算控制码的和
        code += (controlCode & Dlt645Constants.MASK_BYTE);
        code &= Dlt645Constants.MASK_BYTE;

        // 计算数据长度的和
        code += (dataLength & Dlt645Constants.MASK_BYTE);
        code &= Dlt645Constants.MASK_BYTE;

        // 计算数据域的和
        if (dataLength > 0) {
            for (int i = 0; i < dataLength; i++) {
                code += (data[i] & Dlt645Constants.MASK_BYTE);
                code &= Dlt645Constants.MASK_BYTE;
            }
        }

        // 返回校验码
        return (byte) code;
    }

    /**
     * 验证接收到的消息的校验码是否正确。
     *
     * @param address     地址域
     * @param controlCode 控制码
     * @param dataLength  数据长度
     * @param data        数据域
     * @param checkCode   校验码
     * @return 如果校验码正确则返回 true，否则返回 false
     */
    public static boolean verifyCheckCode(byte[] address, byte controlCode, int dataLength, byte[] data, byte checkCode) {
        byte calculatedChecksum = calculateCheckCode(address, controlCode, dataLength, data);
        return calculatedChecksum == checkCode;
    }

    /**
     * 将十进制地址转换为 6 字节的 BCD 编码。
     *
     * @param decimalAddress 十进制地址
     * @return 6 字节的 BCD 编码数组
     */
    public static byte[] convertDecimalStringToBcdBytes(long decimalAddress) {
        byte[] bcdBytes = new byte[6];
        for (int i = 5; i >= 0; i--) {
            int highNibble = (int) (decimalAddress % 10);
            decimalAddress /= 10;
            int lowNibble = (int) (decimalAddress % 10);
            decimalAddress /= 10;
            bcdBytes[i] = (byte) ((lowNibble << 4) | highNibble);
        }
        return bcdBytes;
    }

    /**
     * 读取多字节数值，按照低字节在前，高字节在后的顺序组合成最终的数值。
     *
     * @param in     ByteBuf 输入流
     * @param length 数值的字节长度
     * @return 组合后的数值
     */
    public static long readMultiByteValue(ByteBuf in, int length) {
        long value = 0;
        for (int i = 0; i < length; i++) {
            byte b = in.readByte();
            value = (value << 8) | (b & 0xFF);
        }
        return value;
    }

    /**
     * 逆序反转字节数组。
     *
     * @param bytes 输入的字节数组
     * @return 逆序后的字节数组
     */
    public static byte[] reverseByteArray(byte[] bytes) {
        return ArrayUtil.reverse(bytes);
    }

    /**
     * 检查地址域是否合法
     *
     * @param addressHex 地址域的十六进制字符串
     * @return 是否合法
     */
    public static boolean isValidAddress(String addressHex) {
        // 检查长度，5 字节 = 10 个十六进制字符
        if (addressHex.length() != 10) {
            return false;
        }

        // 检查每个字节是否为合法的 BCD 码
        for (int i = 0; i < addressHex.length(); i += 2) {
            String byteStr = addressHex.substring(i, i + 2);
            int byteValue = Integer.parseInt(byteStr, 16);
            // BCD 码的范围是 0x00 到 0x99
            if (byteValue < 0x00 || byteValue > 0x99) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查字节是否为有效的 BCD 码。
     *
     * @param b 待检查的字节
     * @return 如果字节是有效的 BCD 码，则返回 true；否则返回 false
     */
    public static boolean isValidBcdByte(byte b) {
        int highNibble = (b >> 4) & Dlt645Constants.VALID_BCD;
        int lowNibble = b & Dlt645Constants.VALID_BCD;
        return highNibble <= 9 && lowNibble <= 9;
    }

    /**
     * 将 BCD 编码的字节数组转换为 8 位十进制字符串。
     *
     * @param bcdBytes 输入的 BCD 编码字节数组
     * @return 转换后的 8 位十进制字符串
     */
    public static String convertBcdBytesToAddressString(byte[] bcdBytes) {
        byte[] paddedAddress = Arrays.copyOf(bcdBytes, 6);
        return convertBcdBytesToString(paddedAddress);
    }

    /**
     * 格式化地址
     *
     * @param address 地址
     * @return 格式化后12位十进制字符串
     */
    public static String formatAddressString(String address) {
        return StrUtil.padPre(address, 12, '0');
    }

    /**
     * 将 BCD 编码的字节数组转换为十进制字符串
     *
     * @param bcdBytes
     * @return
     */
    public static String convertBcdBytesToString(byte[] bcdBytes) {
        if (bcdBytes == null || bcdBytes.length == 0) {
            throw new IllegalArgumentException("BCD 字节数组不能为空");
        }

        StringBuilder decimalString = new StringBuilder();

        for (byte b : bcdBytes) {
            // 高 4 位
            int highNibble = (b >> 4) & Dlt645Constants.VALID_BCD;
            // 低 4 位
            int lowNibble = b & Dlt645Constants.VALID_BCD;

            // 检查是否为有效的 BCD 数字（0-9）
            if (highNibble > 9 || lowNibble > 9) {
                throw new IllegalArgumentException("无效的 BCD 字节: " + b);
            }

            // 将数字拼接到字符串中
            decimalString.append(highNibble);
            decimalString.append(lowNibble);
        }

        return decimalString.toString();
    }

    /**
     * 将地址字符串转换为 6 字节 BCD 编码的字节数组并反转。
     *
     * @param addressString 地址字符串（12 位十进制数）
     * @return 6 字节 BCD 编码的字节数组
     * @throws IllegalArgumentException 如果输入的地址字符串格式不正确
     */
    public static byte[] convertAddressStringToBcdBytesAndReverse(String addressString) {
        return reverseByteArray(convertAddressStringToBcdBytes(addressString));
    }

    /**
     * 将地址字符串转换为 6 字节 BCD 编码的字节数组。
     *
     * @param addressString 地址字符串（12 位十进制数）
     * @return 6 字节 BCD 编码的字节数组
     * @throws IllegalArgumentException 如果输入的地址字符串格式不正确
     */
    public static byte[] convertAddressStringToBcdBytes(String addressString) {
        if (StrUtil.isBlank(addressString)) {
            throw new IllegalArgumentException("地址字符串不能为空");
        }

        // 移除非数字字符
        StringBuilder cleanedAddressString = new StringBuilder();
        for (char c : addressString.toCharArray()) {
            if (Character.isDigit(c) || Character.toUpperCase(c) == 'A' || Character.toUpperCase(c) == 'F') {
                cleanedAddressString.append(c);
            }
        }

        // 检查是否为广播地址
        String address = cleanedAddressString.toString();
        if (address.equalsIgnoreCase(Dlt645Constants.BROADCAST_ADDRESS) || address.equalsIgnoreCase("FFFFFFFFFFFF")) {
            return new byte[]{(byte) 0xAA, (byte) 0xAA, (byte) 0xAA, (byte) 0xAA, (byte) 0xAA, (byte) 0xAA};
        }

        if (cleanedAddressString.length() > 12) {
            throw new IllegalArgumentException("地址字符串长度不能超过 12 位");
        }

        // 补全长度
        while (cleanedAddressString.length() < 12) {
            cleanedAddressString.insert(0, "0");
        }

        return convertStringToBcdBytes(cleanedAddressString.toString());
    }

    /**
     * 将字符串转换为 BCD 编码的字节数组并且反转。
     *
     * @param strData 字符串（偶数位十进制数，或包含 A/F 字符）
     * @return BCD 编码的字节数组
     */
    public static byte[] convertStringToBcdBytesAndReverse(String strData) {
        return reverseByteArray(convertStringToBcdBytes(strData));
    }

    /**
     * 将字符串转换为 BCD 编码的字节数组。
     *
     * @param strData 字符串（偶数位十进制数，或包含 A/F 字符）
     * @return BCD 编码的字节数组
     */
    public static byte[] convertStringToBcdBytes(String strData) {
        if (StrUtil.isBlank(strData)) {
            throw new IllegalArgumentException("输入字符串不能为空");
        }

        // 检查字符串长度是否为偶数
        if (strData.length() % 2 != 0) {
            throw new IllegalArgumentException("输入字符串长度必须是偶数");
        }

        // 计算字节数组长度
        int byteLength = strData.length() / 2;
        byte[] bcdBytes = new byte[byteLength];

        // 转换为 BCD 字节数组
        for (int i = 0; i < byteLength; i++) {
            int index = i * 2;
            char highNibble = strData.charAt(index);
            char lowNibble = strData.charAt(index + 1);

            // 处理高 4 位
            int highValue;
            if (Character.toUpperCase(highNibble) == 'A' || Character.toUpperCase(highNibble) == 'F') {
                // 将 A 或 F 转换为 0xA
                highValue = 0xA;
            } else if (Character.isDigit(highNibble)) {
                highValue = Character.getNumericValue(highNibble);
            } else {
                throw new IllegalArgumentException("输入字符串包含非法字符: " + highNibble);
            }

            // 处理低 4 位
            int lowValue;
            if (Character.toUpperCase(lowNibble) == 'A' || Character.toUpperCase(lowNibble) == 'F') {
                // 将 A 或 F 转换为 0xA
                lowValue = 0xA;
            } else if (Character.isDigit(lowNibble)) {
                lowValue = Character.getNumericValue(lowNibble);
            } else {
                throw new IllegalArgumentException("输入字符串包含非法字符: " + lowNibble);
            }

            // 组合高 4 位和低 4 位
            bcdBytes[i] = (byte) ((highValue << 4) | lowValue);
        }

        return bcdBytes;
    }

    /**
     * 将 BCD 编码的字节数组转换格式化后的十进制字符串。
     *
     * @param di       数据项标识
     * @param bcdBytes 输入的 BCD 编码字节数组
     * @return 格式化后的十进制字符串
     */
    public static String convertBcdBytesToFormatString(String di, byte[] bcdBytes) {
        String decimalString = convertBcdBytesToString(bcdBytes);
        String format = Dlt645DiCode.getFormat(di);
        if(StrUtil.isBlank(format)) {
            return decimalString;
        }
        return formatData(decimalString, format);
    }

    /**
     * 格式化电能量数据为指定的格式，并根据格式模式中的小数位数动态调整数值。
     *
     * @param value   电能量数据值（整数形式，如 "00000186"）
     * @param pattern 格式化模式字符串（如 "######.##" 或 "######.###"）
     * @return 格式化后的字符串
     */
    public static String formatData(String value, String pattern) {
        BigDecimal number = new BigDecimal(value.trim());
        int decimalPlaces = getDecimalPlaces(pattern);

        if (decimalPlaces > 0) {
            BigDecimal divisor = BigDecimal.TEN.pow(decimalPlaces);
            number = number.divide(divisor, decimalPlaces, RoundingMode.DOWN);
        }

        number = number.setScale(decimalPlaces, RoundingMode.DOWN);
        DecimalFormat df = new DecimalFormat(pattern);
        df.setGroupingUsed(false);
        return df.format(number);
    }

    /**
     * 获取格式模式中的小数位数。
     *
     * @param pattern 格式化模式字符串
     * @return 小数位数
     */
    private static int getDecimalPlaces(String pattern) {
        int decimalIndex = pattern.indexOf('.');
        return decimalIndex == -1 ? 0 : pattern.length() - decimalIndex - 1;
    }

    /**
     * 将字节数组转换为十六进制字符串（不带空格）。
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String byteArrayToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }

        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            hexString.append(String.format("%02X", b));
        }
        return hexString.toString();
    }

    /**
     * 将字节数组转换为十六进制字符串（带空格）。
     *
     * @param bytes 字节数组
     * @return 十六进制字符串（带空格）
     */
    public static String byteArrayToHexStringWithSpaces(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }

        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            hexString.append(String.format("%02X ", b));
        }

        if (hexString.length() > 0) {
            hexString.setLength(hexString.length() - 1);
        }
        return hexString.toString();
    }

    /**
     * 将十六进制字符串转换为字节数组。
     *
     * @param hexString 十六进制字符串
     * @return 字节数组
     * @throws IllegalArgumentException 如果输入的字符串不是有效的十六进制格式
     */
    public static byte[] hexStringToByteArray(String hexString) {
        String cleanedHexString = hexString.replaceAll("[^0-9A-Fa-f]", "");

        if (cleanedHexString.length() % 2 != 0) {
            throw new IllegalArgumentException("十六进制字符串的长度必须是偶数");
        }

        int length = cleanedHexString.length() / 2;
        byte[] byteArray = new byte[length];

        for (int i = 0; i < length; i++) {
            int index = i * 2;
            byteArray[i] = (byte) Integer.parseInt(cleanedHexString.substring(index, index + 2), 16);
        }

        return byteArray;
    }

    /**
     * 将数字字符串转换为 BCD 编码的字节数组。
     *
     * @param numberString 数字字符串（只包含 0-9）
     * @return BCD 编码的字节数组
     * @throws IllegalArgumentException 如果输入的字符串包含非数字字符或长度不是偶数
     */
    public static byte[] convertNumberStringToBcdBytes(String numberString) {
        if (numberString == null || numberString.isEmpty()) {
            throw new IllegalArgumentException("数字字符串不能为空");
        }

        String cleanedNumberString = numberString.replaceAll("[^0-9]", "");

        if (!cleanedNumberString.matches("[0-9]+")) {
            throw new IllegalArgumentException("数字字符串只能包含 0-9 的数字");
        }

        if (cleanedNumberString.length() % 2 != 0) {
            cleanedNumberString = "0" + cleanedNumberString;
        }

        int length = cleanedNumberString.length() / 2;
        byte[] bcdBytes = new byte[length];

        for (int i = 0; i < length; i++) {
            int index = i * 2;
            char highNibble = cleanedNumberString.charAt(index);
            char lowNibble = cleanedNumberString.charAt(index + 1);
            bcdBytes[i] = (byte) ((Character.getNumericValue(highNibble) << 4) | Character.getNumericValue(lowNibble));
        }

        return bcdBytes;
    }

    /**
     * 计算 DLT645 消息的总长度
     *
     * @param dataItemList 数据项列表
     * @return 消息的总长度
     */
    public static int calculateTotalLength(List<Dlt645ReqDataItemDTO> dataItemList) {
        // 固定部分长度：前导符(4) + 起始符(1) + 地址域(6) + 起始符(1) + 控制码(1) + 数据长度(1) + 校验码(1) + 结束符(1)
        int fixedLength = 4 + 1 + 6 + 1 + 1 + 1 + 1 + 1;
        // 数据域长度
        int dataLength = calculateDataLength(dataItemList);
        // 总长度 = 固定长度 + 数据域长度
        return fixedLength + dataLength;
    }

    /**
     * 计算 DLT645 消息的数据域长度
     *
     * @param dataItemList 数据项列表
     * @return 消息的总长度
     */
    public static int calculateDataLength(List<Dlt645ReqDataItemDTO> dataItemList) {
        // 数据域长度
        int dataLength = 0;

        if (dataItemList != null && !dataItemList.isEmpty()) {
            for (Dlt645ReqDataItemDTO item : dataItemList) {
                if (ArrayUtil.isNotEmpty(item.getDi())) {
                    // 每个字符代表半个字节，因此长度除以 2
                    dataLength += item.getDi().length;
                }
                if (ArrayUtil.isNotEmpty(item.getValue())) {
                    // 每个字符代表半个字节，因此长度除以 2
                    dataLength += item.getValue().length;
                }
            }
        }

        // 数据域长度
        return dataLength;
    }

    /**
     * 将时间字符串转换为字节数组并反转
     *
     * @param timestamp 时间字符串
     * @return 字节数组
     */
    public static byte[] convertTimestampToByteArrayAndReverse(String timestamp) {
        return reverseByteArray(convertTimestampToByteArray(timestamp));
    }

    /**
     * 将时间字符串转换为字节数组
     *
     * @param timestamp 时间字符串
     * @return 字节数组
     * @throws IllegalArgumentException 如果时间字符串格式不正确
     */
    public static byte[] convertTimestampToByteArray(String timestamp) {
        // 检查字符串长度是否为12
        if (timestamp == null || timestamp.length() != 12) {
            throw new IllegalArgumentException("时间字符串格式必须为 YYMMDDhhmmss");
        }

        // 创建字节数组，长度为6（YY MM DD hh mm ss）
        byte[] byteArray = new byte[6];

        // 按两位分割字符串，并转换为字节
        for (int i = 0; i < 6; i++) {
            // 截取两位字符串
            String part = timestamp.substring(i * 2, i * 2 + 2);
            // 转换为字节
            byteArray[i] = (byte) Integer.parseInt(part);
        }

        return byteArray;
    }

    /**
     * 将十六进制字符串转换为二进制字符串
     *
     * @param hex 十六进制字符串
     * @return 二进制字符串
     */
    public static String hexToBinary(String hex) {
        StringBuilder binary = new StringBuilder();
        for (char c : hex.toCharArray()) {
            // 将每个十六进制字符转换为4位二进制
            String binarySegment = Integer.toBinaryString(Character.digit(c, 16));
            // 补全前导零
            while (binarySegment.length() < 4) {
                binarySegment = "0" + binarySegment;
            }
            binary.append(binarySegment);
        }
        return binary.toString();
    }

    /**
     * 将十六进制字符串转换为 ASCII 字符串
     *
     * @param hex 十六进制字符串（例如 "48656C6C6F"）
     * @return 对应的 ASCII 字符串（例如 "Hello"）
     */
    public static String hexToAscii(String hex) {
        if (StrUtil.isNotBlank(hex)) {
            return "";
        }

        // 确保十六进制字符串长度为偶数
        if (hex.length() % 2 != 0) {
            throw new IllegalArgumentException("十六进制字符串长度必须为偶数");
        }

        StringBuilder ascii = new StringBuilder();
        for (int i = 0; i < hex.length(); i += 2) {
            // 每两个字符解析为一个十六进制字节
            String byteStr = hex.substring(i, i + 2);
            // 将十六进制字节转换为整数
            int decimal = Integer.parseInt(byteStr, 16);
            // 将整数转换为 ASCII 字符
            char ch = (char) decimal;
            ascii.append(ch);
        }

        return ascii.toString();
    }

    /**
     * 将字节数组转换为 ASCII 字符串
     *
     * @param bytes 字节数组
     * @return ASCII 字符串
     */
    public static String convertByteArrayToAscii(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        return new String(bytes, StandardCharsets.US_ASCII);
    }

    /**
     * 将十六进制字节数组转换为二进制数组
     *
     * @param hexBytes 十六进制字节数组
     * @return 二进制数组（每个元素是一个 8 位二进制字符串）
     */
    public static String[] hexByteArrayToBinaryArray(byte[] hexBytes) {
        if (hexBytes == null || hexBytes.length == 0) {
            return new String[0];
        }

        String[] binaryArray = new String[hexBytes.length];
        for (int i = 0; i < hexBytes.length; i++) {
            // 将每个字节转换为 8 位二进制字符串，补全前导零
            binaryArray[i] = String.format("%8s", Integer.toBinaryString(hexBytes[i] & 0xFF)).replace(' ', '0');
        }

        return binaryArray;
    }

    /**
     * 将十六进制字节数组转换为二进制字符串
     *
     * @param hexBytes 十六进制字节数组
     * @return 二进制字符串（所有字节的二进制表示拼接在一起）
     */
    public static String convertHexByteArrayToBinaryString(byte[] hexBytes) {
        if (hexBytes == null || hexBytes.length == 0) {
            return "";
        }

        StringBuilder binaryString = new StringBuilder();
        for (byte b : hexBytes) {
            // 将每个字节转换为 8 位二进制字符串，补全前导零
            String binary = String.format("%8s", Integer.toBinaryString(b & 0xFF)).replace(' ', '0');
            binaryString.append(binary);
        }

        return binaryString.toString();
    }

    /**
     * 通过反射根据 diCode 动态设置 Dlt645Device 的字段值
     *
     * @param device   设备对象
     * @param dataItem 数据项
     */
    public static void setFieldValue(Dlt645Device device, Dlt645ResDataItemDTO dataItem) {
        // 获取 diCode 字段名称（转换为驼峰命名）
        String fieldName = convertDiCodeToFieldName(dataItem.getDiCode());
        // 通过反射设置字段值
        Field field = ReflectionUtils.findField(Dlt645Device.class, fieldName);
        if (ObjUtil.isNotNull(field)) {
            // 确保字段可访问
            ReflectionUtils.makeAccessible(field);
            ReflectionUtils.setField(field, device, dataItem.getValue());
        } else {
            log.warn("Field not found in Dlt645Device for diCode: {}", dataItem.getDiCode());
        }
    }

    /**
     * 将 diCode（下划线命名）转换为字段名称（驼峰命名）
     *
     * @param diCode 数据项代码（下划线命名）
     * @return 字段名称（驼峰命名）
     */
    public static String convertDiCodeToFieldName(String diCode) {
        if (StrUtil.isBlank(diCode)) {
            return diCode;
        }

        // 将下划线命名转换为驼峰命名
        StringBuilder fieldName = new StringBuilder();
        boolean nextUpper = false;
        for (char c : diCode.toCharArray()) {
            if (c == CharPool.UNDERLINE) {
                nextUpper = true;
            } else {
                if (nextUpper) {
                    fieldName.append(Character.toUpperCase(c));
                    nextUpper = false;
                } else {
                    fieldName.append(Character.toLowerCase(c));
                }
            }
        }
        return fieldName.toString();
    }

    /**
     * 获取格式化时间
     *
     * @param dataTime
     * @return
     */
    public static Date getFormatDataTime(Date dataTime) {
        return StatTimeUtils.getDataTime(dataTime, Dlt645Constants.REPORT_PERIOD);
    }

    /**
     * 获取格式化当前时间
     *
     * @return
     */
    public static Date getFormatCurrentDataTime() {
        return getFormatDataTime(new Date());
    }

    /**
     * 获取格式化当前时间
     *
     * @return
     */
    public static Date getCurrentDataTime() {
        return new Date();
    }

    /**
     * 获取格式化当前时间
     *
     * @return
     */
    public static Date getFormatPreviousSecondDataTime() {
        return getFormatDataTime(DateUtil.offsetSecond(new Date(), -1));
    }

    /**
     * 获取设备Di信息队列
     *
     * @param dlt645Device DLT645设备
     * @return
     */
    public static Queue<byte[]> getDeviceDiQueue(Dlt645Device dlt645Device) {
        // 设备DI队列
        Queue<byte[]> deviceDiQueue = new ConcurrentLinkedQueue<>();
        // 反射类
        Class<Dlt645Device> clazz = Dlt645Device.class;
        // 获取字段
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            // 处理私有字段
            field.setAccessible(true);
            try {
                // 获取对象字段
                Object value = field.get(dlt645Device);
                if (ObjUtil.isNull(value)) {
                    // 字段名称
                    String fieldName = toUnderscoreName(field.getName());
                    // 获取字段名对应的数据项标识枚举
                    Dlt645DiCode diCode = Dlt645DiCode.getByDiCode(fieldName);
                    if(ObjUtil.isNotNull(diCode)) {
                        deviceDiQueue.add(diCode.getDiByte());
                    }
                }
            } catch (IllegalAccessException e) {
                log.error("获取设备字段异常，msg → {}", ThrowableUtils.getStackTrace(e));
            }
        }
        return deviceDiQueue;
    }

    /**
     * 将驼峰命名法转换为下划线分隔的名称
     *
     * @param name 驼峰命名的字段名
     * @return 用下划线分隔的字段名
     */
    public static String toUnderscoreName(String name) {
        // 在小写字母或数字与大写字母之间插入下划线，并转换为小写
        return name.replaceAll("([a-z0-9])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * 格式化序列号
     *
     * @param address
     * @return
     */
    public static String formatSn(String address) {
        return StrUtil.removeAllPrefix(address, "0");
    }
}
