package org.jeecg.modules.dlt645.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DLT645 数据项
 * 包含数据项标识和对应的数值
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Dlt645ResDataItemDTO implements Serializable {
    private static final long serialVersionUID = -7221844388469859867L;
    /**
     * 数据项标识字符串（4 字节）
     */
    private String di;
    /**
     * 数据项标识字节（4 字节）
     */
    private byte[] diByte;
    /**
     * 数据项标识code
     */
    private String diCode;
    /**
     * 数值部分
     */
    private String value;
}
