package org.jeecg.modules.dlt645.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.HandlebarsUtils;
import org.jeecg.common.util.ThrowableUtils;
import org.jeecg.modules.dlt645.constant.Dlt645Constants;
import org.jeecg.modules.dlt645.constant.Dlt645DiCode;
import org.jeecg.modules.dlt645.constant.Dlt645DiGroupCode;
import org.jeecg.modules.dlt645.dto.GatewayReportDTO;
import org.jeecg.modules.dlt645.exception.Dlt645Exception;
import org.jeecg.modules.dlt645.pojo.dto.*;
import org.jeecg.modules.dlt645.pojo.entity.Dlt645Data;
import org.jeecg.modules.dlt645.pojo.entity.Dlt645Device;
import org.jeecg.modules.dlt645.repository.Dlt645DataRepository;
import org.jeecg.modules.dlt645.service.Dlt645DataService;
import org.jeecg.modules.dlt645.service.Dlt645DeviceService;
import org.jeecg.modules.dlt645.service.Dlt645GatewayService;
import org.jeecg.modules.dlt645.util.Dlt645Utils;
import org.jeecg.modules.mongo.annotation.MongoTransactional;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * DLT645数据解析Service实现
 * 负责解析从站上报的数据，并处理设备注册、心跳包、数据上报等操作。
 *
 * <AUTHOR>
 * @date 2024-01-13
 */
@Slf4j
@Service
public class Dlt645DataServiceImpl implements Dlt645DataService {
    // RabbitMQ前缀
    @Value("${energy-monitor.profiles-active.rabbitmq-prefix}")
    private String rabbitMqPrefix;

    // 平台前缀
    @Value("${energy-monitor.profiles-active.platform-prefix}")
    private String platformPrefix;

    // RabbitMQ客户端
    @Resource
    private RabbitTemplate rabbitTemplate;

    // 设备服务
    @Resource
    private Dlt645DeviceService dlt645DeviceService;

    // 数据仓库
    @Resource
    private Dlt645DataRepository dlt645DataRepository;

    // 网关服务
    @Resource
    private Dlt645GatewayService dlt645GatewayService;

    // 创建定时任务调度器
    ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    // 数据缓存，键为设备SN，值为设备的最新数据列表
    private final Cache<String, List<Dlt645DataItemDTO>> dataCache = Caffeine.newBuilder()
            // 缓存过期
            .expireAfterWrite(5, TimeUnit.SECONDS)
            // 最大缓存容量
            .maximumSize(10000)
            .removalListener((String sn, List<Dlt645DataItemDTO> dlt645DataItemList, RemovalCause cause) -> {
                log.info("缓存移除原因：{}，设备SN：{}", cause, sn);
                if (cause == RemovalCause.EXPIRED) {
                    log.info("缓存过期，触发数据上报：{}", sn);
                    try {
                        reportData(sn, Dlt645Utils.getCurrentDataTime(), dlt645DataItemList);
                    } catch (Dlt645Exception e) {
                        log.error("缓存过期数据上报失败：{}", e.getMessage(), e);
                    }
                }
            })
            .build();

    /**
     * 初始化方法，启动定时任务
     */
    @PostConstruct
    public void init() {
        // 每 5 秒触发一次过期检查
        scheduler.scheduleAtFixedRate(dataCache::cleanUp, 5, 5, TimeUnit.SECONDS);
    }

    @Override
    @MongoTransactional
    public void parse(Dlt645ResDataDTO dlt645ResData) {
        List<Dlt645ResDataItemDTO> dataItemList = dlt645ResData.getDataItemList();
        if (CollUtil.isNotEmpty(dataItemList)) {
            dataItemList.forEach(dataItem -> {
                String address = dlt645ResData.getAddress();
                if (Dlt645DiGroupCode.getDeviceDiQueueCopy().stream().anyMatch(byteArray -> Arrays.equals(dataItem.getDiByte(), byteArray))) {
                    // 保存设备信息
                    dlt645DeviceService.save(address, dataItem);
                } else if (Dlt645DiGroupCode.getEnergyDiQueueCopy().stream().anyMatch(byteArray -> Arrays.equals(dataItem.getDiByte(), byteArray))) {
                    Dlt645Data dlt645Data = new Dlt645Data();
                    dlt645Data.setAddress(address);
                    // 根据地址获取设备SN
                    Optional<String> snOptional = dlt645DeviceService.getSnByAddress(address);
                    if(snOptional.isPresent()) {
                        // 序列号
                        String sn = snOptional.get();
                        dlt645Data.setSn(snOptional.get());
                        dlt645Data.setDiCode(dataItem.getDiCode());
                        if (dataItem.getDi().equals(Dlt645DiCode.METER_STATUS_WORD_3.getDi())) {
                            // 处理电表运行状态字3
                            dlt645Data.setValue(String.valueOf(dataItem.getValue().charAt(11)));
                        } else {
                            dlt645Data.setValue(dataItem.getValue());
                        }
                        dlt645Data.setDataTime(Dlt645Utils.getCurrentDataTime());
                        // 保存抄表数据到数据库
                        dlt645DataRepository.save(dlt645Data);

                        List<Dlt645DataItemDTO> cachedData = dataCache.getIfPresent(sn);
                        if (ObjUtil.isNull(cachedData)) {
                            cachedData = new ArrayList<>();
                            log.info("初始化缓存：{}", sn);
                        }
                        cachedData.add(new Dlt645DataItemDTO(dataItem.getDiCode(), dlt645Data.getValue(), Dlt645Utils.getCurrentDataTime()));
                        // 更新缓存
                        dataCache.put(sn, cachedData);
                        log.info("数据已添加到缓存：{} -> {}", sn, dataItem);

                        Queue<byte[]> pendingDiQueueByAddress = dlt645GatewayService.getPendingDiQueueByAddress(address);
                        if (ObjUtil.isNotNull(pendingDiQueueByAddress)) {
                            // 从待执行队列中移除任务
                            byte[] di = pendingDiQueueByAddress.poll();
                            if (ObjUtil.isNotNull(di)) {
                                log.info("从已完成队列中移除任务：{}，移除后的队列长度：{}", address, pendingDiQueueByAddress.size());
                            }
                            if (pendingDiQueueByAddress.isEmpty()) {
                                Date dataTime = Dlt645Utils.getCurrentDataTime();
                                log.info("从站{}数据接收完整，将上报到RabbitMQ！", sn);
                                try {
                                    // 触发数据上报
                                    reportData(sn, dataTime);
                                } catch (Dlt645Exception e) {
                                    throw new RuntimeException("数据上报失败：" + ThrowableUtils.getStackTrace(e));
                                }
                            }
                        }
                    } else {
                        log.info("设备地址：{} 序列号为空，暂不持久化！", address);
                    }
                } else {
                    log.info("未知的Dlt645DiGroupCode, address → {}, diCode → {}", address, Dlt645Utils.byteArrayToHexString(dataItem.getDiByte()));
                }
            });
        }
    }

    /**
     * 上报数据到RabbitMQ
     *
     * @param sn       设备序列号
     * @param dataTime 数据时间
     * @throws Dlt645Exception 如果上报失败
     */
    private void reportData(String sn, Date dataTime) throws Dlt645Exception {
        // 通过序列号获取数据明细列表
        List<Dlt645DataItemDTO> dlt645DataItemList = dataCache.getIfPresent(sn);
        this.reportData(sn, dataTime, dlt645DataItemList);
    }

    /**
     * 上报数据
     *
     * @param sn 序列号
     * @param dataTime 数据时间
     * @param dlt645DataItemList 数据明细列表
     */
    private void reportData(String sn, Date dataTime, List<Dlt645DataItemDTO> dlt645DataItemList) throws Dlt645Exception {
        // 获取设备信息
        Optional<Dlt645Device> optional = dlt645DeviceService.getOneBySn(sn);
        if(optional.isPresent()) {
            // 设备数据
            Dlt645Device dlt645Device = optional.get();
            // 协议版本号
            String protocolVersion = dlt645Device.getProtocolVersion();
            // 电表型号
            String meterModel = dlt645Device.getMeterModel();
            if (CollUtil.isNotEmpty(dlt645DataItemList)) {
                Dlt645DataDTO dlt645DataDTO = new Dlt645DataDTO();
                // 协议版本
                dlt645DataDTO.setVer(protocolVersion);
                // 设备型号
                dlt645DataDTO.setPKey(meterModel);
                // 设备序列号
                dlt645DataDTO.setSn(sn);
                // 数据时间
                dlt645DataDTO.setTs(dataTime);

                Dlt645DeviceDTO deviceDTO = new Dlt645DeviceDTO();
                // 设备型号
                deviceDTO.setDev(meterModel);
                // 设备序列号
                deviceDTO.setSysId(sn);
                // 数据时间
                deviceDTO.setTs(dataTime);
                // 数据明细
                deviceDTO.setD(dlt645DataItemList);
                // 设置设备数据
                dlt645DataDTO.setDevs(Collections.singletonList(deviceDTO));

                // 参数Map
                Map<String, Object> params = new HashMap<>(1);
                params.put("platform", platformPrefix);
                params.put("pKey", meterModel);
                params.put("sn", sn);
                String topic = HandlebarsUtils.apply(Dlt645Constants.MQ_TOPIC, params);

                // DLT645 MQ对象
                Dlt645MqDTO mqDTO = new Dlt645MqDTO();
                mqDTO.setTopic(topic);
                mqDTO.setContent(dlt645DataDTO);
                // 将数据转换为JSON字符串
                String data = JSON.toJSONString(mqDTO);
                log.info("发送设备 {} 的数据到RabbitMQ → {}", sn, data);
                // 发送数据到RabbitMQ
                rabbitTemplate.convertAndSend(
                        rabbitMqPrefix + StrPool.DOT + Dlt645Constants.EMQX_EXCHANGE,
                        rabbitMqPrefix + StrPool.DOT + Dlt645Constants.MESSAGE_PUBLISH,
                        data
                );
                // 清理缓存
                dataCache.invalidate(sn);
            }
        } else {
            log.info("{} 设备数据为空，暂不上报！", sn);
        }
    }


    @Override
    @MongoTransactional
    public void parseRegisterMsg(ChannelHandlerContext ctx, ByteBuf in) {
        // 读取注册包数据
        String hexAddress = ByteBufUtil.hexDump(in);
        // 校验地址合法性
        boolean validAddress = Dlt645Utils.isValidAddress(hexAddress);
        if (validAddress) {
            // 格式化地址
            String address = Dlt645Utils.formatAddressString(hexAddress);
            // 保存设备
            dlt645DeviceService.save(address, ctx);
        }
    }

    @Override
    public Result<String> reportData(GatewayReportDTO reportDTO) {
        // 网关序列号
        String sn = reportDTO.getCode();
        if(StrUtil.isBlank(sn)) {
            // 序列号为空，则上报所有设备
            List<Dlt645Device> dlt645DeviceList = dlt645DeviceService.getList();
            if(CollUtil.isNotEmpty(dlt645DeviceList)) {
                for (Dlt645Device dlt645Device : dlt645DeviceList) {
                    // 设备序列号
                    sn = dlt645Device.getSn();
                    // 开始时间
                    Date sdt = DateUtil.parse(reportDTO.getSdt());
                    // 结束时间
                    Date edt = DateUtil.parse(reportDTO.getEdt());
                    // 循环获取对应时间数据
                    for (Date dataTime = sdt; dataTime.compareTo(edt) <= 0; dataTime = DateUtil.offsetDay(dataTime, 1)) {
                        // 结束时间
                        Date dataEndTime = DateUtil.offsetDay(dataTime, 1);
                        log.info("查询数据，sn → {}, sdt → {}, edt → {}", sn, dataTime, dataEndTime);
                        // 获取数据
                        List<Dlt645DataItemDTO> dlt645DataItemList = dlt645DataRepository.getListAsDTO(sn, DateUtil.formatDateTime(dataTime), DateUtil.formatDateTime(dataEndTime));
                        log.info("上报数据，dlt645DataItemList → {}", dlt645DataItemList);
                        if(CollUtil.isNotEmpty(dlt645DataItemList)) {
                            // 上报数据
                            try {
                                this.reportData(sn, dataTime, dlt645DataItemList);
                            } catch (Dlt645Exception e) {
                                log.error("Report Data Error，sn → {}, dataTime → {}, dlt645DataItemList → {}", sn, dataTime, dlt645DataItemList);
                            }
                        }
                    }
                }
            }
        }


        return Result.ok();
    }
}
