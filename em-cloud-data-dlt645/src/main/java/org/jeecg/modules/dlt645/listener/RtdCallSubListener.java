package org.jeecg.modules.dlt645.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.iot.mqtt.codec.MqttQoS;
import net.dreamlu.iot.mqtt.spring.client.MqttClientSubscribe;
import org.jeecg.modules.dlt645.exception.Dlt645Exception;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645RealTimeDataDTO;
import org.jeecg.modules.dlt645.service.Dlt645GatewayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 召读实时数据监听器
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@Component
public class RtdCallSubListener {
    @Autowired
    private Dlt645GatewayService dlt645GatewayService;

    /**
     * 数据订阅
     *
     * @param topic
     * @param payload
     */
    @MqttClientSubscribe(value = "/4g/cloud/${pKey}/${sn}/rtg/call", qos = MqttQoS.QOS2)
    public void subRtgCall(String topic, byte[] payload) throws Dlt645Exception {
        String realTimeDataCallPayload = new String(payload, StandardCharsets.UTF_8);
        log.info("topic:{} payload:{}", topic, realTimeDataCallPayload);
        // 解析数据
        Dlt645RealTimeDataDTO realTimeDataDTO = JSON.parseObject(realTimeDataCallPayload, Dlt645RealTimeDataDTO.class);
        // 下发指令
        dlt645GatewayService.sendReadDataMsg(realTimeDataDTO.getSn());
    }
}
