package org.jeecg.modules.dlt645.pojo.dto;

import lombok.Data;
import org.jeecg.modules.dlt645.util.Dlt645Utils;

import java.io.Serializable;

/**
 * DLT645数据帧
 *
 * 包含地址域、控制码、数据长度 L、数据域、校验码和结束符
 *
 * <AUTHOR>
 * @date 2024-12-23
 */
@Data
public class Dlt645FrameDTO implements Serializable {
    private static final long serialVersionUID = 8073059133164894379L;
    /**
     * 地址域 A0～A5
     */
    private byte[] address;
    /**
     * 帧控制码
     */
    private byte controlCode;
    /**
     * 数据长度 L
     */
    private int dataLength;
    /**
     * 数据域 DATA
     */
    private byte[] data;
    /**
     * 校验码 CS
     */
    private byte checkCode;

    public void setAddress(byte[] address) {
        this.address = Dlt645Utils.reverseByteArray(address);;
    }

    public void setData(byte[] data) {
        this.data = Dlt645Utils.decode(data);
        if (data != null) {
            this.dataLength = data.length;
        } else {
            this.dataLength = 0;
        }
    }
}
