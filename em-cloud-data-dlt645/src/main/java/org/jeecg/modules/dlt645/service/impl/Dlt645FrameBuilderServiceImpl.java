package org.jeecg.modules.dlt645.service.impl;

import org.jeecg.modules.dlt645.constant.ControlReqCode;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ReqDataDTO;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ReqDataItemDTO;
import org.jeecg.modules.dlt645.service.Dlt645FrameBuilderService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * DLT645 指令构建器实现类
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@Service
public class Dlt645FrameBuilderServiceImpl implements Dlt645FrameBuilderService {
    @Override
    public Dlt645ReqDataDTO buildMessage(String address, ControlReqCode controlCode, List<Dlt645ReqDataItemDTO> dataItemList) {
        Dlt645ReqDataDTO reqMessage = new Dlt645ReqDataDTO();
        reqMessage.setAddress(address);
        reqMessage.setControlCode(controlCode);
        reqMessage.setDataItemList(dataItemList);
        return reqMessage;
    }
}
