package org.jeecg.modules.dlt645.pojo.dto;

import lombok.Data;
import org.jeecg.modules.dlt645.constant.ControlReqCode;
import org.jeecg.modules.dlt645.util.Dlt645Utils;

import java.io.Serializable;
import java.util.List;

/**
 * DLT645 协议请求消息类
 *
 * <AUTHOR>
 * @date 2024-12-23
 */
@Data
public class Dlt645ReqDataDTO implements Serializable {
    private static final long serialVersionUID = -3001917801650639851L;
    /**
     * 地址域 A0～A5
     */
    private byte[] address;
    /**
     * 请求控制码
     */
    private byte controlCode;
    /**
     * 数据域
     */
    private List<Dlt645ReqDataItemDTO> dataItemList;

    /**
     * 设置地址域，输入为 6 字节的 BCD 编码字节数组。
     * 如果输入的字节数组长度不足 6 字节，则高位用 0x00 补足。
     * 每个字节的高 4 位和低 4 位必须是有效的 BCD 码（0-9）。
     *
     * @param address 输入的 BCD 编码字节数组
     * @throws IllegalArgumentException 如果输入的字节数组不符合 BCD 编码规则
     */
    public void setAddress(String address) {
        // 将 BCD 编码的字节数组转换为 12 位十进制字符串
        this.address = Dlt645Utils.convertAddressStringToBcdBytes(address);
    }

    /**
     * 设置控制码
     *
     * @param controlCode
     */
    public void setControlCode(ControlReqCode controlCode) {
        this.controlCode = controlCode.code();
    }
}
