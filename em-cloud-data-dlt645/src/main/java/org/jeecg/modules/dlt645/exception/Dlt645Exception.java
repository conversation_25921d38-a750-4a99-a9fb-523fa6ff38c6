package org.jeecg.modules.dlt645.exception;

/**
 * DLT645 协议解析异常
 *
 * 用于在解析 DLT645 协议数据时抛出明确的错误信息。
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
public class Dlt645Exception extends Exception {

    private static final long serialVersionUID = -8294115854690583053L;

    /**
     * 构造方法：创建一个不带详细信息的 DLT645 解析异常
     */
    public Dlt645Exception() {
        super();
    }

    /**
     * 构造方法：创建一个带有详细信息的 DLT645 解析异常
     *
     * @param message 异常信息
     */
    public Dlt645Exception(String message) {
        super(message);
    }

    /**
     * 构造方法：创建一个带有详细信息和原因的 DLT645 解析异常
     *
     * @param message 异常信息
     * @param cause   异常原因
     */
    public Dlt645Exception(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造方法：创建一个带有原因的 DLT645 解析异常
     *
     * @param cause 异常原因
     */
    public Dlt645Exception(Throwable cause) {
        super(cause);
    }
}
