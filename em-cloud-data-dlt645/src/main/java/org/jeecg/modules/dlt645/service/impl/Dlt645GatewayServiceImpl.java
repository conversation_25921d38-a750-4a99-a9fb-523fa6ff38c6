package org.jeecg.modules.dlt645.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.dlt645.constant.*;
import org.jeecg.modules.dlt645.exception.Dlt645Exception;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ControlValveDTO;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ReqDataDTO;
import org.jeecg.modules.dlt645.service.Dlt645DeviceService;
import org.jeecg.modules.dlt645.service.Dlt645FrameBuilderService;
import org.jeecg.modules.dlt645.service.Dlt645GatewayService;
import org.jeecg.modules.dlt645.util.Dlt645Utils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

/**
 * DLT645网关Service实现类
 * 负责与DLT645设备通信，包括发送读取数据、控制命令等操作。
 *
 * <AUTHOR>
 * @date  2024-12-27
 */
@Service
@Slf4j
public class Dlt645GatewayServiceImpl implements Dlt645GatewayService {
    /**
     * 注入设备服务，用于管理设备通道上下文
     */
    @Resource
    private Dlt645DeviceService dlt645DeviceService;

    /**
     * 注入数据帧构建服务，用于构建DLT645协议帧
     */
    @Resource
    private Dlt645FrameBuilderService dlt645FrameBuilderService;

    /**
     * 设备任务管理器，负责任务队列和调度器的管理
     */
    private final DeviceTaskManager deviceTaskManager = new DeviceTaskManager();

    /**
     * 缓存待响应的指令队列，键为设备地址，值为待响应的指令队列
     */
    private final ConcurrentHashMap<String, Queue<byte[]>> pendingDiQueueCache = new ConcurrentHashMap<>();

    /**
     * 获取指定地址的待响应指令队列
     *
     * @param address 设备地址
     * @return 待响应指令队列
     */
    @Override
    public Queue<byte[]> getPendingDiQueueByAddress(String address) {
        return pendingDiQueueCache.get(address);
    }

    /**
     * 发送读取设备信息的消息
     *
     * @param address 设备地址
     */
    @Override
    public void sendReadDeviceInfoMsg(String address) {
        // 创建任务队列
        Queue<Dlt645PrioritizedTask> deviceQueue = new ConcurrentLinkedQueue<>();
        // 遍历设备DI队列，为每个DI创建任务
        Dlt645DiGroupCode.getDeviceDiQueueCopy().forEach(diByte -> addTaskToQueue(deviceQueue, address, diByte));
        // 提交任务到调度器
        submitTaskToScheduler(address, deviceQueue);
    }

    @Override
    public void sendReadDeviceInfoMsg(String address, Queue<byte[]> deviceDiQueue) {
        // 创建任务队列
        Queue<Dlt645PrioritizedTask> deviceQueue = new ConcurrentLinkedQueue<>();
        // 遍历设备DI队列，为每个DI创建任务
        deviceDiQueue.forEach(diByte -> addTaskToQueue(deviceQueue, address, diByte));
        // 提交任务到调度器
        submitTaskToScheduler(address, deviceQueue);
    }

    /**
     * 批量发送读取数据的消息
     */
    @Override
    public void batchSendReadDataMsg() {
        // 获取所有设备的通道上下文
        Map<String, ChannelHandlerContext> addressToChannelMap = dlt645DeviceService.getAddressToChannel();
        if (MapUtil.isEmpty(addressToChannelMap)) {
            log.error("未找到任何设备的通道上下文！");
            return;
        }
        // 并行处理设备地址和通道上下文
        addressToChannelMap.entrySet().parallelStream().forEach(addressToChannel -> {
            // 通讯地址
            String address = addressToChannel.getKey();
            // 通道上下文
            ChannelHandlerContext ctx = addressToChannel.getValue();
            // 获取设备信息
            Optional<String> optional = dlt645DeviceService.getMeterModelByAddress(address);
            // 设备型号不为空，获取型号对应的DI
            if(optional.isPresent()) {
                // 能源DI队列
                Queue<byte[]> energyDiQueue = Dlt645DiGroupCode.getEnergyDiQueueCopy(optional.get());
                if(CollUtil.isNotEmpty(energyDiQueue)) {
                    // 发送抄数据指令
                    sendReadDataMsg(address, energyDiQueue, ctx);
                }
            } else {
                // 发送抄数据指令
                sendReadDataMsg(address, ctx);
            }
        });
    }

    /**
     * 发送读取数据的消息
     *
     * @param sn 设备序列号
     */
    @Override
    public void sendReadDataMsg(String sn) {
        // 通过序列号获取设备地址
        Optional<String> optional = dlt645DeviceService.getAddressBySn(sn);
        if (optional.isPresent()) {
            String address = optional.get();
            // 通过地址获取通道上下文
            ChannelHandlerContext ctx = dlt645DeviceService.getChannelByAddress(address);
            sendReadDataMsg(address, ctx);
        } else {
            log.info("{} 设备地址为空，暂不下发指令！", sn);
        }
    }

    /**
     * 发送读取数据的消息（按地址）
     *
     * @param address 设备地址
     */
    @Override
    public void sendReadDataMsgByAddress(String address) {
        // 通过地址获取通道上下文
        ChannelHandlerContext ctx = dlt645DeviceService.getChannelByAddress(address);
        sendReadDataMsg(address, ctx);
    }

    /**
     * 发送读取数据的消息（内部调用）
     *
     * @param address 设备地址
     * @param ctx     通道上下文
     */
    private void sendReadDataMsg(String address, ChannelHandlerContext ctx) {
        // 获取能源数据DI队列
        Queue<byte[]> energyDiQueue = Dlt645DiGroupCode.getEnergyDiQueueCopy();
        // 发送抄数据指令
        sendReadDataMsg(address, energyDiQueue, ctx);
    }

    /**
     * 发送读取数据的消息（内部调用）
     *
     * @param address 设备地址
     * @param ctx     通道上下文
     */
    private void sendReadDataMsg(String address, Queue<byte[]> energyDiQueue, ChannelHandlerContext ctx) {
        if (checkChannelContext(address, ctx)) {
            return;
        }
        // 创建能源数据读取任务队列
        Queue<Dlt645PrioritizedTask> energyQueue = new ConcurrentLinkedQueue<>();
        // 遍历能源数据DI队列，为每个DI创建任务
        energyDiQueue.forEach(diByte -> addTaskToQueue(energyQueue, address, diByte));
        // 记录待响应的指令队列
        pendingDiQueueCache.compute(address, (k, v) -> {
            if (v == null) {
                return new ConcurrentLinkedQueue<>(energyDiQueue);
            } else {
                v.addAll(energyDiQueue);
                return v;
            }
        });
        // 提交任务到调度器
        submitTaskToScheduler(address, energyQueue);
    }

    /**
     * 发送广播校时消息
     */
    @Override
    public void sendBroadcastDatetimeMsg() {
        // 获取所有设备的通道上下文
        Map<String, ChannelHandlerContext> addressToChannel = dlt645DeviceService.getAddressToChannel();
        if (MapUtil.isEmpty(addressToChannel)) {
            log.error("未找到任何设备的通道上下文！");
            return;
        }
        // 遍历所有设备，提交广播校时任务到调度器
        addressToChannel.forEach((address, ctx) -> {
            if (checkChannelContext(address, ctx)) {
                return;
            }
            // 创建广播校时任务队列
            Queue<Dlt645PrioritizedTask> broadcastQueue = new ConcurrentLinkedQueue<>();
            // 添加广播校时任务
            broadcastQueue.add(new Dlt645PrioritizedTask(() -> {
                // 构建广播校时命令
                Dlt645ReqDataDTO dlt645ReqData = dlt645FrameBuilderService.buildBroadcastTimeSyncCommand(new Date());
                // 发送广播校时命令
                ctx.writeAndFlush(dlt645ReqData);
            }, Dlt645TaskPriority.LOW));
            // 提交任务到调度器
            submitTaskToScheduler(address, broadcastQueue);
        });
    }

    /**
     * 发送跳闸命令
     *
     * @param address 设备地址
     */
    @Override
    public void sendTripCommand(String address) {
        // 提交跳闸命令任务到调度器
        submitControlCommand(address, ControlReqCode.TRIP);
    }

    /**
     * 发送合闸命令
     *
     * @param address 设备地址
     */
    @Override
    public void sendCloseCommand(String address) {
        // 提交合闸命令任务到调度器
        submitControlCommand(address, ControlReqCode.CLOSE);
    }

    /**
     * 发送阀控命令
     *
     * @param dlt645ControlValve 阀控命令DTO
     * @throws Dlt645Exception 如果获取地址失败
     */
    @Override
    public void sendControlValve(Dlt645ControlValveDTO dlt645ControlValve) throws Dlt645Exception {
        // 获取设备序列号
        String sn = dlt645ControlValve.getSn();
        // 获取阀控状态
        Boolean valveStatus = dlt645ControlValve.getValveStatus();
        // 根据序列号获取设备地址
        Optional<String> optional = dlt645DeviceService.getAddressBySn(sn);
        if (optional.isPresent()) {
            String address = optional.get();
            if (Boolean.TRUE.equals(valveStatus)) {
                // 跳闸
                sendTripCommand(address);
            } else {
                // 合闸
                sendCloseCommand(address);
            }
        } else {
            log.error("序列号获取地址对应通讯信道为空！");
        }
    }

    /**
     * 发送读取信号强度消息
     *
     * @param address 设备地址
     */
    @Override
    public void sendReadSignalStrengthMsg(String address) {
        // 创建信号强度读取任务队列
        Queue<Dlt645PrioritizedTask> signalQueue = new ConcurrentLinkedQueue<>();
        // 添加信号强度读取任务
        signalQueue.add(new Dlt645PrioritizedTask(() -> {
            // 通过地址获取通道上下文
            ChannelHandlerContext ctx = dlt645DeviceService.getChannelByAddress(address);
            if (checkChannelContext(address, ctx)) {
                return;
            }
            // 发送单个数据请求（信号强度）
            sendSingleDataRequest(address, ctx, Dlt645DiCode.DATA_NETWORK_SIGNAL_STRENGTH.getDiByte());
        }, Dlt645TaskPriority.MEDIUM));
        // 提交任务到调度器
        submitTaskToScheduler(address, signalQueue);
    }

    /**
     * 提交控制命令任务到调度器
     *
     * @param address  设备地址
     * @param reqCode  控制请求码
     */
    private void submitControlCommand(String address, ControlReqCode reqCode) {
        // 创建控制命令任务队列
        Queue<Dlt645PrioritizedTask> controlQueue = new ConcurrentLinkedQueue<>();
        // 添加控制命令任务
        controlQueue.add(new Dlt645PrioritizedTask(() -> {
            // 通过地址获取通道上下文
            ChannelHandlerContext ctx = dlt645DeviceService.getChannelByAddress(address);
            if (checkChannelContext(address, ctx)) {
                return;
            }
            // 构建控制命令帧
            Dlt645ReqDataDTO dlt645ReqData = dlt645FrameBuilderService.buildControlValveCommand(address, reqCode);
            // 发送控制命令
            ctx.writeAndFlush(dlt645ReqData);
        }, Dlt645TaskPriority.LOW));
        // 提交任务到调度器
        submitTaskToScheduler(address, controlQueue);
    }

    /**
     * 发送单个数据请求
     *
     * @param address 设备地址
     * @param ctx     通道上下文
     * @param diByte  DI字节码
     */
    private void sendSingleDataRequest(String address, ChannelHandlerContext ctx, byte[] diByte) {
        // 检查通道是否激活
        if (ctx.channel().isActive()) {
            // 构建读取单个数据的请求帧
            Dlt645ReqDataDTO dlt645ReqData = dlt645FrameBuilderService.buildReadSingleDataCommand(address, diByte);
            // 发送请求帧
            ctx.writeAndFlush(dlt645ReqData).addListener(futureListener -> {
                if (futureListener.isSuccess()) {
                    log.info("请求帧成功发送到地址 {}，DI: {}, CODE: {}", address, Dlt645Utils.byteArrayToHexString(diByte), Dlt645DiCode.getCode(diByte));
                } else {
                    log.error("请求帧发送失败，地址: {}", address, futureListener.cause());
                }
            });
        } else {
            log.error("通道未激活，地址: {}", address);
            dlt645DeviceService.unregisterDevice(address);
        }
    }

    /**
     * 添加任务到队列
     *
     * @param queue   任务队列
     * @param address 设备地址
     * @param diByte  DI字节码
     */
    private void addTaskToQueue(Queue<Dlt645PrioritizedTask> queue, String address, byte[] diByte) {
        // 添加读取数据任务
        queue.add(new Dlt645PrioritizedTask(() -> {
            // 通过地址获取通道上下文
            ChannelHandlerContext ctx = dlt645DeviceService.getChannelByAddress(address);
            if (checkChannelContext(address, ctx)) {
                return;
            }
            // 发送单个数据请求
            sendSingleDataRequest(address, ctx, diByte);
        }, Dlt645TaskPriority.HIGH));
    }

    /**
     * 提交任务到调度器
     *
     * @param address 设备地址
     * @param queue   任务队列
     */
    private void submitTaskToScheduler(String address, Queue<Dlt645PrioritizedTask> queue) {
        // 提交任务到设备任务管理器
        deviceTaskManager.submitTask(address, queue);
    }

    /**
     * 检查通道上下文是否为空
     *
     * @param address 设备地址
     * @param ctx     通道上下文
     * @return 如果通道上下文为空，记录日志并返回false，否则返回true
     */
    private boolean checkChannelContext(String address, ChannelHandlerContext ctx) {
        if (ObjUtil.isNull(ctx)) {
            log.error("设备地址 {} 的通道上下文为空！", address);
            return true;
        }
        return false;
    }

    /**
     * 设备任务管理器，负责任务队列和调度器的管理
     */
    private static class DeviceTaskManager {
        // 缓存任务队列，键为设备地址，值为任务队列
        private final ConcurrentHashMap<String, Queue<Dlt645PrioritizedTask>> taskQueueCache = new ConcurrentHashMap<>();

        // 缓存调度器，键为设备地址，值为调度器实例
        private final ConcurrentHashMap<String, ScheduledExecutorService> schedulerCache = new ConcurrentHashMap<>();

        /**
         * 提交任务到调度器
         *
         * @param address 设备地址
         * @param queue   任务队列
         */
        public void submitTask(String address, Queue<Dlt645PrioritizedTask> queue) {
            // 检查任务队列是否为空
            if (CollUtil.isEmpty(queue)) {
                log.warn("任务队列为空，未提交任何任务！");
                return;
            }
            // 获取当前设备的任务队列
            Queue<Dlt645PrioritizedTask> taskQueue = taskQueueCache.computeIfAbsent(address, k ->
                    new PriorityQueue<>(Comparator.comparing(Dlt645PrioritizedTask::getPriority, Comparator.comparingInt(Dlt645TaskPriority::getPriorityValue)))
            );
            // 将任务加入队列
            taskQueue.addAll(queue);
            // 获取或创建调度器
            ScheduledExecutorService scheduler = schedulerCache.compute(address, (k, v) -> {
                if (v == null || v.isTerminated()) {
                    return Executors.newScheduledThreadPool(1);
                }
                return v;
            });
            // 提交任务到调度器
            scheduler.scheduleAtFixedRate(() -> {
                Dlt645PrioritizedTask task = taskQueue.poll();
                if (task != null) {
                    task.run();
                } else {
                    log.info("任务队列已处理完毕，停止任务调度。");
                    // �移除并关闭调度器
                    ScheduledExecutorService removedScheduler = schedulerCache.remove(address);
                    if (ObjUtil.isNotNull(removedScheduler)) {
                        removedScheduler.shutdown();
                    }
                    // 清理任务队列缓存
                    taskQueueCache.remove(address);
                }
            }, 0, Dlt645Constants.DELAY_SECONDS, TimeUnit.MILLISECONDS);
        }
    }
}
