package org.jeecg.modules.dlt645.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Arrays;

/**
 * DL/T645 协议控制码枚举
 *
 * 该类定义了 DL/T645 协议中主站请求的控制码及其描述信息。
 * 每个枚举实例包含控制码的字节值和描述信息，并支持根据字节值查找对应的枚举实例。
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
@Getter
@ToString
@Accessors(fluent = true)
@RequiredArgsConstructor
public enum ControlReqCode {
    // 注册设备
    REGISTER_DEVICE((byte) 0x00, "注册设备"),

    // 设备心跳
    HEART_BEAT((byte) 0x61, "设备心跳"),

    // 读数据
    READ_DATA((byte) 0x11, "读数据"),

    // 写数据
    WRITE_DATA((byte) 0x14, "写数据"),

    // 读通信地址
    READ_ADDRESS((byte) 0x13, "读通信地址"),

    // 写通信地址
    WRITE_ADDRESS((byte) 0x15, "写通信地址"),

    // 冻结命令
    FREEZE_COMMAND((byte) 0x16, "冻结命令"),

    // 广播校时
    BROADCAST_TIME_SYNC((byte) 0x08, "广播校时"),

    // 更改通信速率
    MODIFY_BAUD_RATE((byte) 0x17, "更改通信速率"),

    // 修改密码
    MODIFY_PASSWORD((byte) 0x18, "修改密码"),

    // 最大需量清零
    CLEAR_MAX_DEMAND((byte) 0x19, "最大需量清零"),

    // 电表清零
    CLEAR_METER((byte) 0x1A, "电表清零"),

    // 事件清零
    CLEAR_EVENTS((byte) 0x1B, "事件清零"),

    // 跳合闸、保电
    TRIP_CLOSE_POWER_PROTECTION((byte) 0x1C, "跳合闸、保电"),

    // 跳闸
    TRIP((byte) 0x1A, "跳闸"),

    // 合闸允许
    CLOSE_ALLOWED((byte) 0x1B, "合闸允许"),

    // 直接合闸
    CLOSE((byte) 0x1C, "直接合闸"),

    // 保电
    POWER_PROTECT((byte) 0x3A, "保电"),

    // 保电解除
    POWER_UNPROTECT((byte) 0x3B, "保电解除"),

    // 多功能端子输出控制命令
    MULTIFUNCTION_TERMINAL_OUTPUT((byte) 0x1D, "多功能端子输出控制命令"),

    // 未知控制码
    UNKNOWN((byte) -1, "未知控制码");

    /**
     * 控制码的字节值
     */
    private final byte code;

    /**
     * 控制码的描述信息
     */
    private final String description;

    /**
     * 根据字节值获取对应的 ControlReqCode 枚举实例
     *
     * @param code 控制码的字节值
     * @return 对应的 ControlReqCode 枚举实例，如果找不到则返回 UNKNOWN
     */
    public static ControlReqCode fromByte(byte code) {
        return Arrays.stream(values())
                .filter(controlCode -> controlCode.code() == code)
                .findFirst()
                .orElse(UNKNOWN);
    }

    /**
     * 判断当前控制码是否为未知控制码
     *
     * @return 如果是未知控制码则返回 true，否则返回 false
     */
    public boolean isUnknown() {
        return this == UNKNOWN;
    }

    /**
     * 获取控制码的十六进制字符串表示
     *
     * @return 控制码的十六进制字符串
     */
    public String toHexString() {
        return String.format("0x%02X", code);
    }
}
