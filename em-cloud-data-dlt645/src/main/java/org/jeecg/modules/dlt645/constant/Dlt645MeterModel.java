package org.jeecg.modules.dlt645.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 型号枚举
 *
 * <AUTHOR>
 * @date 2025-01-20 16:50
 */
@Getter
@ToString
@Accessors(fluent = true)
@AllArgsConstructor
public enum Dlt645MeterModel {
    PMC_320_W("PMC-320-W"),
    PMC_340_W("PMC-340-W");

    /**
     * 型号
     */
    private final String code;
}
