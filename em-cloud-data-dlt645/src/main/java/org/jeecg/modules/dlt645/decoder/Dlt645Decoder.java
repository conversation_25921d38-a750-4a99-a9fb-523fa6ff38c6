package org.jeecg.modules.dlt645.decoder;

import cn.hutool.core.util.ObjUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.dlt645.constant.ControlResCode;
import org.jeecg.modules.dlt645.constant.Dlt645Constants;
import org.jeecg.modules.dlt645.convert.Dlt645FrameConvert;
import org.jeecg.modules.dlt645.exception.Dlt645Exception;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645FrameDTO;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ResDataDTO;
import org.jeecg.modules.dlt645.service.Dlt645DataService;
import org.jeecg.modules.dlt645.util.Dlt645Utils;

import java.util.List;

/**
 * Dlt645协议解码器
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Slf4j
public class Dlt645Decoder extends ByteToMessageDecoder {
    /**
     * DL/T645消息Service
     */
    private final Dlt645DataService dlt645DataService;

    /**
     * 缓存多帧数据
     */
    private final Cache<String, byte[]> frameCache = Caffeine.newBuilder().build();

    /**
     * 构造函数
     *
     * @param dlt645DataService 消息
     */
    public Dlt645Decoder(Dlt645DataService dlt645DataService) {
        this.dlt645DataService = dlt645DataService;
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Dlt645Exception {
        log.info("DL/T645数据帧：{}", ByteBufUtil.prettyHexDump(in));
        // 确保有足够的字节来读取完整的帧，最小帧长度为 13 字节
        int packageLength = in.readableBytes();
        if (packageLength < 13) {
            // 如果是5字节，则认为是注册包/心跳包
            if (packageLength == Dlt645Constants.REGISTER_PACKET_LENGTH) {
                dlt645DataService.parseRegisterMsg(ctx, in);
            }
            // 数据不完整，跳过
            in.skipBytes(packageLength);
        } else {
            // 读取帧起始符，必须是 0x68
            byte frameHeader = in.readByte();
            if (frameHeader != Dlt645Constants.FRAME_HEADER) {
                log.error("Invalid frame header: {}", frameHeader);
                in.skipBytes(packageLength - 1);
                return;
            }
            // 读取地址域 (6 字节)
            byte[] address = new byte[6];
            in.readBytes(address);
            // 读取帧起始符，必须是 0x68
            byte frameStartChar = in.readByte();
            if (frameStartChar != Dlt645Constants.FRAME_HEADER) {
                log.error("Invalid frame start: {}", frameStartChar);
                in.skipBytes(packageLength - 8);
                return;
            }
            // 读取控制码
            byte controlCode = in.readByte();
            // 读取数据长度
            int dataLength = in.readUnsignedByte();
            // 检查数据域是否完整
            if (in.readableBytes() < dataLength + 2) {
                log.warn("数据域不完整，剩余字节: {}", in.readableBytes());
                in.resetReaderIndex();
                return;
            }
            // 读取数据域
            byte[] data = new byte[dataLength];
            in.readBytes(data);
            // 读取校验码
            byte checkCode = in.readByte();
            // 读取帧尾
            byte frameTail = in.readByte();
            if (frameTail != Dlt645Constants.FRAME_TAIL) {
                log.error("Invalid frame tail: {}", frameTail);
                in.skipBytes(in.readableBytes());
                return;
            }
            // 验证校验码
            boolean isValid = Dlt645Utils.verifyCheckCode(address, controlCode, dataLength, data, checkCode);
            if (!isValid) {
                log.error("CheckCode verification failed: {}", ByteBufUtil.hexDump(data));
                return;
            }
            // 处理多帧数据
            if (controlCode == ControlResCode.READ_DATA_FOLLOWING.code()) {
                // 有后续帧，缓存当前帧数据
                String addressKey = ByteBufUtil.hexDump(address);
                byte[] cachedData = frameCache.getIfPresent(addressKey);
                if (cachedData == null) {
                    frameCache.put(addressKey, data);
                } else {
                    // 拼接数据
                    byte[] newData = new byte[cachedData.length + data.length];
                    System.arraycopy(cachedData, 0, newData, 0, cachedData.length);
                    System.arraycopy(data, 0, newData, cachedData.length, data.length);
                    frameCache.put(addressKey, newData);
                }
                log.info("Cached frame data for address: {}", addressKey);
            } else if (controlCode == ControlResCode.READ_DATA_NO_FOLLOWING.code()) {
                // 无后续帧，处理完整数据
                String addressKey = ByteBufUtil.hexDump(address);
                byte[] cachedData = frameCache.getIfPresent(addressKey);
                if (ObjUtil.isNotNull(cachedData)) {
                    // 拼接最后一帧数据
                    byte[] newData = new byte[cachedData.length + data.length];
                    System.arraycopy(cachedData, 0, newData, 0, cachedData.length);
                    System.arraycopy(data, 0, newData, cachedData.length, data.length);
                    frameCache.invalidate(addressKey);
                    data = newData;
                }
                // 创建 Dlt645FrameDTO 对象
                Dlt645FrameDTO frame = new Dlt645FrameDTO();
                frame.setAddress(address);
                frame.setControlCode(controlCode);
                frame.setData(data);
                frame.setCheckCode(checkCode);
                // 将 Dlt645FrameDTO 转换为 Dlt645Msg
                Dlt645ResDataDTO message = Dlt645FrameConvert.INSTANCE.convert(frame);
                // 将解码后的业务消息添加到输出列表
                out.add(message);
            } else {
                log.error("Unsupported control code: {}", controlCode);
            }
        }
    }
}
