package org.jeecg.modules.dlt645.handler;

import cn.hutool.core.exceptions.ExceptionUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.ThrowableUtils;
import org.jeecg.modules.dlt645.pojo.dto.Dlt645ResDataDTO;
import org.jeecg.modules.dlt645.service.Dlt645DataService;
import org.jeecg.modules.dlt645.service.Dlt645DeviceService;

/**
 * DLT645协议处理器
 * 处理DLT645协议消息
 *
 * <AUTHOR>
 * @date 2024-12-23
 */
@Slf4j
public class Dlt645Handler extends SimpleChannelInboundHandler<Dlt645ResDataDTO> {
    /**
     * DL/T645消息Service
     */
    private final Dlt645DataService dlt645DataService;
    /**
     * DL/T645设备Service
     */
    private final Dlt645DeviceService dlt645DeviceService;

    /**
     * 构造函数
     *
     * @param dlt645DataService DL/T645消息Service
     * @param dlt645DeviceService DL/T645设备Service
     */
    public Dlt645Handler(Dlt645DataService dlt645DataService, Dlt645DeviceService dlt645DeviceService) {
        this.dlt645DataService = dlt645DataService;
        this.dlt645DeviceService = dlt645DeviceService;
    }


    @Override
    protected void channelRead0(ChannelHandlerContext ctx, Dlt645ResDataDTO dlt645ResData) {
        log.info("Netty → 客户端地址 → {}, 接收数据 → {}", ctx.channel().remoteAddress(), dlt645ResData.toString());
        dlt645DataService.parse(dlt645ResData);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.info("Netty → 异常捕获 → {}", ExceptionUtil.getMessage(cause));
        ctx.close();
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        log.info("Netty → TCP客户端服务上线：{}", channel.remoteAddress());
        // 更新上线状态
        dlt645DeviceService.updateOnline(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        try {
            Channel channel = ctx.channel();
            log.info("Netty → TCP客户端服务下线：{}", channel.remoteAddress());
            // 更新下线状态
            dlt645DeviceService.updateOffline(ctx);
        } catch (Exception e) {
            log.error("Netty → TCP客户端服务下线异常，msg → {}", ThrowableUtils.getStackTrace(e));
        } finally {
            ctx.close();
        }
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) {
        ctx.flush();
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            if (event.state() == IdleState.READER_IDLE) {
                // 关闭连接
                ctx.close();
            } else if (event.state() == IdleState.ALL_IDLE) {
                // 关闭连接
                ctx.close();
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }
}
