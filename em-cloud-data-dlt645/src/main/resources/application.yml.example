management:
  endpoints:
    web:
      exposure:
        include: metrics,httptrace

spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
    #Spring Boot 2.6+后映射匹配的默认策略已从AntPathMatcher更改为PathPatternParser, 需要手动指定为ant-path-matcher
    pathmatch:
      matching-strategy: ant-path-matcher
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  web:
    resources:
      static-locations: classpath:/static/,classpath:/public/
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          url: *************************************************************************************************************************************************************************
          username: root
          password: SFEmc5Nn%j
          driver-class-name: com.mysql.cj.jdbc.Driver
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #延迟10秒启动定时任务
    startup-delay: 1s
    #停止应用是，等待任务完成
    wait-for-jobs-to-complete-on-shutdown: true
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: EmDataDlt645Scheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 60000
            clusterCheckinInterval: 10000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 100
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    open-in-view: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  data:
    mongodb:
      uri: *************************************************************************************************************************************************************
      database: em_dev
      auto-index-creation: true
  #redis 配置
  redis:
    database: 0
    host: *************
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: 'ot9!bKiIb5'
    port: 6379
  rabbitmq:
    addresses: *************
    port: 5672
    userName: admin
    password: rabbitmq
    virtual-host: em
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      type: simple
      simple:
        # 每次从RabbitMQ获取的消息数量
        prefetch: 200
        # 【 (若有重试)重试次数超过限制后】，将被拒绝的消息重新入队
        default-requeue-rejected: true
        # 每个队列启动的消费者数量
        concurrency: 8
        # 每个队列最大的消费者数量
        max-concurrency: 10
        # 手动签收ACK
        acknowledge-mode: manual
        retry:
          # 10秒后重试
          initial-interval: 10000
    template:
      mandatory: false
  dinger:
    project-id: ${spring.application.name}
    dingers:
      # 使用钉钉机器人, 请根据自己机器人配置信息进行修改
      dingtalk:
        tokenId: 82a7d9dc6c7130618e5cd901ba1ea30ae4c1cac4ea6c3443e19b8b2f227bdacb
        secret: 1ZsCPVuptHgHJkmnXocK7XhafgUnRKX3BjHdUyQWRvNaZGh7Nv8uHRvtLj6I_F62

mqtt:
  client:
    enabled: true               # 是否开启客户端，默认：true
    ip: *************               # 连接的服务端 ip ，默认：127.0.0.1
    port: 1883                  # 端口：默认：1883
    name: DLT645-Mqtt-Client      # 名称，默认：Mica-Mqtt-Client
    clientId: dlt645-consumer-c101e12d2a005e6fa8547591d473f655            # 客户端Id（非常重要，一般为设备 sn，不可重复）
    user-name: admin             # 认证的用户名
    password: FFf^5ByJe2            # 认证的密码
    timeout: 5                  # 超时时间，单位：秒，默认：5秒
    reconnect: true             # 是否重连，默认：true
    re-interval: 5000           # 重连时间，默认 5000 毫秒
    version: mqtt_3_1_1         # mqtt 协议版本，可选 MQTT_3_1、mqtt_3_1_1、mqtt_5，默认：mqtt_3_1_1
    read-buffer-size: 8KB       # 接收数据的 buffer size，默认：8k
    max-bytes-in-message: 10MB  # 消息解析最大 bytes 长度，默认：10M
    buffer-allocator: heap      # 堆内存和堆外内存，默认：堆内存
    keep-alive-secs: 60         # keep-alive 时间，单位：秒
    clean-session: true         # mqtt clean session，默认：true

# RabbitMQ激活环境
energy-monitor:
  profiles-active:
    rabbitmq-prefix: dev

# Netty端口
netty:
  port: 8888

jeecg:
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: local
  path:
    #文件上传根目录 设置
    upload: /Users/<USER>/data
    #webapp文件路径
    webapp: /Users/<USER>/data
