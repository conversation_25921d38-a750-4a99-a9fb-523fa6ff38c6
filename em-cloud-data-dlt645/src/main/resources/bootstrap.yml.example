server:
  port: 1773

spring:
  application:
    name: em-data-dlt645
  profiles:
    active: dev
  main:
    allow-circular-references: true
  cloud:
    bus:
      id: ${spring.application.name}:${server.port}
    nacos:
      config:
        username: nacos
        password: z6v^@&Yw5O
        namespace: d8a009f5-f100-4d41-975f-dbba07f49957
        server-addr: 192.168.22.52:8848
        group: DEFAULT_GROUP
        file-extension: yaml
        prefix: em-data-dlt645
      discovery:
        namespace: d8a009f5-f100-4d41-975f-dbba07f49957
        server-addr: 192.168.22.52:8848
        watch:
          enabled: false

jeecg:
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: local
  path:
    #文件上传根目录 设置
    upload: /Users/<USER>/data
    #webapp文件路径
    webapp: /Users/<USER>/data

logging:
  level:
    org.jeecg: info
  config: classpath:logback-spring.xml
