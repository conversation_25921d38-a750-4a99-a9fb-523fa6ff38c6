<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bonait.boot</groupId>
        <artifactId>em-data-parent</artifactId>
        <version>2.3.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>em-cloud-data-dlt645</artifactId>

    <dependencies>
        <!-- 引入微服务启动依赖 starter -->
        <dependency>
            <groupId>com.bonait.boot</groupId>
            <artifactId>em-starter-cloud</artifactId>
        </dependency>
        <!-- Mongodb -->
        <dependency>
            <groupId>com.bonait.boot</groupId>
            <artifactId>em-module-mongo</artifactId>
        </dependency>
        <!-- DLT645 API -->
        <dependency>
            <groupId>com.bonait.boot</groupId>
            <artifactId>em-cloud-data-dlt645-api</artifactId>
        </dependency>
        <!-- plumelog -->
        <dependency>
            <groupId>com.plumelog</groupId>
            <artifactId>plumelog-logback</artifactId>
        </dependency>
        <!-- RabbitMQ消息队列 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <!-- mqtt -->
        <dependency>
            <groupId>net.dreamlu</groupId>
            <artifactId>mica-mqtt-client-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>
    </dependencies>

    <!-- 环境 -->
    <profiles>
        <!-- 默认运行环境 -->
        <profile>
            <id>DEFAULT</id>
            <activation>
                <!--默认激活配置-->
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <!-- 设置构建的 jar 包名 -->
                <finalName>${project.artifactId}-${project.version}</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <!--设置为true，以便把本地的system的jar也包括进来-->
                            <includeSystemScope>true</includeSystemScope>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- 测试运行环境 -->
        <profile>
            <id>TEST</id>
            <build>
                <!-- 设置构建的 jar 包名 -->
                <finalName>${project.artifactId}-test-${project.version}</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <!--设置为true，以便把本地的system的jar也包括进来-->
                            <includeSystemScope>true</includeSystemScope>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
