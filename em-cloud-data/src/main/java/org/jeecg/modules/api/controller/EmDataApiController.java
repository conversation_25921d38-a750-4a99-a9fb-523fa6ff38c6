package org.jeecg.modules.api.controller;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.SysIpRestrictions;
import org.jeecg.common.aspect.annotation.SysLog;
import org.jeecg.common.constant.SysTips;
import org.jeecg.common.util.ThrowableUtils;
import org.jeecg.modules.data.api.IEmDataApi;
import org.jeecg.modules.data.dto.EmProcessSensorDTO;
import org.jeecg.modules.data.dto.EmSensorRerunDataDTO;
import org.jeecg.modules.data.dto.MeterControlReqDTO;
import org.jeecg.modules.data.service.EmDataSourcesService;
import org.jeecg.modules.data.service.EmProcessSensorService;
import org.jeecg.modules.data.service.GetDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 数据网关api Controller
 *
 * <AUTHOR>
 * @date 2022-08-22 11:00
 */
@Slf4j
@RestController
@RequestMapping("/data/api")
public class EmDataApiController implements IEmDataApi {
    @Lazy
    @Autowired
    private GetDataService getDataService;
    @Autowired
    private EmDataSourcesService emDataSourcesService;
    @Autowired
    private EmProcessSensorService emProcessSensorService;

    /**
     * 新增传感器
     *
     * @param emProcessSensorList
     * @return
     */
    @Override
    @SysLog("数据网关-新增传感器")
    @PostMapping("/addBatch")
    public Result<String> addBatch(@RequestBody List<EmProcessSensorDTO> emProcessSensorList) {
        emDataSourcesService.addSensor(emProcessSensorList);
        return Result.ok(SysTips.ADD_SUCCESS);
    }

    /**
     * 分合闸控制（管理员权限）
     *
     * @param meterControlReqDTO
     */
    @Override
    @SysIpRestrictions
    @SysLog("数据网关-分合闸控制")
    @PostMapping(value = "/meterControl")
    public Result<String> meterControl(@RequestBody MeterControlReqDTO meterControlReqDTO) {
        emDataSourcesService.meterControl(meterControlReqDTO);
        return Result.ok(SysTips.SUCCESS_MSG);
    }

    /**
     * 回溯origin数据,指标模板变更、修改安装时间
     *
     * @param emSensorRerunDataDTO
     * @return
     */
    @Override
    @SysLog("数据网关-回溯origin数据")
    @PostMapping(value = "/rerunOriginData")
    public Result<String> rerunOriginData(@RequestBody EmSensorRerunDataDTO emSensorRerunDataDTO) {
        log.info("回溯origin数据, emSensorRerunDataDTO- {}", emSensorRerunDataDTO);
        emDataSourcesService.rerunData(emSensorRerunDataDTO);
        return Result.ok("回溯数据中，请稍后！");
    }

    /**
     * 删除定时任务，重新加一次，用于传感器编号和上报频率变更
     *
     * @param emSensorRerunDataDTO
     * @return
     */
    @Override
    @SysLog("数据网关-回溯origin数据")
    @PostMapping(value = "/deleteJobAddAgain")
    public Result<String> deleteJobAddAgain(@RequestBody EmSensorRerunDataDTO emSensorRerunDataDTO) {
        log.info("删除定时任务，重新加一次，回溯origin数据, emSensorRerunDataDTO- {}", emSensorRerunDataDTO);
        emDataSourcesService.deleteJobAddAgain(emSensorRerunDataDTO);
        return Result.ok(SysTips.SUCCESS_MSG + "回溯数据中，请稍后！");
    }

    /**
     * 删除网关
     *
     * @param codeList
     * @return
     */
    @Override
    @SysLog("数据网关-删除网关")
    @DeleteMapping(value = "/deleteProcessSensor")
    public Result<String> deleteProcessSensor(@RequestParam("codeList") List<String> codeList) {
        emProcessSensorService.removeByDeviceAddrAndDeleteDataCache(codeList);
        return Result.ok(SysTips.SUCCESS_MSG);
    }

    /**
     * 初始化园区网关
     *
     * @param sysParkId
     * @return org.jeecg.common.api.vo.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023年09月14日 13:09:14
     */
    @Override
    @SysLog("数据网关-初始化园区网关")
    @GetMapping(value = "/initSysParkJob")
    public Result<String> initSysParkJob(@RequestParam(name = "sysParkId", required = false) String sysParkId,
                                         @RequestParam(name = "checkParkFlag", required = false) Boolean checkParkFlag) {
        getDataService.initSysParkJob(sysParkId, checkParkFlag);
        return Result.ok(SysTips.SUCCESS_MSG);
    }

    /**
     * 抄表
     *
     * @param deviceAddr
     * @return
     */
    @Override
    @SysLog("数据网关-抄表")
    @PostMapping(value = "/getRealData")
    public Result<String> getRealData(@NotBlank(message = "网关编号不能为空！") @RequestParam(name = "deviceAddr") String deviceAddr) {
        try {
            getDataService.getRealData(deviceAddr);
        } catch (Exception e) {
            log.error("安科瑞抄表失败 → {}", ThrowableUtils.getStackTrace(e));
            return Result.error("安科瑞抄表失败");
        }
        return Result.ok();
    }
}
