package org.jeecg.modules.data.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jeecg.boot.starter.rabbitmq.client.RabbitMqClient;
import org.jeecg.common.constant.DatabaseConstants;
import org.jeecg.common.constant.RabbitMqConstants;
import org.jeecg.common.constant.RedisKeyConstants;
import org.jeecg.common.constant.StrPool;
import org.jeecg.common.constant.enums.DataSourcesEnum;
import org.jeecg.common.util.RedisUtils;
import org.jeecg.modules.data.config.EmPrefixProperties;
import org.jeecg.modules.data.dto.EmSensorRerunDataDTO;
import org.jeecg.modules.data.entity.EmGatewayData;
import org.jeecg.modules.data.entity.EmSensor;
import org.jeecg.modules.data.entity.EmSensorOriginData;
import org.jeecg.modules.data.repository.EmSensorOriginDataRepository;
import org.jeecg.modules.data.service.EmDataSourcesService;
import org.jeecg.modules.data.service.EmSensorOriginDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.extra.spring.SpringUtil.getBean;

/**
 * 能源监测-传感器原始数据Service实现
 *
 * <AUTHOR>
 * @date 2022-04-24 14:37
 */
@Slf4j
@Service
public class EmSensorOriginDataServiceImpl implements EmSensorOriginDataService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private RabbitMqClient rabbitMqClient;
    @Autowired
    private EmPrefixProperties emPrefixProperties;
    @Autowired
    private EmDataSourcesService emDataSourcesService;
    @Autowired
    private EmSensorOriginDataRepository emSensorOriginDataRepository;

    @Override
    @Transactional(value = "mongoTransactionManager", rollbackFor = Exception.class)
    public List<EmSensorOriginData> insert(EmGatewayData gatewayData) {
        // 获取传感器数据详情
        List<EmSensorOriginData> emSensorOriginDataList = JSON.parseArray(gatewayData.getDataContent(), EmSensorOriginData.class);
        // 更新列表
        List<EmSensorOriginData> updateSensorOriginDataList = new ArrayList<>(16);
        // 新增列表
        List<EmSensorOriginData> addSensorOriginDataList = new ArrayList<>(16);
        for (EmSensorOriginData emSensorOriginData : emSensorOriginDataList) {
            // 网关数据ID
            String gatewayDataId = gatewayData.getId();
            // 网关地址
            String deviceAddr = gatewayData.getDeviceAddr();
            // pn
            Integer pn = emSensorOriginData.getPn();
            // 数据di
            String di = emSensorOriginData.getDi();
            // 数据时间
            String dataTime = emSensorOriginData.getDataTime();
            // 数据日期
            String dataDate = DateUtil.formatDate(DateUtil.parseDate(dataTime));
            EmSensorOriginData sensorOriginData = emSensorOriginDataRepository.findByDataTimeAndDeviceAddrAndDiAndPn(dataTime, deviceAddr, di, pn);
            if (Objects.nonNull(sensorOriginData)) {
                sensorOriginData.setGatewayDataId(gatewayDataId);
                sensorOriginData.setValue(emSensorOriginData.getValue());
                sensorOriginData.setName(emSensorOriginData.getName());
                sensorOriginData.setDataDate(dataDate);
                sensorOriginData.setCreateBy(DatabaseConstants.SYS_ADMIN);
                sensorOriginData.setCreateTime(DateUtil.parseDateTime(dataTime));
                sensorOriginData.setUpdateBy(DatabaseConstants.SYS_ADMIN);
                sensorOriginData.setUpdateTime(new Date());
                updateSensorOriginDataList.add(sensorOriginData);
            } else {
                // 再添加一份原始数据供操作
                emSensorOriginData.setDataDate(dataDate);
                emSensorOriginData.setGatewayDataId(gatewayDataId);
                emSensorOriginData.setDeviceAddr(deviceAddr);
                emSensorOriginData.setCreateBy(DatabaseConstants.SYS_ADMIN);
                emSensorOriginData.setCreateTime(new Date());
                addSensorOriginDataList.add(emSensorOriginData);
            }
        }
        if (CollectionUtils.isNotEmpty(addSensorOriginDataList)) {
            emSensorOriginDataRepository.insert(addSensorOriginDataList);
        }
        if (CollectionUtils.isNotEmpty(updateSensorOriginDataList)) {
            emSensorOriginDataRepository.saveAll(updateSensorOriginDataList);
        }
        addSensorOriginDataList.addAll(updateSensorOriginDataList);
        return addSensorOriginDataList;
    }

    @Override
    public void reSendData(String deviceAddr, Integer pn, String di, String sdt, String edt) {
        // 日期格式化
        Date startDt = DateUtil.parseDate(sdt);
        Date endDt = DateUtil.parseDate(edt);
        while (startDt.compareTo(endDt) <= 0) {
            if (StringUtils.isNotBlank(deviceAddr)) {
                if (Objects.isNull(pn)) {
                    this.sendMsg(startDt, deviceAddr, di);
                } else {
                    this.sendMsg(startDt, deviceAddr, pn, di);
                }
            } else {
                List<EmSensor> emSensorList = redisUtils.hashGetList(RedisKeyConstants.EmSensor.SENSOR_CODE_HASH, EmSensor.class);
                for (EmSensor emSensor : emSensorList) {
                    deviceAddr = StringUtils.substringBeforeLast(emSensor.getCode(), StrPool.UNDER_LINE);
                    if (Objects.isNull(pn)) {
                        this.sendMsg(startDt, deviceAddr, di);
                    } else {
                        this.sendMsg(startDt, deviceAddr, pn, di);
                    }
                }
                // 循环所有传感器之后，网关地址置空
                deviceAddr = null;
            }
            // 开始日期加一
            startDt = DateUtils.addDays(startDt, 1);
        }
    }

    @Override
    public void reSendDataBatch(List<String> deviceAddrList, Integer pn, String di, String sdt, String edt) {
        deviceAddrList.forEach(deviceAddr -> this.reSendData(deviceAddr, pn, di, sdt, edt));
    }

    @Async("asyncServiceExecutor")
    @Override
    public void originDataCommon(String deviceAddr, Integer pn, String di, Date sdt, Date edt, Integer dataSources, Integer dataFreq) {
        // 数据来源和频率为空给默认值
        if (Objects.isNull(dataSources)) {
            dataSources = DataSourcesEnum.ACREL.getCode();
        }
        if (Objects.isNull(dataFreq)) {
            dataFreq = 60;
        }
        DateTime endDateTime = DateUtil.endOfDay(edt);
        if (endDateTime.before(sdt)) {
            return;
        }
        // 第一个开始时间有时分秒,第一天过滤安装时间，后面都拿整天
        deviceAddr = checkIsNullRerunData(deviceAddr, pn, di, sdt, dataSources, dataFreq, Boolean.TRUE);
        // 开始时间加一天，从零点开始
        sdt = DateUtil.beginOfDay(DateUtils.addDays(sdt, 1));
        while (sdt.compareTo(endDateTime) <= 0) {
            deviceAddr = checkIsNullRerunData(deviceAddr, pn, di, sdt, dataSources, dataFreq, Boolean.FALSE);
            // 开始日期加一
            sdt = DateUtils.addDays(sdt, 1);
        }
    }

    /**
     * 判断网关编号是否为空去重跑数据
     *
     * @param deviceAddr        网关编号
     * @param pn                pn
     * @param di                数据项
     * @param sdt               开始时间
     * @param dataSources       数据类型
     * @param dataFreq          数据频率
     * @param filterInstallTime 是否需要过滤安装时间
     * @return 网关编号
     */
    private String checkIsNullRerunData(String deviceAddr, Integer pn, String di, Date sdt, Integer dataSources, Integer dataFreq, boolean filterInstallTime) {
        if (StringUtils.isNotBlank(deviceAddr)) {
            checkNullToSend(deviceAddr, pn, di, sdt, dataSources, dataFreq, filterInstallTime);
        } else {
            List<EmSensor> emSensorList = redisUtils.hashGetList(RedisKeyConstants.EmSensor.SENSOR_CODE_HASH, EmSensor.class);
            for (EmSensor emSensor : emSensorList) {
                deviceAddr = StringUtils.substringBeforeLast(emSensor.getCode(), StrPool.UNDER_LINE);
                checkNullToSend(deviceAddr, pn, di, sdt, dataSources, dataFreq, filterInstallTime);
            }
            // 循环所有传感器之后，网关地址置空
            deviceAddr = null;
        }
        return deviceAddr;
    }

    @Override
    public void rerunOriginData(EmSensorRerunDataDTO emSensorRerunDataDTO) {
        String deviceAddr = StringUtils.substringBeforeLast(emSensorRerunDataDTO.getSensorCode(), StrPool.UNDER_LINE);
        Integer pn = Integer.valueOf(StringUtils.substringAfterLast(emSensorRerunDataDTO.getSensorCode(), StrPool.UNDER_LINE));
        EmSensorOriginDataService bean = getBean(EmSensorOriginDataService.class);
        bean.originDataCommon(deviceAddr, pn, null, emSensorRerunDataDTO.getInstallTime(), emSensorRerunDataDTO.getEndDate(), emSensorRerunDataDTO.getDataSources(), emSensorRerunDataDTO.getDataFreq());
    }

    /**
     * 判断参数是否为空 查询不同的数据
     *
     * @param deviceAddr        网关编号
     * @param pn                pn
     * @param di                数据项
     * @param sdt               开始时间
     * @param dataSources       数据类型
     * @param filterInstallTime 是否需要过滤安装时间
     */
    private void checkNullToSend(String deviceAddr, Integer pn, String di, Date sdt, Integer dataSources, Integer dataFreq, boolean filterInstallTime) {
        List<EmSensorOriginData> sensorOriginDataList = getSensorOriginDataList(deviceAddr, pn, di, sdt, filterInstallTime);
        if (CollUtil.isNotEmpty(sensorOriginDataList)) {
            log.info("时间 → {},获取到传感器原始数据集合大小 → {}", DateUtil.formatDate(sdt), sensorOriginDataList.size());
            // 发送数据到MQ
            this.rerunOriginSendMsg(sensorOriginDataList);
        } else {
            if (!DataSourcesEnum.MOCK_DATA.getCode().equals(dataSources)) {
                // 走原始队列
                log.info("传感器原始数据为空，走原始通道获取数据，deviceAddr → {}，pn → {}, sdt → {} ", deviceAddr, pn, sdt);
                emDataSourcesService.originChannel(deviceAddr, sdt, dataSources, dataFreq);
            }
        }
    }

    /**
     * 获取原始数据
     *
     * @param deviceAddr        网关编号
     * @param pn                pn
     * @param di                数据项
     * @param sdt               开始时间
     * @param filterInstallTime 是否需要过滤安装时间
     * @return 原始数据
     */
    private List<EmSensorOriginData> getSensorOriginDataList(String deviceAddr, Integer pn, String di, Date sdt, boolean filterInstallTime) {
        List<EmSensorOriginData> sensorOriginDataList = null;
        // 根据各种情况查不同的数据
        if (Objects.isNull(pn) && StringUtils.isNotBlank(di)) {
            sensorOriginDataList = this.getList(sdt, deviceAddr, di);
        } else if (Objects.nonNull(pn) && StringUtils.isBlank(di)) {
            sensorOriginDataList = this.getList(sdt, deviceAddr, pn);
        } else if (Objects.nonNull(pn) && StringUtils.isNotBlank(di)) {
            sensorOriginDataList = this.getList(sdt, deviceAddr, pn, di);
        }
        // 不为空并且需要过滤安装时间
        if (CollUtil.isNotEmpty(sensorOriginDataList) && filterInstallTime) {
            // 过滤大于等于开始时间
            log.info("过滤大于等于开始时间, 时间：{}", DateUtil.formatDateTime(sdt));
            return sensorOriginDataList.stream().filter(origin -> DateUtil.parseDateTime(origin.getDataTime()).compareTo(sdt) >= 0).collect(Collectors.toList());
        }
        return sensorOriginDataList;
    }

    /**
     * 发送消息
     *
     * @param startDt
     * @param deviceAddr
     * @param di
     */
    private void sendMsg(Date startDt, String deviceAddr, String di) {
        // 获取传感器原始数据
        List<EmSensorOriginData> sensorOriginDataList = this.getList(startDt, deviceAddr, di);
        log.info("传感器原始数据 → " + JSON.toJSONString(sensorOriginDataList));
        if (CollectionUtils.isNotEmpty(sensorOriginDataList)) {
            // 发送数据到MQ
            this.sendMsg(sensorOriginDataList);
        }
    }

    /**
     * 发送消息
     *
     * @param startDt
     * @param deviceAddr
     * @param pn
     * @param di
     */
    private void sendMsg(Date startDt, String deviceAddr, Integer pn, String di) {
        // 获取传感器原始数据
        List<EmSensorOriginData> sensorOriginDataList = this.getList(startDt, deviceAddr, pn, di);
        log.info("传感器原始数据 → " + JSON.toJSONString(sensorOriginDataList));
        if (CollectionUtils.isNotEmpty(sensorOriginDataList)) {
            // 发送数据到MQ
            this.sendMsg(sensorOriginDataList);
        }
    }

    /**
     * 发送MQ消息-原始队列
     *
     * @param emSensorOriginDataList
     */
    private void sendMsg(List<EmSensorOriginData> emSensorOriginDataList) {
        emSensorOriginDataList.forEach(emSensorOriginData -> {
            // 发送数据到MQ
            rabbitMqClient.sendMessage(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.DIRECT_EXCHANGE, RabbitMqConstants.SENSOR_DATA_QUEUE, JSON.toJSONString(Collections.singletonList(emSensorOriginData)));
        });
    }

    /**
     * 发送MQ消息-重发origin不同队列消息
     *
     * @param emSensorOriginDataList
     */
    private void rerunOriginSendMsg(List<EmSensorOriginData> emSensorOriginDataList) {
        emSensorOriginDataList.forEach(emSensorOriginData -> {
            // 发送数据到MQ
            rabbitMqClient.sendMessage(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.RERUN_EXCHANGE, RabbitMqConstants.RERUN_DATA_QUEUE, JSON.toJSONString(Collections.singletonList(emSensorOriginData)));
        });
    }

    @Override
    public List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, String di) {
        return emSensorOriginDataRepository.findByDataDateAndDeviceAddrAndDi(DateUtil.formatDate(dataDate), deviceAddr, di, Sort.by(Sort.Direction.ASC, "data_time"));
    }

    @Override
    public List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, Integer pn, String di) {
        return emSensorOriginDataRepository.findByDataDateAndDeviceAddrAndPnAndDi(DateUtil.formatDate(dataDate), deviceAddr, pn, di, Sort.by(Sort.Direction.ASC, "data_time"));
    }

    @Override
    public List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, Integer pn) {
        return emSensorOriginDataRepository.findByDataDateAndDeviceAddrAndPn(DateUtil.formatDate(dataDate), deviceAddr, pn, Sort.by(Sort.Direction.ASC, "data_time"));
    }

    @Override
    public EmSensorOriginData getNowOrNextOneBy(String deviceAddr, Date dataDate) {
        return emSensorOriginDataRepository.findNowOrNextOneBy(deviceAddr, DateUtil.formatDateTime(dataDate));
    }

    @Override
    public EmSensorOriginData getFirstOne(String deviceAddr, String di) {
        return emSensorOriginDataRepository.findFirstOne(deviceAddr, di);
    }
}
