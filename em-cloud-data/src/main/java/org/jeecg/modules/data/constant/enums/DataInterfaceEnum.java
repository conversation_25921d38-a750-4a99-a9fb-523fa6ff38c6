package org.jeecg.modules.data.constant.enums;

import lombok.Getter;
import org.jeecg.modules.data.constant.AcrelDataConstants;

/**
 * 数据接口
 *
 * <AUTHOR>
 * @date 2022/01/17
 */
@Getter
public enum DataInterfaceEnum {
    /**
     * 用户登录
     */
    USER_LOGIN(1, "acrel0001", AcrelDataConstants.RequestType.POST, "UserLogin"),
    /**
     * 获取用户名下所有电表信息
     */
    METER_LIST(2, "acrel0002", AcrelDataConstants.RequestType.GET, "MeterList"),
    /**
     * 获取电表信息
     */
    GET_METER_INFO(3, "acrel0003", AcrelDataConstants.RequestType.GET, "GetMeterInfo"),
    /**
     * 电费充值（管理员权限）
     */
    SALE_POWER(4, "acrel0004", AcrelDataConstants.RequestType.POST, "SalePower"),
    /**
     * 分合闸控制（管理员权限）
     */
    METER_CONTROL(5, "acrel0005", AcrelDataConstants.RequestType.POST, "MeterControl"),
    /**
     * 开户（管理员权限）
     */
    OPEN_USER(6, "acrel0006", AcrelDataConstants.RequestType.POST, "OpenUser"),
    /**
     * 修改用户（管理员权限）
     */
    EDITOR_USER(7, "acrel0007", AcrelDataConstants.RequestType.POST, "EditorUser"),
    /**
     * 销户（管理员权限）
     */
    DEL_USER(8, "acrel0008", AcrelDataConstants.RequestType.POST, "delUser"),
    /**
     * 用电统计
     */
    USE_POWER(9, "acrel0009", AcrelDataConstants.RequestType.POST, "UsePower"),
    /**
     * 根据电表编号查管控表信息的接口
     */
    GET_METER_WATCH_INFO(10, "acrel0010", AcrelDataConstants.RequestType.GET, "GetMeterWatchInfo"),
    /**
     * 根据电表编号获取抄表信息
     */
    GET_COLLECT(11, "acrel0011", AcrelDataConstants.RequestType.GET, "GetCollect"),
    /**
     * 获取管理员账号下所有电表（管理员权限）
     */
    GET_ALL_METERS(12, "acrel0012", AcrelDataConstants.RequestType.GET, "GetAllMeters"),
    /**
     * 获得一段时间内的电表电能数据（管理员权限）
     */
    GET_ENERGY_DATA(13, "acrel0013", AcrelDataConstants.RequestType.GET, "GetEnergyData"),
    /**
     * 获得一段时间内的电表电能数据2（管理员权限）
     */
    GET_ENERGY_DATA2(14, "acrel0014", AcrelDataConstants.RequestType.GET, "GetEnergyData2"),
    /**
     * 获取管控表数据
     */
    GET_CONTROL_METER(15, "acrel0015", AcrelDataConstants.RequestType.GET, "GetControlMeter"),
    /**
     * 获取所有用户及电表(管理员权限)
     */
    GET_OWNER_DETAILS(16, "acrel0016", AcrelDataConstants.RequestType.GET, "GetOwnerDetails"),
    /**
     * 获取某天用电数据(管理员权限)
     */
    GET_ENERGY_DAY(17, "acrel0017", AcrelDataConstants.RequestType.GET, "GetEnergyDay"),
    /**
     * 获取所有用户信息
     */
    GET_ALL_OWNERS(18, "acrel0018", AcrelDataConstants.RequestType.GET, "GetAllowners"),
    /**
     * 水表分合闸控制（管理员权限）
     */
    WATER_METER_CONTROL(19, "acrel0019", AcrelDataConstants.RequestType.POST, "WaterMeterControl"),
    /**
     * 获取用户名下某个水表信息
     */
    GET_METER_WATER_INFO(20, "acrel0020", AcrelDataConstants.RequestType.GET, "GetMeterWaterInfo");

    private final Integer code;

    private final String di;

    private final Integer type;

    private final String function;

    DataInterfaceEnum(Integer code, String di, Integer type, String function) {
        this.code = code;
        this.di = di;
        this.type = type;
        this.function = function;
    }

    public static DataInterfaceEnum getEnumByCode(Integer code) {
        for (DataInterfaceEnum dataInterfaceEnum : DataInterfaceEnum.values()) {
            if (dataInterfaceEnum.code.equals(code)) {
                return dataInterfaceEnum;
            }
        }
        return null;
    }
}
