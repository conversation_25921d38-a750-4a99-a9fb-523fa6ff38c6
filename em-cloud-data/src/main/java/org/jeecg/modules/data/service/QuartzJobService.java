package org.jeecg.modules.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.data.entity.SysQuartzJob;
import org.quartz.SchedulerException;

import java.util.List;

/**
 * 定时任务在线管理
 *
 * <AUTHOR>
 * @version V1.1
 * @date 2019-04-28
 */
public interface QuartzJobService extends IService<SysQuartzJob> {
    /***
     * 查询定时任务是否存在
     *
     * @param jobClassName
     * @return List
     */
    List<SysQuartzJob> findByJobClassName(String jobClassName);

    /***
     * 新增定时任务
     *
     * @param sysQuartzJob
     * @return
     */
    boolean saveAndScheduleJob(SysQuartzJob sysQuartzJob);

    /***
     * 更新定时任务
     *
     * @param sysQuartzJob
     * @throws SchedulerException
     * @return
     */
    boolean editAndScheduleJob(SysQuartzJob sysQuartzJob) throws SchedulerException;

    /***
     * 删除停止定时任务
     *
     * @param sysQuartzJob
     * @return
     */
    boolean deleteAndStopJob(SysQuartzJob sysQuartzJob);

    /***
     * 恢复定时任务
     *
     * @param sysQuartzJob
     * @return
     */
    boolean resumeJob(SysQuartzJob sysQuartzJob);

    /**
     * 执行定时任务
     *
     * @param sysQuartzJob
     * @throws Exception
     */
    void execute(SysQuartzJob sysQuartzJob) throws Exception;

    /**
     * 暂停任务
     *
     * @param sysQuartzJob
     */
    void pause(SysQuartzJob sysQuartzJob);

    /**
     * 删除定时任务
     *
     * @param id 任务id
     */
    void schedulerDelete(String id);
}
