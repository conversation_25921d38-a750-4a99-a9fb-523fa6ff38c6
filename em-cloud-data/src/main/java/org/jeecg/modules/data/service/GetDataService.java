package org.jeecg.modules.data.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.enums.DataSourcesEnum;
import org.jeecg.modules.data.dto.EmSensorRerunDataDTO;
import org.jeecg.modules.data.entity.EmProcessSensor;
import org.jeecg.modules.data.vo.MockDataDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据处理service
 *
 * <AUTHOR>
 * @date 2022-01-12 10:48
 */
public interface GetDataService {

    /**
     * 用户登录
     *
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> userLogin(Boolean isReTry) throws Exception;

    /**
     * 获取用户名下所有电表信息
     * 初始化库表 塞到redis
     *
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> meterList(Boolean isReTry) throws Exception;

    /**
     * 获取电表信息
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getMeterInfo(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 电费充值（管理员权限）
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> salePower(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 分合闸控制（管理员权限）
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> meterControl(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 开户（管理员权限）
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> openUser(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 修改用户（管理员权限）
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> editorUser(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 销户（管理员权限）
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> delUser(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 用电统计
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> usePower(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 根据电表编号查管控表信息的接口
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getMeterWatchInfo(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 根据电表编号获取抄表信息
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getCollect(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 获取管理员账号下所有电表（管理员权限）
     *
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getAllMeters(Boolean isReTry) throws Exception;

    /**
     * 获得一段时间内的电表电能数据（管理员权限）   弃用
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getEnergyData(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 获得一段时间内的电表电能数据（管理员权限）
     * 新版接口，读取mongodb数据
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getEnergyData2(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 获取管控表数据
     *
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getControlMeter(Boolean isReTry) throws Exception;

    /**
     * 获取所有用户及电表(管理员权限)
     *
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getOwnerDetails(Boolean isReTry) throws Exception;

    /**
     * 获取某天用电数据(管理员权限)     弃用
     *
     * @param map
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getEnergyDay(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 获取所有用户信息
     *
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getAllOwners(Boolean isReTry) throws Exception;

    /**
     * 水表分合闸控制（管理员权限）
     *
     * @param map     参数
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> waterMeterControl(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 获取水表信息
     *
     * @param map     参数
     * @param isReTry 是否重试
     * @return
     * @throws Exception
     */
    Result<String> getWaterMeterInfo(Map<String, String> map, Boolean isReTry) throws Exception;

    /**
     * 根据日志id重试
     *
     * @param id
     * @return
     * @throws Exception
     */
    Result<String> reTryByLogId(String id) throws Exception;

    /**
     * 获得一段时间内的电表电能数据   模拟数据
     *
     * @param meterId
     * @param dataTime    数据时间
     * @param mockDataDTO 变位数据
     * @return
     * @throws Exception
     */
    Result<String> getEnergyData2Demo(String meterId, Date dataTime, MockDataDTO mockDataDTO);

    /**
     * 新增定时器
     *
     * @param deviceAddr
     * @param reportFreq
     */
    void initAndAddJob(String deviceAddr, Integer reportFreq);

    /**
     * 获取当前数据时间获取历史数据和实时数据
     *
     * @param emProcessSensor 网关对象
     * @param isRerunData     是否回溯数据
     */
    void getWhenBefore(EmProcessSensor emProcessSensor, boolean isRerunData);

    /**
     * 获取当前数据时间获取历史数据和实时数据
     *
     * @param emProcessSensor 网关对象
     */
    void getWaterDataWhenBefore(EmProcessSensor emProcessSensor);

    /**
     * 跑历史数据接口
     *
     * @param meterId            网关编码
     * @param recallStartTime    回溯开始时间
     * @param recallEndTime      回溯结束时间
     * @param dataSourcesEnum    数据来源
     * @param dataFreq           数据频率
     * @param isUpdateReCallDate 是否修改数据时间
     * @param type               设备类型：1.电，2.水
     */
    Date runHistoryData(String meterId, Date recallStartTime, Date recallEndTime, DataSourcesEnum dataSourcesEnum, Integer dataFreq, boolean isUpdateReCallDate, Integer type);

    /**
     * 新增单个网关  安科瑞
     *
     * @param emProcessSensor
     * @return
     */
    Result<String> addOneSensor(EmProcessSensor emProcessSensor);

    /**
     * 删除所有任务
     *
     * @param parameters   网关id集合，不传删所有
     * @param jobClassName 类，不传删默认的
     * @return
     */
    Result<String> deleteAllJob(List<String> parameters, String jobClassName);

    /**
     * 查询没有定时器的传感器，加定时器
     */
    void dealOldSensorJob();

    /**
     * 开关阀后，重新获取一次数据
     *
     * @param deviceAddr
     */
    void getRealDataAfterMeterControl(String deviceAddr);

    /**
     * 获取实时数据
     *
     * @param deviceAddr 网关编号
     */
    void getRealData(String deviceAddr);

    /**
     * 触发一次定时器
     *
     * @param meterId
     */
    void triggerJob(String meterId);

    /**
     * 模拟数据修改安装时间回溯数据
     *
     * @param emSensorRerunDataDTO
     */
    void rerunMockData(EmSensorRerunDataDTO emSensorRerunDataDTO);

    /**
     * 初始化园区定时统计任务
     *
     * @param sysParkId 可空，为空就初始化所有园区
     * @return void
     * <AUTHOR>
     * @date 2023年09月13日 14:09:28
     */
    void initSysParkJob(String sysParkId, Boolean checkPark);

    /**
     * 处理数据
     *
     * @param sysParkId
     * @param meterId
     * @param freq
     * @return void
     * <AUTHOR>
     * @date 2023年09月13日 14:09:56
     */
    void processData(String sysParkId, String meterId, Integer freq);

    /**
     * 删除定时任务，补充园区id，添加园区定时任务
     *
     * @return void
     * <AUTHOR>
     * @date 2023年09月14日 16:09:15
     */
    void delSensorJobAndAddParkJob();

    /**
     * 推送电表/水表实时数据
     *
     * @param jsonObject 数据项内容
     * @param di         数据类型
     */
    void sendCollectMessage(JSONObject jsonObject, String di);
}
