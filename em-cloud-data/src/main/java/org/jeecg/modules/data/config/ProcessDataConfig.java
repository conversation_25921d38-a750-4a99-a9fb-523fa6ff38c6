package org.jeecg.modules.data.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 处理数据配置
 *
 * <AUTHOR>
 * @date 2022-01-13 18:27
 */
@Data
@Component
@ConfigurationProperties(prefix = "process-data")
public class ProcessDataConfig {
    /**
     * url
     */
    private String url;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 传感器新增时数据追溯开始时间
     */
    private String dataDate;
}
