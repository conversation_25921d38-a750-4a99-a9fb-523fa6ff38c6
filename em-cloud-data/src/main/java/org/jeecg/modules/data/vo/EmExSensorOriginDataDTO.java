package org.jeecg.modules.data.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 前置机传感器数据DTO
 *
 * <AUTHOR>
 * @date 2022-04-24 10:52
 */
@Data
public class EmExSensorOriginDataDTO implements Serializable {
    private static final long serialVersionUID = -7861939644592120974L;
    /**
     * di
     */
    private String di;
    /**
     * 名称
     */
    private String name;
    /**
     * 数据值
     */
    private String value;
    /**
     * pn
     */
    private Integer pn;
    /**
     * 数据时间
     */
    @JSONField(name = "data_time", format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;
}
