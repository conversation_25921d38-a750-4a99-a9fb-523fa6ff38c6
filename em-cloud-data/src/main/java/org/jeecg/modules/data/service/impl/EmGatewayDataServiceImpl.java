package org.jeecg.modules.data.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.jeecg.boot.starter.rabbitmq.client.RabbitMqClient;
import org.jeecg.common.constant.DatabaseConstants;
import org.jeecg.common.constant.RabbitMqConstants;
import org.jeecg.modules.data.config.EmPrefixProperties;
import org.jeecg.modules.data.entity.EmGatewayData;
import org.jeecg.modules.data.entity.EmSensorOriginData;
import org.jeecg.modules.data.repository.EmGatewayDataRepository;
import org.jeecg.modules.data.service.EmGatewayDataService;
import org.jeecg.modules.data.service.EmSensorOriginDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 能源监测-网关数据Service实现
 *
 * <AUTHOR>
 * @date 2022-04-24 14:33
 */
@Slf4j
@Service
public class EmGatewayDataServiceImpl implements EmGatewayDataService {
    @Autowired
    private RabbitMqClient rabbitMqClient;
    @Autowired
    private EmPrefixProperties emPrefixProperties;
    @Autowired
    private EmGatewayDataRepository emGatewayDataRepository;
    @Autowired
    private EmSensorOriginDataService emSensorOriginDataService;

    @Override
    @Transactional(value = "mongoTransactionManager", rollbackFor = Exception.class)
    public List<EmSensorOriginData> insert(EmGatewayData gatewayData) {
        gatewayData.setDataDate(DateUtil.formatDate(DateUtil.parseDate(gatewayData.getSendTime())));
        gatewayData.setCreateBy(DatabaseConstants.SYS_ADMIN);
        gatewayData.setCreateTime(new Date());
        // 网关数据直接入库，入库后不再做任何操作
        emGatewayDataRepository.insert(gatewayData);
        // 新增传感器原始数据
        return emSensorOriginDataService.insert(gatewayData);
    }

    @Async
    @Override
    public void reSendData(String deviceAddr, String sdt, String edt) {
        // 日期格式化
        Date startDt = DateUtil.parseDate(sdt);
        Date endDt = DateUtil.parseDate(edt);
        while (startDt.compareTo(endDt) <= 0) {
            log.info("网关数据日期，dataDate → " + DateUtil.formatDate(startDt));
            // 发送数据
            this.sendMsg(deviceAddr, startDt);
            // 开始日期加一
            startDt = DateUtils.addDays(startDt, 1);
        }
    }

    @Async
    @Override
    public void reSendData(List<String> deviceAddrList, String sdt, String edt) {
        // 日期格式化
        Date startDt = DateUtil.parseDate(sdt);
        Date endDt = DateUtil.parseDate(edt);
        while (startDt.compareTo(endDt) <= 0) {
            log.info("网关数据日期，dataDate → " + DateUtil.formatDate(startDt));
            for (String deviceAddr : deviceAddrList) {
                // 发送数据
                this.sendMsg(deviceAddr, startDt);
            }
            // 开始日期加一
            startDt = DateUtils.addDays(startDt, 1);
        }
    }

    /**
     * 发送数据
     *
     * @param deviceAddr
     * @param dataDate
     */
    private void sendMsg(String deviceAddr, Date dataDate) {
        List<EmGatewayData> emGatewayDataList = emGatewayDataRepository.findByDeviceAddrAndDataDate(deviceAddr, DateUtil.formatDate(dataDate));
        emGatewayDataList.forEach(emGatewayData -> {
            // 传感器原始数据列表
            List<EmSensorOriginData> emSensorOriginDataList = emSensorOriginDataService.insert(emGatewayData);
            log.info("传感器原始数据 → " + JSON.toJSONString(emSensorOriginDataList));
            rabbitMqClient.sendMessage(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.DIRECT_EXCHANGE, RabbitMqConstants.SENSOR_DATA_QUEUE, JSON.toJSONString(emSensorOriginDataList));
        });
    }
}
