<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.data.mapper.EmProcessUserMapper">
    <resultMap id="BaseResultMap" type="org.jeecg.modules.data.entity.EmProcessUser">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="tel" jdbcType="VARCHAR" property="tel"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="is_admin" jdbcType="BOOLEAN" property="admin"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">id, name, password, tel, address, is_admin, remark, is_deleted, create_by, create_time, update_by, update_time</sql>
</mapper>