package org.jeecg.modules.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.data.entity.EmProcessTask;
import org.jeecg.modules.data.vo.TaskDTO;

import java.util.List;

/**
 * 能源监测-数据处理-任务Service接口
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
public interface EmProcessTaskService extends IService<EmProcessTask> {
    /**
     * 查询所有
     *
     * @return
     */
    List<TaskDTO> selectAllUpdateRedis();

}
