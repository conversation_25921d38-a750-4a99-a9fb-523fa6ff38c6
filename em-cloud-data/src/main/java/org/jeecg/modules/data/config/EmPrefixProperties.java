package org.jeecg.modules.data.config;

import lombok.Setter;
import org.jeecg.common.constant.StrPool;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 处理数据配置
 *
 * <AUTHOR>
 * @date 2022-01-13 18:27
 */
@Setter
@Component
@ConfigurationProperties(prefix = "energy-monitor.profiles-active")
public class EmPrefixProperties {
    /**
     * 前缀
     */
    private String rabbitMqPrefix;

    public String getRabbitMqPrefix() {
        return rabbitMqPrefix + StrPool.DOT;
    }
}
