package org.jeecg.modules.data.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 阀控之后获取设备信息DTO
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmMeterInfoAfterControlDTO implements Serializable {
    private static final long serialVersionUID = 8404429107628463392L;
    /**
     * 设备类型
     */
    private Integer type;

    /**
     * 设备地址
     */
    private String deviceAddr;

    /**
     * 触发次数
     */
    private Integer triggerCount;

    /**
     * 控制类型
     */
    private Integer meterControlType;

    /**
     * 映射表id
     */
    private String mapSensorId;

    /**
     * 需要通知的用户id
     */
    private String userId;
}