package org.jeecg.modules.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.data.entity.SysQuartzJob;

import java.util.List;

/**
 * 定时任务在线管理
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2019-01-02
 */
public interface QuartzJobMapper extends BaseMapper<SysQuartzJob> {
    /**
     * 查询定时任务
     *
     * @param jobClassName
     * @return
     */
    List<SysQuartzJob> findByJobClassName(@Param("jobClassName") String jobClassName);
}
