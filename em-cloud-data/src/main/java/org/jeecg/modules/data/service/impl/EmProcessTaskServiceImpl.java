package org.jeecg.modules.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.data.entity.EmProcessTask;
import org.jeecg.modules.data.mapper.EmProcessTaskMapper;
import org.jeecg.modules.data.service.EmProcessTaskService;
import org.jeecg.modules.data.vo.TaskDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 能源监测-数据处理-任务Service实现
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Service
public class EmProcessTaskServiceImpl extends ServiceImpl<EmProcessTaskMapper, EmProcessTask> implements EmProcessTaskService {
    @Autowired
    private EmProcessTaskMapper emProcessTaskMapper;

    @Override
    public List<TaskDTO> selectAllUpdateRedis() {
        return emProcessTaskMapper.selectAllUpdateRedis();
    }
}
