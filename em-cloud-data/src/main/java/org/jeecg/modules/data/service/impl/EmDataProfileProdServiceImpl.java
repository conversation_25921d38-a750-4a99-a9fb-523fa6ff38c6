package org.jeecg.modules.data.service.impl;

import org.jeecg.modules.data.service.EmDataProfileService;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

/**
 * 能源监测-正式环境
 *
 * <AUTHOR>
 * @date 2022-10-12
 */
@Profile({"prod"})
@Service
public class EmDataProfileProdServiceImpl implements EmDataProfileService {

    @Override
    public void checkMeterControl(String id) {

    }
}
