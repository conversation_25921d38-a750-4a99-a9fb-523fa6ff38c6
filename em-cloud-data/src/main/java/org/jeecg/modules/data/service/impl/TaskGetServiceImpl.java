package org.jeecg.modules.data.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.data.service.TaskGetService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 前置机获取历史数据下发
 *
 * <AUTHOR>
 * @date 2022-05-24
 */
@Slf4j
@Service
public class TaskGetServiceImpl implements TaskGetService {
    /**
     * 任务前缀
     */
    private static final String TASK_PREFIX = "18";
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public void copyData(String sdt, String edt, String sensorId) {
        if (StringUtils.isBlank(sensorId)) {
            throw new JeecgBootException("终端id不能为空！");
        }
        Map<String, Object> map = new HashMap<>(5);
        map.put("send_time", DateUtil.formatDateTime(new Date()));
        map.put("jcd_id", "000000000000".concat(sensorId));
        if (StringUtils.startsWith(sensorId, TASK_PREFIX)) {
            map.put("task_id", 2);
        } else {
            map.put("task_id", 1);
        }
        map.put("start_time", sdt);
        map.put("end_time", edt);
        String msg = JSON.toJSONString(map);
        log.info("补抄数据下发 msg {}", msg);
        rabbitTemplate.convertAndSend("task_get", msg);
    }
}
