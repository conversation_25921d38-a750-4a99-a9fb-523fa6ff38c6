package org.jeecg.modules.data.service;

import org.jeecg.modules.data.entity.EmProcessDataLog;


/**
 * 能源监测-安科瑞数据处理-接口日志
 *
 * <AUTHOR>
 * @date 2022-05-30
 */
public interface EmProcessDataLogService {
    /**
     * 新增
     *
     * @param emProcessDataLog
     * @return
     */
    EmProcessDataLog insert(EmProcessDataLog emProcessDataLog);

    /**
     * 根据id获取
     *
     * @param id
     * @return
     */
    EmProcessDataLog getById(String id);

    /**
     * 修改
     *
     * @param emProcessDataLog
     * @return
     */
    void update(EmProcessDataLog emProcessDataLog);
}
