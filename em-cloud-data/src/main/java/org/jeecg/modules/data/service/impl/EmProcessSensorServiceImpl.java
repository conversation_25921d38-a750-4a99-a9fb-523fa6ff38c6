package org.jeecg.modules.data.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.DatabaseConstants;
import org.jeecg.common.constant.RedisKeyConstants;
import org.jeecg.common.constant.StrPool;
import org.jeecg.common.constant.SysConstants;
import org.jeecg.common.constant.enums.DataSourcesEnum;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtils;
import org.jeecg.common.util.StatTimeUtils;
import org.jeecg.modules.data.config.ProcessDataConfig;
import org.jeecg.modules.data.constant.AcrelDataConstants;
import org.jeecg.modules.data.entity.EmProcessSensor;
import org.jeecg.modules.data.mapper.EmProcessSensorMapper;
import org.jeecg.modules.data.service.EmProcessSensorService;
import org.jeecg.modules.data.service.GetDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 能源监测-数据处理-设备Service实现
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Slf4j
@Service
public class EmProcessSensorServiceImpl extends ServiceImpl<EmProcessSensorMapper, EmProcessSensor> implements EmProcessSensorService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private ProcessDataConfig dataConfig;
    @Lazy
    @Autowired
    private GetDataService getDataService;
    @Autowired
    private EmProcessSensorMapper emProcessSensorMapper;

    @Override
    public List<EmProcessSensor> selectAll() {
        return emProcessSensorMapper.selectList(new LambdaQueryWrapper<EmProcessSensor>().eq(EmProcessSensor::getDeleted, SysConstants.IsStatus.NO));
    }

    @Override
    public List<EmProcessSensor> selectListByUserId(String userId) {
        return emProcessSensorMapper.selectList(new LambdaQueryWrapper<EmProcessSensor>()
                .eq(EmProcessSensor::getDeleted, SysConstants.IsStatus.NO).eq(EmProcessSensor::getUserId, userId));
    }

    @Override
    public EmProcessSensor getOneByDeviceAddr(String deviceAddr) {
        return emProcessSensorMapper.selectOne(new LambdaQueryWrapper<EmProcessSensor>()
                .eq(EmProcessSensor::getDeleted, SysConstants.IsStatus.NO).eq(EmProcessSensor::getDeviceAddr, deviceAddr));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSensor(String meterId, Date recallStartTime, Date recallEndTime, Date latestComTime, Date latestTaskTime) {
        LambdaUpdateWrapper<EmProcessSensor> updateWrapper = new LambdaUpdateWrapper<EmProcessSensor>().eq(EmProcessSensor::getDeviceAddr, meterId);
        if (Objects.nonNull(recallStartTime)) {
            updateWrapper.set(EmProcessSensor::getRecallStartTime, recallStartTime);
        }
        if (Objects.nonNull(recallEndTime)) {
            updateWrapper.set(EmProcessSensor::getRecallEndTime, recallEndTime);
        }
        if (Objects.nonNull(latestComTime)) {
            updateWrapper.set(EmProcessSensor::getLatestComTime, latestComTime);
        }
        if (Objects.nonNull(latestTaskTime)) {
            updateWrapper.set(EmProcessSensor::getLatestTaskTime, latestTaskTime);
        }
        this.update(updateWrapper);
    }

    @Override
    public List<EmProcessSensor> getNotHaveJob() {
        return emProcessSensorMapper.selectListNotHaveJob();
    }

    @Override
    public void addProcessSensor(EmProcessSensor emProcessSensor) {
        checkProcessSensor(emProcessSensor);
        // 网关
        emProcessSensor.setUserId(DatabaseConstants.SYS_ADMIN);
        emProcessSensor.setJcdId(emProcessSensor.getDeviceAddr());
        // 传感器新增时数据追溯开始时间
        if (Objects.isNull(emProcessSensor.getLatestComTime())) {
            emProcessSensor.setRecallStartTime(DateUtil.parseDateTime(dataConfig.getDataDate()));
        } else {
            emProcessSensor.setRecallStartTime(emProcessSensor.getLatestComTime());
        }
        // 获取上次数据时间
        Date dataTime = StatTimeUtils.getPrevDataTime(new Date(), emProcessSensor.getDataFreq());
        emProcessSensor.setLatestComTime(dataTime);
        // 回溯数据结束时间为最近的数据时间
        emProcessSensor.setRecallEndTime(dataTime);
        // 最近任务时间默认当前时间
        emProcessSensor.setLatestTaskTime(StatTimeUtils.getDataTime(new Date(), emProcessSensor.getDataFreq()));
        // 安科瑞的水  特殊处理，不需要回溯时间字段和最新任务时间字段
        if (DataSourcesEnum.ACREL.getCode().equals(emProcessSensor.getDataSources()) && AcrelDataConstants.DeviceType.WATER.equals(emProcessSensor.getType())) {
            emProcessSensor.setLatestComTime(emProcessSensor.getRecallStartTime());
        }
        this.save(emProcessSensor);
    }

    /**
     * 校验网关信息
     *
     * @param emProcessSensor
     */
    private void checkProcessSensor(EmProcessSensor emProcessSensor) {
        if (ObjectUtil.isEmpty(emProcessSensor)) {
            throw new JeecgBootException("新增数据不能为空！");
        }
        EmProcessSensor existEmProcessSensor = this.getOneByDeviceAddr(emProcessSensor.getDeviceAddr());
        if (Objects.nonNull(existEmProcessSensor)) {
            throw new JeecgBootException("该网关已存在！");
        }
    }

    @Override
    public void removeByDeviceAddr(String deviceAddr) {
        // 删除定时器
        getDataService.deleteAllJob(Collections.singletonList(deviceAddr), null);
        this.remove(new LambdaQueryWrapper<EmProcessSensor>().eq(EmProcessSensor::getDeviceAddr, deviceAddr));
    }

    @Override
    public void removeByDeviceAddrAndDeleteDataCache(List<String> deviceAddrList) {
        if (CollUtil.isNotEmpty(deviceAddrList)) {
            deviceAddrList.forEach(deviceAddr -> {
                String code = StringUtils.substringBeforeLast(deviceAddr, StrPool.UNDER_LINE);
                // 清除模拟数据缓存
                redisUtils.delete(CharSequenceUtil.format(RedisKeyConstants.EmSensor.SENSOR_DEMO_DATA, code));
                removeByDeviceAddr(code);
            });
        }
    }

    @Override
    public void addRunningCache(String deviceAddr) {
        redisUtils.strSet(CharSequenceUtil.format(RedisKeyConstants.EmSensor.SENSOR_RUNNING, deviceAddr), deviceAddr);
    }

    @Override
    public String getRunningCache(String deviceAddr) {
        return redisUtils.strGet(CharSequenceUtil.format(RedisKeyConstants.EmSensor.SENSOR_RUNNING, deviceAddr));
    }

    @Override
    public void removeRunningCache(String deviceAddr) {
        redisUtils.delete(CharSequenceUtil.format(RedisKeyConstants.EmSensor.SENSOR_RUNNING, deviceAddr));
    }

    @Override
    public List<EmProcessSensor> listByLatestComTime() {
        return emProcessSensorMapper.selectlistByLatestComTime();
    }

    @Override
    public void initReCallDate() {
        List<EmProcessSensor> emProcessSensorList = this.selectAll();
        if (CollUtil.isNotEmpty(emProcessSensorList)) {
            emProcessSensorList.forEach(emProcessSensor -> {
                emProcessSensor.setRecallEndTime(emProcessSensor.getLatestComTime());
                emProcessSensor.setRecallStartTime(DateUtil.offsetHour(emProcessSensor.getLatestComTime(), -3));
            });
            updateBatchById(emProcessSensorList);
        }
    }

    @Override
    public List<EmProcessSensor> getBySysParkId(String sysParkId, Integer freq) {
        LambdaQueryWrapper<EmProcessSensor> lqw = new LambdaQueryWrapper<EmProcessSensor>();
        lqw.eq(EmProcessSensor::getDeleted, SysConstants.IsStatus.NO).eq(EmProcessSensor::getSysParkId, sysParkId);
        if (Objects.nonNull(freq)) {
            lqw.eq(EmProcessSensor::getDataFreq, freq);
        }
        return list(lqw);
    }

    @Override
    public void repairSysParkId(String sensorCode) {
        List<EmProcessSensor> list = null;
        List<String> sensorCodes = null;
        // 如果
        if (StringUtils.isNotBlank(sensorCode)) {
            sensorCodes = Arrays.asList(sensorCode.split(","));
        }
        list = emProcessSensorMapper.listBySensorCodes(sensorCodes);
        if (CollectionUtils.isEmpty(list)) {
            log.info("数据处理-设备数据为空，不需要处理园区id");
            return;
        }
        List<EmProcessSensor> upList = new ArrayList<>(list.size());
        list.forEach(e -> {
            if (StringUtils.isBlank(e.getSysParkId())) {
                return;
            }
            EmProcessSensor up = new EmProcessSensor();
            up.setId(e.getId());
            up.setSysParkId(e.getSysParkId());
            upList.add(up);
        });
        if (CollectionUtils.isEmpty(upList)) {
            return;
        }
        updateBatchById(upList);
    }
}
