package org.jeecg.modules.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.jeecg.common.system.base.entity.SysBaseEntity;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 能源监测-数据处理-设备
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("em_process_sensor")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "em_process_sensor对象", description = "能源监测-数据处理-设备")
public class EmProcessSensor extends SysBaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 设备类型：1.水，2.电
     */
    @Excel(name = "设备类型：1.水，2.电", width = 15)
    @ApiModelProperty(value = "设备类型：1.水，2.电")
    private Integer type;
    /**
     * 安科瑞用户id
     */
    @Excel(name = "安科瑞用户id", width = 15)
    @NotBlank(message = "安科瑞用户id不能为空！")
    @ApiModelProperty(value = "安科瑞用户id")
    private String userId;
    /**
     * 终端地址
     */
    @Excel(name = "终端地址", width = 15)
    @NotBlank(message = "终端地址不能为空！")
    @ApiModelProperty(value = "终端地址")
    private String deviceAddr;
    /**
     * jcd_id
     */
    @Excel(name = "jcd_id", width = 15)
    @NotBlank(message = "jcd_id不能为空！")
    @ApiModelProperty(value = "jcd_id")
    private String jcdId;
    /**
     * 最近通讯时间
     */
    @Excel(name = "最新通讯时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "最新通讯时间不能为空！")
    @ApiModelProperty(value = "最新通讯时间")
    private Date latestComTime;
    /**
     * 最近通讯时间
     */
    @Excel(name = "回溯数据开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "回溯数据开始时间不能为空！")
    @ApiModelProperty(value = "回溯数据开始时间")
    private Date recallStartTime;
    /**
     * 最近通讯时间
     */
    @Excel(name = "回溯数据结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "回溯数据结束时间不能为空！")
    @ApiModelProperty(value = "回溯数据结束时间")
    private Date recallEndTime;
    /**
     * 是否删除：0-否，1-是
     */
    @Excel(name = "是否删除：0-否，1-是", width = 15)
    @NotNull(message = "是否删除：0-否，1-是不能为空！")
    @ApiModelProperty(value = "是否删除：0-否，1-是")
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;
    /**
     * 数据频率
     */
    @Excel(name = "数据频率", width = 15)
    @NotNull(message = "数据频率不能为空！")
    @ApiModelProperty(value = "数据频率")
    private Integer dataFreq;
    /**
     * 阀控是否开启：0-否，1-是
     **/
    @ApiModelProperty(value = "阀控是否开启")
    @TableField("is_valve_open")
    private Boolean valveOpen;
    /**
     * 数据来源  1 安科瑞，2 前置机，3 模拟数据
     */
    @Excel(name = "数据来源", width = 15)
    @NotNull(message = "数据来源不能为空！")
    @ApiModelProperty(value = "数据来源")
    private Integer dataSources;
    /**
     * 初始化数据
     **/
    @ApiModelProperty(value = "初始化数据")
    private BigDecimal initDataVal;
    /**
     * 最近任务时间
     */
    @Excel(name = "最近任务时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "最近任务时间不能为空！")
    @ApiModelProperty(value = "最近任务时间")
    private Date latestTaskTime;

    /** 园区id */
    @NotBlank(message = "园区id不能为空！")
    @ApiModelProperty(value = "园区id")
    private String sysParkId;

    /**
     * 构造方法
     *
     * @param type
     * @param userId
     * @param deviceAddr
     * @param jcdId
     */
    public EmProcessSensor(Integer type, String userId, String deviceAddr, String jcdId) {
        this.type = type;
        this.userId = userId;
        this.deviceAddr = deviceAddr;
        this.jcdId = jcdId;
    }

    /**
     * 构造方法
     *
     * @param deviceAddr
     * @param latestComTime
     * @param dataFreq
     * @param dataSources
     */
    public EmProcessSensor(String deviceAddr, Date latestComTime, Integer dataFreq, Integer dataSources) {
        this.deviceAddr = deviceAddr;
        this.latestComTime = latestComTime;
        this.dataFreq = dataFreq;
        this.dataSources = dataSources;
    }
}
