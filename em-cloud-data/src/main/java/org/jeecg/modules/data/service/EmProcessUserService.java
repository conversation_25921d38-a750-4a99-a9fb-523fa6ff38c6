package org.jeecg.modules.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.data.entity.EmProcessUser;

import java.util.List;

/**
 * 能源监测-数据处理-用户Service接口
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
public interface EmProcessUserService extends IService<EmProcessUser> {

    /**
     * 查所有用户
     *
     * @return
     */
    List<EmProcessUser> getAll();

    /**
     * 查一个
     *
     * @param userId
     * @return
     */
    EmProcessUser getOneByUserId(String userId);
}
