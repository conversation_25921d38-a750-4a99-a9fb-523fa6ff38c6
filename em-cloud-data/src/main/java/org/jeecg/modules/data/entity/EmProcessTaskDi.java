package org.jeecg.modules.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.jeecg.common.system.base.entity.SysBaseEntity;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 能源监测-数据处理-任务
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("em_process_task_di")
@EqualsAndHashCode(callSuper = true)
public class EmProcessTaskDi extends SysBaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * taskId
     */
    @Excel(name = "taskId", width = 15)
    @NotBlank(message = "di不能为空！")
    @ApiModelProperty(value = "di")
    private String taskId;
    /**
     * di
     */
    @Excel(name = "di", width = 15)
    @NotBlank(message = "di不能为空！")
    @ApiModelProperty(value = "di")
    private String di;
    /**
     * di名称
     */
    @Excel(name = "di名称", width = 15)
    @NotBlank(message = "di名称不能为空！")
    @ApiModelProperty(value = "di名称")
    private String name;
    /**
     * 字段code
     */
    @Excel(name = "字段code", width = 15)
    @NotBlank(message = "字段code不能为空！")
    @ApiModelProperty(value = "字段code")
    private String code;
    /**
     * pn
     */
    @Excel(name = "pn", width = 15)
    @NotNull(message = "pn不能为空！")
    @ApiModelProperty(value = "pn")
    private Integer pn;
    /**
     * 是否删除：0-否，1-是
     */
    @Excel(name = "是否删除：0-否，1-是", width = 15)
    @NotNull(message = "是否删除：0-否，1-是不能为空！")
    @ApiModelProperty(value = "是否删除：0-否，1-是")
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;
}