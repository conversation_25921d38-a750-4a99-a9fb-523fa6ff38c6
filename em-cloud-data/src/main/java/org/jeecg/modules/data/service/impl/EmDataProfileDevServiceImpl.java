package org.jeecg.modules.data.service.impl;

import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.data.service.EmDataProfileService;
import org.jeecg.modules.data.service.EmProcessSensorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 能源监测-测试环境
 *
 * <AUTHOR>
 * @date 2022-10-12
 */
@Profile({"dev", "test", "pet"})
@Service
public class EmDataProfileDevServiceImpl implements EmDataProfileService {
    @Autowired
    private EmProcessSensorService emProcessSensorService;

    @Override
    public void checkMeterControl(String id) {
        if (Objects.isNull(emProcessSensorService.getOneByDeviceAddr(id))) {
            throw new JeecgBootException("系统查询不到该传感器，请联系管理员！");
        }
        String deviceAddr = "0010010101,0013850101,0020010101,12108125030390";
        if (!deviceAddr.contains(id)) {
            throw new JeecgBootException("测试环境开关阀只支持0010010101,0013850101,0020010101,12108125030390！");
        }
    }
}
