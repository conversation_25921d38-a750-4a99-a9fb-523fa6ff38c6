package org.jeecg.modules.data.service;

import org.jeecg.modules.data.dto.EmSensorRerunDataDTO;
import org.jeecg.modules.data.entity.EmGatewayData;
import org.jeecg.modules.data.entity.EmSensorOriginData;

import java.util.Date;
import java.util.List;

/**
 * 能源监测-传感器原始数据Service
 *
 * <AUTHOR>
 * @date 2022-04-24 14:37
 */
public interface EmSensorOriginDataService {
    /**
     * 新增
     *
     * @param gatewayData
     * @return
     */
    List<EmSensorOriginData> insert(EmGatewayData gatewayData);

    /**
     * 获取数据列表
     *
     * @param dataDate
     * @param deviceAddr
     * @param di
     * @return
     */
    List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, String di);

    /**
     * 获取数据列表
     *
     * @param dataDate
     * @param deviceAddr
     * @param pn
     * @param di
     * @return
     */
    List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, Integer pn, String di);

    /**
     * 获取数据列表
     *
     * @param dataDate
     * @param deviceAddr
     * @param pn
     * @return
     */
    List<EmSensorOriginData> getList(Date dataDate, String deviceAddr, Integer pn);

    /**
     * 重新发送数据
     *
     * @param deviceAddr
     * @param pn
     * @param di
     * @param sdt
     * @param edt
     */
    void reSendData(String deviceAddr, Integer pn, String di, String sdt, String edt);

    /**
     * 批量重新发送数据
     *
     * @param deviceAddrList
     * @param pn
     * @param di
     * @param sdt
     * @param edt
     */
    void reSendDataBatch(List<String> deviceAddrList, Integer pn, String di, String sdt, String edt);

    /**
     * 重新发送数据
     *
     * @param deviceAddr
     * @param pn
     * @param di
     * @param sdt
     * @param edt
     * @param dataSources 数据来源  1 安科瑞，2 前置机，3 模拟数据
     * @param dataFreq    频率 为空默认值60分钟
     */
    void originDataCommon(String deviceAddr, Integer pn, String di, Date sdt, Date edt, Integer dataSources, Integer dataFreq);

    /**
     * 重新发送数据
     *
     * @param emSensorRerunDataDTO
     */
    void rerunOriginData(EmSensorRerunDataDTO emSensorRerunDataDTO);

    /**
     * 根据网关ID，数据时间和DI获取本条下一条
     *
     * @param deviceAddr
     * @param dataTime
     * @return
     */
    EmSensorOriginData getNowOrNextOneBy(String deviceAddr, Date dataTime);

    /**
     * 根据网关ID和DI获取第一条数据
     *
     * @param deviceAddr
     * @param di
     * @return
     */
    EmSensorOriginData getFirstOne(String deviceAddr, String di);
}
