package org.jeecg.modules.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.jeecg.common.system.base.entity.SysBaseEntity;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 能源监测-数据处理-用户
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("em_process_user")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "em_process_user对象", description = "能源监测-数据处理-用户")
public class EmProcessUser extends SysBaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * userId
     */
    @Excel(name = "用户id", width = 15)
    @NotBlank(message = "用户id不能为空！")
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 用户名
     */
    @Excel(name = "用户名", width = 15)
    @NotBlank(message = "用户名不能为空！")
    @ApiModelProperty(value = "用户名")
    private String name;
    /**
     * 密码
     */
    @Excel(name = "密码", width = 15)
    @NotBlank(message = "密码不能为空！")
    @ApiModelProperty(value = "密码")
    private String password;
    /**
     * 电话
     */
    @Excel(name = "电话", width = 15)
    @NotBlank(message = "电话不能为空！")
    @ApiModelProperty(value = "电话")
    private String tel;
    /**
     * 地址
     */
    @Excel(name = "地址", width = 15)
    @NotBlank(message = "地址不能为空！")
    @ApiModelProperty(value = "地址")
    private String address;
    /**
     * 是否管理员：0-否，1-是
     */
    @Excel(name = "是否管理员：0-否，1-是", width = 15)
    @NotNull(message = "是否管理员：0-否，1-是不能为空！")
    @ApiModelProperty(value = "是否管理员：0-否，1-是")
    @TableField("is_admin")
    private Boolean admin;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否删除：0-否，1-是
     */
    @Excel(name = "是否删除：0-否，1-是", width = 15)
    @NotNull(message = "是否删除：0-否，1-是不能为空！")
    @ApiModelProperty(value = "是否删除：0-否，1-是")
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;

    /**
     * 构造方法
     *
     * @param userId
     * @param name
     * @param id
     */
    public EmProcessUser(String userId, String name, String id) {
        this.userId = userId;
        this.name = name;
        this.id = id;
    }
}
