package org.jeecg.modules.data.utils;

import org.apache.commons.lang3.ObjectUtils;
import org.jeecg.common.exception.JeecgBootException;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * cron表达式工具
 *
 * <AUTHOR> <PERSON>hen<PERSON><PERSON>
 * @date : 2022/1/13 0:13
 */
public class CronUtil {

    /**
     * 只执行一次
     */
    private static final String TRANS_CRON_FORMAT_ONCE = "ss mm HH dd MM ? yyyy";
    /**
     * 每月一次
     */
    private static final String MONTH_CRON_FORMAT_ONCE = "ss mm HH dd * ?";

    private CronUtil() {
        throw new JeecgBootException("You don't need to create");
    }

    /**
     * 获取时间的cron表达式
     *
     * @param date 时间
     * @return cron表达式
     */
    public static String getMonthCron(Date date) {
        if (ObjectUtils.isNotEmpty(date)) {
            SimpleDateFormat sdf = new SimpleDateFormat("00 00 00 1 MM ? ");
            return sdf.format(date);
        }
        throw new JeecgBootException("处理时间为空");
    }

    /**
     * 日期转cron  只执行一次
     *
     * @param date 日期
     * @return cron表达式
     */
    public static String getOnceCron(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(TRANS_CRON_FORMAT_ONCE);
        String cron = "";
        if (date != null) {
            cron = sdf.format(date);
        }
        return cron;
    }

    /**
     * 最大上报频率
     */
    private static final int REPORT_FREQ_CRON_MAX = 60;

    /**
     * 最小上报频率
     */
    private static final int REPORT_FREQ_CRON_MIN = 0;

    /**
     * 获取上报频率的cron表达式
     *
     * @param reportFreq 上报频率
     * @return 返回cron表达式
     */
    public static String getReportFreqCron(int reportFreq) {
        if (reportFreq >= REPORT_FREQ_CRON_MAX || reportFreq <= REPORT_FREQ_CRON_MIN) {
            return "0 0 * * * ? ";
        } else {
            return "0 0/" + reportFreq + " * * * ? ";
        }
    }

    /**
     * 日期转cron  每月执行一次
     *
     * @param date 日期
     * @return cron表达式
     */
    public static String getMonthOnceCron(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(MONTH_CRON_FORMAT_ONCE);
        String cron = "";
        if (date != null) {
            cron = sdf.format(date);
        }
        return cron;
    }
}
