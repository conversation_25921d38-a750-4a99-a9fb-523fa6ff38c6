package org.jeecg.modules.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.constant.SysConstants;
import org.jeecg.modules.data.service.EmProcessUserService;
import org.jeecg.modules.data.entity.EmProcessUser;
import org.jeecg.modules.data.mapper.EmProcessUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 能源监测-数据处理-用户Service实现
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Service
public class EmProcessUserServiceImpl extends ServiceImpl<EmProcessUserMapper, EmProcessUser> implements EmProcessUserService {
    @Autowired
    private EmProcessUserMapper emProcessUserMapper;

    @Override
    public List<EmProcessUser> getAll() {
        return emProcessUserMapper.selectList(new LambdaQueryWrapper<EmProcessUser>().eq(EmProcessUser::getDeleted, SysConstants.IsStatus.NO));
    }

    @Override
    public EmProcessUser getOneByUserId(String userId) {
        return emProcessUserMapper.selectOne(new LambdaQueryWrapper<EmProcessUser>()
                .eq(EmProcessUser::getDeleted, SysConstants.IsStatus.NO).eq(EmProcessUser::getUserId, userId));
    }
}
