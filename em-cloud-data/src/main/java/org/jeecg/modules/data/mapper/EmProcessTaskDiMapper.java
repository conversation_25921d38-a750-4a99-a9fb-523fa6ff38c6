package org.jeecg.modules.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.data.entity.EmProcessTaskDi;
import org.jeecg.modules.data.vo.TaskDiDTO;

import java.util.List;

/**
 * 能源监测-数据处理-任务diMapper接口
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Mapper
public interface EmProcessTaskDiMapper extends BaseMapper<EmProcessTaskDi> {
    /**
     * 根据主键ID查询
     *
     * @param mainId
     * @return
     */
    List<TaskDiDTO> selectByMainId(@Param("mainId") String mainId);
}