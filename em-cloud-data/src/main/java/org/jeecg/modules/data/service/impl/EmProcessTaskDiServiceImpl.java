package org.jeecg.modules.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.data.entity.EmProcessTaskDi;
import org.jeecg.modules.data.mapper.EmProcessTaskDiMapper;
import org.jeecg.modules.data.service.EmProcessTaskDiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 能源监测-数据处理-任务对应的diService实现
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Service
public class EmProcessTaskDiServiceImpl extends ServiceImpl<EmProcessTaskDiMapper, EmProcessTaskDi> implements EmProcessTaskDiService {
    @Autowired
    private EmProcessTaskDiMapper emProcessTaskDiMapper;
}
