package org.jeecg.modules.data.controller;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.SysLog;
import org.jeecg.common.aspect.annotation.SysReqLimit;
import org.jeecg.modules.data.service.EmSensorOriginDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 传感器原始数据Controller
 *
 * <AUTHOR>
 * @date 2022-07-08 11:31
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/data/sensorOriginData")
public class EmSensorOriginDataController {
    @Autowired
    private EmSensorOriginDataService emSensorOriginDataService;

    /**
     * 重新发送数据
     *
     * @param deviceAddr
     * @param pn
     * @param di
     * @param sdt
     * @param edt
     * @return
     */
    @GetMapping(value = "/reSendData")
    @SysLog(value = "能源监测-传感器原始数据-重新发送")
    @SysReqLimit(key = "data:sensorOriginData:reSendData")
    @ApiOperation(value = "能源监测-传感器原始数据-重新发送", notes = "能源监测-传感器原始数据-重新发送")
    public Result<String> reSendData(@RequestParam(name = "deviceAddr", required = false) String deviceAddr,
                                     @RequestParam(name = "pn", required = false) Integer pn,
                                     @RequestParam(name = "di") String di,
                                     @RequestParam(name = "sdt") String sdt,
                                     @RequestParam(name = "edt") String edt) {
        emSensorOriginDataService.reSendData(deviceAddr, pn, di, sdt, edt);
        return Result.ok("重新发送成功");
    }

    /**
     * 重新发送数据
     *
     * @param deviceAddrList
     * @param pn
     * @param di
     * @param sdt
     * @param edt
     * @return
     */
    @GetMapping(value = "/reSendDataBatch")
    @SysLog(value = "能源监测-传感器原始数据-批量重新发送")
    @SysReqLimit(key = "data:sensorOriginData:reSendDataBatch")
    @ApiOperation(value = "能源监测-传感器原始数据-批量重新发送", notes = "能源监测-传感器原始数据-批量重新发送")
    public Result<String> reSendDataBatch(@RequestParam(name = "deviceAddrList", required = false) List<String> deviceAddrList,
                                          @RequestParam(name = "pn", required = false) Integer pn,
                                          @RequestParam(name = "di") String di,
                                          @RequestParam(name = "sdt") String sdt,
                                          @RequestParam(name = "edt") String edt) {
        emSensorOriginDataService.reSendDataBatch(deviceAddrList, pn, di, sdt, edt);
        return Result.ok("重新发送成功");
    }
}
