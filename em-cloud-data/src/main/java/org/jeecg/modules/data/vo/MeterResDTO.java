package org.jeecg.modules.data.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 电表信息
 *
 * <AUTHOR>
 * @date 2022-01-14
 */
@Data
public class MeterResDTO {
    /**
     * 电表编号
     */
    @JsonProperty("MeterID")
    private String meterId;
    /**
     * 商铺号
     */
    @JsonProperty("RoomID")
    private String roomId;
    /**
     * 开户时的总用电量
     */
    @JsonProperty("ThisPower")
    private BigDecimal thisPower;
    /**
     * 上次销户时的总用电量
     */
    @JsonProperty("LastPower")
    private BigDecimal lastPower;
    /**
     * 剩余电费
     */
    @JsonProperty("PowerRemain")
    private String powerRemain;
    /**
     * 价格计划
     */
    @JsonProperty("PricePlan")
    private String pricePlan;
    /**
     * 合并计量余额
     */
    @JsonProperty("Money")
    private String money;
    /**
     * 报警1状态（true为达到报警1）
     */
    @JsonProperty("AlarmA")
    private Boolean alarmA;
    /**
     * 报警2状态（true为达到报警2）
     */
    @JsonProperty("AlarmB")
    private Boolean alarmB;
    /**
     * 是否欠费（true表示欠费）
     */
    @JsonProperty("OweMoney")
    private Boolean oweMoney;
    /**
     * 是否失连（true为失连）
     */
    @JsonProperty("UnConnect")
    private Boolean unConnect;
    /**
     * 分合闸状态（true为拉闸，false为合闸）
     */
    @JsonProperty("OpenOrClose")
    private Boolean openOrClose;
}
