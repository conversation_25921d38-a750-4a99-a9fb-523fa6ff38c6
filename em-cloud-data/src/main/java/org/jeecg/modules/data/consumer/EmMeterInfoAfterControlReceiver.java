package org.jeecg.modules.data.consumer;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.RabbitMqConstants;
import org.jeecg.common.constant.StrPool;
import org.jeecg.common.util.ThrowableUtils;
import org.jeecg.modules.data.service.EmDataSourcesService;
import org.jeecg.modules.data.vo.EmMeterInfoAfterControlDTO;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;


/**
 * 阀控之后获取设备信息消费者
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@Slf4j
@Component
public class EmMeterInfoAfterControlReceiver {
    @Autowired
    private EmDataSourcesService emDataSourcesService;

    /**
     * 传感器中断
     *
     * @param message
     * @param channel
     * @param deliveryTag
     */
    @RabbitListener(bindings = @QueueBinding(
            exchange = @Exchange(name = "${energy-monitor.profiles-active.rabbitMqPrefix}" + StrPool.DOT + RabbitMqConstants.METER_INFO_AFTER_CONTROL_EXCHANGE, delayed = "true"),
            value = @Queue(value = "${energy-monitor.profiles-active.rabbitMqPrefix}" + StrPool.DOT + RabbitMqConstants.METER_INFO_AFTER_CONTROL, durable = "true")
    ), concurrency = "4-8")
    public void onMessage(Message<String> message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("阀控之后获取设备信息消息消费 → {}", message.getPayload());
            String payload = message.getPayload();
            if (StringUtils.isNotBlank(payload)) {
                EmMeterInfoAfterControlDTO emMeterInfoAfterControlDTO = JSON.parseObject(payload, EmMeterInfoAfterControlDTO.class);
                if (Objects.nonNull(emMeterInfoAfterControlDTO)) {
                    emDataSourcesService.meterControlDelayGetInfo(emMeterInfoAfterControlDTO);
                    if (log.isDebugEnabled()) {
                        log.debug("阀控之后获取设备信息消息消费成功，emMeterInfoAfterControlDTO → {}", emMeterInfoAfterControlDTO);
                    }
                }
            }
            channel.basicAck(deliveryTag, true);
        } catch (Exception e) {
            log.error("阀控之后获取设备信息消息消费失败，重新放回队列，msg → {}，失败原因 → {}", message.getPayload(), ThrowableUtils.getStackTrace(e));
            try {
                // deliveryTag → 该消息的index，multiple → 是否批量，true:将一次性拒绝所有小于deliveryTag的消息，requeue → 被拒绝的是否重新入队列
                channel.basicNack(deliveryTag, true, true);
            } catch (IOException ex) {
                log.error("阀控之后获取设备信息消息重新放回队列错误，失败原因 → {}", ThrowableUtils.getStackTrace(ex));
            }
        }
    }
}
