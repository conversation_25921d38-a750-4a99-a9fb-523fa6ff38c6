package org.jeecg.modules.data.repository;

import org.jeecg.modules.data.entity.EmSensorOriginData;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 能源监测-传感器原始数据
 *
 * <AUTHOR>
 * @date 2022-01-20 12:46
 */
public interface EmSensorOriginDataRepository extends MongoRepository<EmSensorOriginData, String> {
    /**
     * 根据数据时间，网关地址，DI，PN获取
     *
     * @param dataTime
     * @param deviceAddr
     * @param di
     * @param pn
     * @return
     */
    EmSensorOriginData findByDataTimeAndDeviceAddrAndDiAndPn(@Param("dataTime") String dataTime, @Param("deviceAddr") String deviceAddr, @Param("di") String di, @Param("pn") Integer pn);

    /**
     * 根据数据日期，网关地址，DI获取
     *
     * @param dataDate
     * @param deviceAddr
     * @param di
     * @param sort
     * @return
     */
    List<EmSensorOriginData> findByDataDateAndDeviceAddrAndDi(@Param("dataDate") String dataDate, @Param("deviceAddr") String deviceAddr, @Param("di") String di, Sort sort);

    /**
     * 根据数据日期，网关地址，PN, DI获取
     *
     * @param dataDate
     * @param deviceAddr
     * @param pn
     * @param di
     * @param sort
     * @return
     */
    List<EmSensorOriginData> findByDataDateAndDeviceAddrAndPnAndDi(@Param("dataDate") String dataDate, @Param("deviceAddr") String deviceAddr, @Param("pn") Integer pn, @Param("di") String di, Sort sort);

    /**
     * 根据数据日期，网关地址，PN获取
     *
     * @param dataDate
     * @param deviceAddr
     * @param pn
     * @param sort
     * @return
     */
    List<EmSensorOriginData> findByDataDateAndDeviceAddrAndPn(@Param("dataDate") String dataDate, @Param("deviceAddr") String deviceAddr, @Param("pn") Integer pn, Sort sort);

    /**
     * 根据网关ID，数据时间和DI获取本条下一条
     *
     * @param deviceAddr
     * @param dataTime
     * @return
     */
    @Aggregation(pipeline = {
            "{'$match': {'device_addr': ?0, 'data_time': {$gte: ?1}, 'di':'P0053'}}",
            "{'$sort': {'data_time':  1}}",
            "{'$limit': 1}"
    })
    EmSensorOriginData findNowOrNextOneBy(@Param("deviceAddr") String deviceAddr, @Param("dataTime") String dataTime);

    /**
     * 根据网关ID和DI获取第一条数据
     *
     * @param deviceAddr
     * @param di
     * @return
     */
    @Aggregation(pipeline = {
            "{'$match': {'device_addr': ?0, 'di': ?1}}",
            "{'$sort': {'data_time':  1}}",
            "{'$limit': 1}"
    })
    EmSensorOriginData findFirstOne(@Param("deviceAddr") String deviceAddr, @Param("dataTime") String di);
}
