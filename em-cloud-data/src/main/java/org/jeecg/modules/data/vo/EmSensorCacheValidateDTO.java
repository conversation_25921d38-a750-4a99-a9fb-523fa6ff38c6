package org.jeecg.modules.data.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 传感器校验缓存是否存在延时对象
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmSensorCacheValidateDTO {
    /**
     * 传感器编号
     */
    private String sensorCode;
    /**
     * 网关编号
     */
    private String deviceAddr;
    /**
     * 触发次数
     */
    private Integer triggerCount;

    public EmSensorCacheValidateDTO(String sensorCode, String deviceAddr) {
        this.sensorCode = sensorCode;
        this.deviceAddr = deviceAddr;
        this.triggerCount = 0;
    }
}
