package org.jeecg.modules.data.service;

import org.jeecg.modules.data.dto.EmProcessSensorDTO;
import org.jeecg.modules.data.dto.EmSensorRerunDataDTO;
import org.jeecg.modules.data.dto.MeterControlReqDTO;
import org.jeecg.modules.data.vo.EmMeterInfoAfterControlDTO;

import java.util.Date;
import java.util.List;

/**
 * 能源监测- 处理不同数据平台走向
 *
 * <AUTHOR>
 * @date 2022-11-2
 */
public interface EmDataSourcesService {
    /**
     * 新增传感器
     *
     * @param emProcessSensorList
     * @return
     */
    void addSensor(List<EmProcessSensorDTO> emProcessSensorList);

    /**
     * 校验传感器缓存是否存在
     *
     * @param sensorCode 传感器编号
     * @param deviceAddr 网关编号
     */
    void checkExistCacheRunData(String sensorCode, String deviceAddr, Integer triggerCount);

    /**
     * 原始通道回溯数据
     *
     * @param deviceAddr
     * @param date
     * @param dataSources
     * @param dataFreq
     */
    void originChannel(String deviceAddr, Date date, Integer dataSources, Integer dataFreq);

    /**
     * 删除后新增 回溯数据
     *
     * @param emSensorRerunDataDTO
     */
    void deleteJobAddAgain(EmSensorRerunDataDTO emSensorRerunDataDTO);

    /**
     * 重跑数据，修改安装时间、修改指标模板
     *
     * @param emSensorRerunDataDTO
     */
    void rerunData(EmSensorRerunDataDTO emSensorRerunDataDTO);

    /**
     * 阀控
     *
     * @param meterControlReqDTO 阀控参数
     */
    void meterControl(MeterControlReqDTO meterControlReqDTO);

    /**
     * 分合闸后延时获取设备信息
     *
     * @param emMeterInfoAfterControlDTO 参数
     */
    void meterControlDelayGetInfo(EmMeterInfoAfterControlDTO emMeterInfoAfterControlDTO);
}
