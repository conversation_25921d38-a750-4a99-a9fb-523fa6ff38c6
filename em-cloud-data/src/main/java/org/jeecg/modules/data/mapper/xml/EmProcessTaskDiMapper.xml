<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.data.mapper.EmProcessTaskDiMapper">
    <resultMap id="BaseResultMap" type="org.jeecg.modules.data.entity.EmProcessTaskDi">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="task_id" jdbcType="VARCHAR" property="taskId" />
        <result column="di" jdbcType="VARCHAR" property="di" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="pn" jdbcType="INTEGER" property="pn" />
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">id, task_id, di, `name`, code, pn, is_deleted, create_by, create_time, update_by, update_time</sql>

    <!-- 表名称 -->
    <sql id="tname">em_process_task_di eptd</sql>

    <!-- 字段名称 -->
    <sql id="cols">eptd.di, eptd.name, eptd.code, eptd.pn</sql>

    <resultMap id="selectByMainId" type="org.jeecg.modules.data.vo.TaskDiDTO">
        <result column="di" jdbcType="VARCHAR" property="di" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="pn" jdbcType="INTEGER" property="pn" />
    </resultMap>

    <select id="selectByMainId" parameterType="java.lang.String" resultMap="selectByMainId">
        SELECT
            <include refid="cols" />
        FROM <include refid="tname" />, em_process_task_relation_di eptrd
        WHERE eptd.id = eptrd.task_di_id
            AND eptrd.is_deleted = 0
            AND eptd.is_deleted = 0
            AND eptrd.task_id = #{mainId}
  </select>
</mapper>