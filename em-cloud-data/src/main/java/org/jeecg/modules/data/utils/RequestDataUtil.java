package org.jeecg.modules.data.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtils;
import org.jeecg.modules.data.config.ProcessDataConfig;
import org.jeecg.modules.data.constant.AcrelDataConstants;
import org.jeecg.modules.data.constant.enums.DataInterfaceEnum;
import org.jeecg.modules.data.entity.EmProcessDataLog;
import org.jeecg.modules.data.service.EmProcessDataLogService;
import org.jeecg.modules.data.vo.LoginReqDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * 请求数据
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Slf4j
@Component
public class RequestDataUtil {
    private static final String MD5 = "md5";
    private static final String TOKEN = "Token";
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ProcessDataConfig dataConfig;
    @Autowired
    private EmProcessDataLogService emProcessDataLogService;

    /**
     * md5加密
     *
     * @param str
     * @return
     */
    public String getMd5Str(String str) {
        byte[] digest = null;
        try {
            MessageDigest md5 = MessageDigest.getInstance(MD5);
            digest = md5.digest(str.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        //16是表示转换为16进制数
        return new BigInteger(1, digest).toString(16);
    }

    /**
     * @param methodType   方法类型
     * @param map          接口参数
     * @param functionName 接口名称
     * @param isGetToken   是否获取token接口
     * @param requestType  请求类型
     * @return
     * @throws Exception
     */
    public EmProcessDataLog httpJsonObject(Integer methodType, Map<String, String> map, String functionName, boolean isGetToken, Integer requestType) throws Exception {
        // 头部信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        ResponseEntity<String> responseEntity;
        // 塞token
        if (!isGetToken) {
            headers.add(TOKEN, getToken());
            log.info("获取token完成！");
        }
        // 初始化日志
        EmProcessDataLog dataLog = new EmProcessDataLog();
        dataLog.setType(methodType);
        dataLog.setInterfaceUrl(functionName);
        dataLog.setRequestType(requestType);
        // 请求类型
        if (AcrelDataConstants.RequestType.POST.equals(requestType)) {
            ObjectMapper mapper = new ObjectMapper();
            HttpEntity<String> entity = new HttpEntity<>(mapper.writeValueAsString(map), headers);
            log.info("requestEntity → " + mapper.writeValueAsString(entity));
            responseEntity = restTemplate.postForEntity(dataConfig.getUrl().concat(functionName), entity, String.class);
        } else {
            HttpEntity<String> entity = new HttpEntity<>(headers);
            functionName = functionName.concat(concatUrl(map));
            String url = dataConfig.getUrl().concat(functionName);
            log.info("发起请求，url → {}, entity → {}", url, entity);
            responseEntity = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
        }
        dataLog.setRequestParam(JSON.toJSONString(map));
        JSONObject body = JSON.parseObject(responseEntity.getBody());
        // 获取状态码 不等于200抛异常
        Integer status = Integer.valueOf(body.getString("status"));
        dataLog.setResponseMsg(body.getString("msg"));
        // 获取网关id
        dataLog.setDeviceAddr(getDeviceAdrr(map));
        if (!status.equals(HttpStatus.OK.value())) {
            log.error("调用接口 → {}，返回状态异常，body → {}", functionName, body);
            dataLog.setResponseBody(body.toJSONString());
            dataLog.setSuccess(false);
            throw new Exception(JSON.toJSONString(dataLog));
        }
        String repData = body.getString("data");
        log.info("调用成功，body.Data → {}", repData);
        dataLog.setResponseBody(repData);
        dataLog.setSuccess(true);
        return dataLog;
    }

    /**
     * get请求拼接url
     *
     * @param map
     * @return
     */
    private String concatUrl(Map<String, String> map) {
        String url = "";
        if (map == null || map.isEmpty()) {
            return url;
        }
        url = "?";
        for (Object key : map.keySet()) {
            url = url.concat(key.toString()).concat("=").concat(map.get(key)).concat("&");
        }
        url = url.substring(0, url.length() - 1);
        return url;
    }

    /**
     * 获取网关id
     *
     * @param map
     * @return
     */
    private String getDeviceAdrr(Map<String, String> map) {
        if (map.containsKey(AcrelDataConstants.METER_ID)) {
            return map.get(AcrelDataConstants.METER_ID);
        }
        if (map.containsKey(AcrelDataConstants.METER_ID_TWO)) {
            return map.get(AcrelDataConstants.METER_ID_TWO);
        }
        if (map.containsKey(AcrelDataConstants.STR)) {
            return map.get(AcrelDataConstants.STR);
        }
        return null;
    }

    /**
     * 获取token
     *
     * @return
     */
    public String getToken() {
        String token = redisUtils.strGet(AcrelDataConstants.ACREL_DATA_TOKEN);
        log.info("redis-token → {}", token);
        try {
            if (StringUtils.isBlank(token)) {
                DataInterfaceEnum interfaceEnum = DataInterfaceEnum.USER_LOGIN;
                LoginReqDTO loginReqDTO = new LoginReqDTO();
                loginReqDTO.setUsername(dataConfig.getUsername());
                loginReqDTO.setPassword(getMd5Str(dataConfig.getPassword()));
                loginReqDTO.setType("1");
                EmProcessDataLog dataLog = httpJsonObject(interfaceEnum.getCode(), JSON.parseObject(JSON.toJSONString(loginReqDTO), Map.class), interfaceEnum.getFunction()
                        , true, interfaceEnum.getType());
                JSONObject body = JSON.parseObject(dataLog.getResponseBody());
                log.info("获取token请求成功, body → {}", body.toString());
                emProcessDataLogService.insert(dataLog);
                token = body.getString(TOKEN);
            }
        } catch (Exception e) {
            log.error("获取token异常，→ {}", e.getMessage());
        }
        // 刷新token过期时间
        redisUtils.strSet(AcrelDataConstants.ACREL_DATA_TOKEN, token, 60 * 60 * 24);
        return token;
    }
}
