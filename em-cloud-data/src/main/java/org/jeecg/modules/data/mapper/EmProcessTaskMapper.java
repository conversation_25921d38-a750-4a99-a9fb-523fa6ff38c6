package org.jeecg.modules.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.jeecg.modules.data.entity.EmProcessTask;
import org.jeecg.modules.data.vo.TaskDTO;

import java.util.List;

/**
 * 能源监测-数据处理-任务Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-13
 */

@Mapper
public interface EmProcessTaskMapper extends BaseMapper<EmProcessTask> {
    /**
     * 查询所有和明细
     *
     * @return
     */
    List<TaskDTO> selectAllUpdateRedis();
}