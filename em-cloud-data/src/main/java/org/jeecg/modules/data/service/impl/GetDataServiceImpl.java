package org.jeecg.modules.data.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializeFilter;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.boot.starter.rabbitmq.client.RabbitMqClient;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.*;
import org.jeecg.common.constant.enums.DataSourcesEnum;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.EmGatewayDataContentDTO;
import org.jeecg.common.util.*;
import org.jeecg.modules.data.config.EmPrefixProperties;
import org.jeecg.modules.data.config.ProcessDataConfig;
import org.jeecg.modules.data.constant.AcrelDataConstants;
import org.jeecg.modules.data.constant.enums.DataInterfaceEnum;
import org.jeecg.modules.data.constant.enums.QuartzJobEnum;
import org.jeecg.modules.data.dto.EmSensorRerunDataDTO;
import org.jeecg.modules.data.entity.*;
import org.jeecg.modules.data.service.*;
import org.jeecg.modules.data.utils.CronUtil;
import org.jeecg.modules.data.utils.RequestDataUtil;
import org.jeecg.modules.data.vo.MockDataDTO;
import org.jeecg.modules.data.vo.TaskDTO;
import org.jeecg.modules.data.vo.TaskDiDTO;
import org.jeecg.modules.data.vo.UserAndMeterResDTO;
import org.jeecg.modules.web.api.IEmWebApi;
import org.jeecg.modules.web.dto.SysParkApiDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static cn.hutool.extra.spring.SpringUtil.getBean;

/**
 * 获取数据
 *
 * <AUTHOR>
 * @date 2022-01-12 10:48
 */
@Slf4j
@Service
public class GetDataServiceImpl implements GetDataService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private ProcessDataConfig dataConfig;
    @Autowired
    private RabbitMqClient rabbitMqClient;
    @Autowired
    private RequestDataUtil requestDataUtil;
    @Autowired
    private QuartzJobService quartzJobService;
    @Autowired
    private EmPrefixProperties emPrefixProperties;
    @Autowired
    private EmProcessTaskService emProcessTaskService;
    @Autowired
    private EmGatewayDataService emGatewayDataService;
    @Autowired
    private EmProcessUserService emProcessUserService;
    @Lazy
    @Autowired
    private EmProcessSensorService emProcessSensorService;
    @Autowired
    private EmProcessDataLogService emProcessDataLogService;
    @Lazy
    @Autowired
    private EmSensorOriginDataService emSensorOriginDataService;

    @Autowired
    private IEmWebApi emWebApi;

    /**
     * 方法名首字母大写转换
     *
     * @param str
     * @return
     */
    public static String toLowerCaseFirstOne(String str) {
        if (Character.isLowerCase(str.charAt(0))) {
            return str;
        } else {
            return Character.toLowerCase(str.charAt(0)) + str.substring(1);
        }
    }

    /**
     * 初始化 taskDi
     */
    @PostConstruct
    public void initTaskAndTaskDi() {
        List<TaskDTO> taskDtoList = emProcessTaskService.selectAllUpdateRedis();
        if (CollUtil.isNotEmpty(taskDtoList)) {
            taskDtoList.forEach(taskDTO -> {
                String value = JSON.toJSONString(taskDTO);
                redisUtils.strSet(CharSequenceUtil.format(RedisKeyConstants.TaskDi.MQ_TASK_DI_KEY, taskDTO.getTaskDi()), value);
            });
        }
        // 服务启动查询网关最新数据时间不等于回溯数据结束时间，覆盖回溯数据结束时间
        List<EmProcessSensor> emProcessSensorList = emProcessSensorService.listByLatestComTime();
        if (CollUtil.isNotEmpty(emProcessSensorList)) {
            emProcessSensorList.forEach(emProcessSensor -> {
                emProcessSensor.setRecallEndTime(emProcessSensor.getLatestComTime());
            });
            emProcessSensorService.updateBatchById(emProcessSensorList);
        }
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 1, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> userLogin(Boolean isReTry) throws Exception {
        return Result.ok(requestDataUtil.getToken());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> meterList(Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.METER_LIST;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), null,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        // 调用成功往下走   调用失败回调
        log.info("{}，获取用户名下所有电表信息 return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        // 查询task表  生成json 塞mq
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseBody());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> getMeterInfo(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_METER_INFO;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，获取电表信息 return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseBody());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> salePower(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.SALE_POWER;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，电费充值（管理员权限）return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseMsg());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 1, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> meterControl(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.METER_CONTROL;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，分合闸控制（管理员权限）return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseMsg());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> openUser(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.OPEN_USER;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，开户（管理员权限）return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseMsg());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> editorUser(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.EDITOR_USER;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，修改用户（管理员权限）return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseMsg());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> delUser(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.DEL_USER;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，销户（管理员权限）return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseMsg());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> usePower(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.USE_POWER;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，用电统计return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseBody());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> getMeterWatchInfo(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_METER_WATCH_INFO;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，根据电表编号查管控表信息的接口 return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseBody());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 1, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> getCollect(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_COLLECT;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，根据电表编号获取抄表信息 return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(SysTips.SUCCESS_MSG, dataLog.getResponseBody());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> getAllMeters(Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_COLLECT;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), null,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，获取管理员账号下所有电表（管理员权限） return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseBody());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> getEnergyData(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_ENERGY_DATA;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，获得一段时间内的电表电能数据（管理员权限） return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseBody());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 10000L, multiplier = 1.5))
    @Transactional(rollbackFor = Exception.class)
    public Result<String> getEnergyData2(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_ENERGY_DATA2;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        String returnStr = null;
        String dataTime = map.get("dataTime");
        // 匹配数据时间
        if (StringUtils.isNotBlank(dataTime)) {
            if (StringUtils.isNotBlank(dataLog.getResponseBody()) || !StrPool.SQ_BRACKET.equals(dataLog.getResponseBody())) {
                List<JSONObject> dataList = JSON.parseArray(dataLog.getResponseBody(), JSONObject.class);
                // 判断返回数据是否存在该数据时间的值  不存在抛异常  10秒后重试
                boolean notExist = true;
                for (JSONObject meter : dataList) {
                    if (meter.getString(AcrelDataConstants.CREATE_TIME).equals(dataTime)) {
                        processMeterSend(JSON.toJSONString(meter), interfaceEnum.getDi(), false);
                        notExist = false;
                        returnStr = meter.toString();
                        break;
                    }
                }
                if (notExist) {
                    dataLog.setSuccess(false);
                    dataLog.setResponseMsg("返回值不存在该数据时间！");
                }
            }
        } else {
            // 数据回放返回这段时间所有数据
            processMeterSend(dataLog.getResponseBody(), interfaceEnum.getDi(), true);
            returnStr = dataLog.getResponseBody();
        }
        addLog(dataLog, isReTry);
        return Result.ok(returnStr);
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> getControlMeter(Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_CONTROL_METER;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), null
                , interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，获取管控表数据 return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseBody());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> getOwnerDetails(Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_OWNER_DETAILS;
        log.info("获取所有用户及电表(管理员权限)开始");
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), null
                , interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，获取所有用户及电表(管理员权限) return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        // 返回集合
        List<UserAndMeterResDTO> returnList = JSON.parseArray(JSON.parse(dataLog.getResponseBody()).toString(), UserAndMeterResDTO.class);
        // 查出已存在用户和设备
        List<EmProcessSensor> sensorList = emProcessSensorService.selectAll();
        List<EmProcessUser> userList = emProcessUserService.getAll();
        // 查询没有定时器的传感器，新增
        this.dealOldSensorJob();
        // 转map去比对
        // 返回网关数据map
        Map<String, String> returnSensorMap = returnList.stream().collect(Collectors.toMap(UserAndMeterResDTO::getMeterId, UserAndMeterResDTO::getMeterId, (value1, value2) -> value2));
        // 现有网关数据map
        Map<String, String> dbSensorMap = sensorList.stream().collect(Collectors.toMap(EmProcessSensor::getDeviceAddr, EmProcessSensor::getDeviceAddr, (value1, value2) -> value2));
        // 现有用户数据map
        Map<String, String> dbUserMap = userList.stream().collect(Collectors.toMap(EmProcessUser::getUserId, EmProcessUser::getUserId, (value1, value2) -> value2));

        // 取现有数据有的返回数据没有的
        List<EmProcessSensor> deleteSensors = sensorList.stream().filter(meter -> !returnSensorMap.containsKey(meter.getDeviceAddr()))
                .collect(Collectors.toList());
        // 取返回数据有的现有数据没有的
        List<UserAndMeterResDTO> newAddSensors = returnList.stream().filter(meter -> !dbSensorMap.containsKey(meter.getMeterId()))
                .collect(Collectors.toList());
        List<UserAndMeterResDTO> newAddUsers = returnList.stream().filter(meter -> !dbUserMap.containsKey(meter.getUserId()))
                .collect(Collectors.toList());
        // 用户去重
        Map<String, String> addUserMap = newAddUsers.stream().collect(Collectors.toMap(UserAndMeterResDTO::getUserId,
                UserAndMeterResDTO::getUserName, (value1, value2) -> value2));
        // 传感器去重
        Map<String, String> addSensorMap = newAddSensors.stream().collect(Collectors.toMap(UserAndMeterResDTO::getMeterId,
                UserAndMeterResDTO::getUserId, (value1, value2) -> value2));
        // 删除网关
        emProcessSensorService.removeByIds(deleteSensors);
        // 删除定时器
        List<String> sensorIds = deleteSensors.stream().map(EmProcessSensor::getDeviceAddr).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(sensorIds)) {
            this.deleteAllJob(sensorIds, null);
        }
        // 新增
        List<EmProcessUser> addUsers = new ArrayList<>();
        List<EmProcessSensor> addSensorList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(addUserMap)) {
            for (String key : addUserMap.keySet()) {
                // 用户
                EmProcessUser user = new EmProcessUser(key, addUserMap.get(key), AssignIdGenUtils.generate(new EmProcessUser()));
                addUsers.add(user);
            }
            emProcessUserService.saveBatch(addUsers);
        }
        if (ObjectUtil.isNotEmpty(addSensorMap)) {
            for (String key : addSensorMap.keySet()) {
                // 网关
                EmProcessSensor emProcessSensor = new EmProcessSensor(AcrelDataConstants.DeviceType.POWER, emProcessUserService.getOneByUserId(addSensorMap.get(key)).getId(), key, key);
                // 传感器新增时数据追溯开始时间
                emProcessSensor.setRecallStartTime(DateUtil.parseDateTime(dataConfig.getDataDate()));
                // 获取上次数据时间
                Date dataTime = StatTimeUtils.getPrevDataTime(new Date(), 60);
                emProcessSensor.setRecallEndTime(DateUtil.offsetMinute(dataTime, -emProcessSensor.getDataFreq()));
                emProcessSensor.setLatestComTime(dataTime);
                addSensorList.add(emProcessSensor);
                // 新增定时器
                // initAndAddJob(key, 60);
            }
            emProcessSensorService.saveBatch(addSensorList);
            // 往前追溯数据
            previousData(addSensorList);
        }
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseBody());
    }

    @Override
    public void dealOldSensorJob() {
        log.info("业务调整为园区级别定时任务，添加传感器维度的定时任务，已经终止");
        if (1 == 1) {
            return;
        }
        List<EmProcessSensor> emProcessSensorList = emProcessSensorService.getNotHaveJob();
        log.info("现有传感器没有定时器的个数-> {}", emProcessSensorList.size());
        if (CollUtil.isNotEmpty(emProcessSensorList)) {
            // 去重
            List<EmProcessSensor> dealList = emProcessSensorList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EmProcessSensor::getDeviceAddr))), ArrayList::new));
            log.info("恢复现有sensor定时器开始，emProcessSensorList-> {}", dealList);
            dealList.forEach(emProcessSensor -> {
                initAndAddJob(emProcessSensor.getDeviceAddr(), emProcessSensor.getDataFreq());
            });
            log.info("恢复现有sensor定时器结束");
        }
    }

    /**
     * 初始化定时器并新增
     *
     * @param deviceAddr
     */
    @Override
    public void initAndAddJob(String deviceAddr, Integer reportFreq) {
        List<SysQuartzJob> jobList = quartzJobService.list(new LambdaQueryWrapper<SysQuartzJob>()
                .eq(SysQuartzJob::getParameter, deviceAddr)
                .eq(SysQuartzJob::getJobClassName, QuartzJobEnum.GATEWAY_DATA_JOB.getClassName()));
        if (CollUtil.isNotEmpty(jobList)) {
            throw new JeecgBootException("已存在该定时器！");
        }
        String cron = CronUtil.getReportFreqCron(reportFreq);
        // 新增定时器
        SysQuartzJob sysQuartzJob = new SysQuartzJob();
        sysQuartzJob.setJobClassName(QuartzJobEnum.GATEWAY_DATA_JOB.getClassName());
        sysQuartzJob.setCronExpression(cron);
        sysQuartzJob.setParameter(deviceAddr);
        sysQuartzJob.setStatus(SysConstants.IsStatus.YES);
        quartzJobService.saveAndScheduleJob(sysQuartzJob);
    }

    /**
     * 循环所有网关 追溯之前的数据
     * 新增网关的时候调用，更新数据最新时间
     *
     * @param sensorList
     */
    public void previousData(List<EmProcessSensor> sensorList) {
        GetDataService bean = getBean(GetDataService.class);
        if (CollUtil.isNotEmpty(sensorList)) {
            sensorList.forEach(emProcessSensor -> bean.getWhenBefore(emProcessSensor, Boolean.FALSE));
        }
    }

    @Override
    public Date runHistoryData(String meterId, Date recallStartTime, Date recallEndTime, DataSourcesEnum dataSourcesEnum, Integer dataFreq, boolean isUpdateReCallDate, Integer type) {
        emProcessSensorService.addRunningCache(meterId);
        Date compareTime;
        if (dataSourcesEnum.equals(DataSourcesEnum.MOCK_DATA)) {
            compareTime = recallEndTime;
        } else {
            // 回溯结束时间加三个小时与当前时间对比
            Date beforeThreeHour = DateUtil.offsetHour(new Date(), -3);
            boolean before = recallEndTime.compareTo(beforeThreeHour) <= 0;
            // 回溯结束时间
            compareTime = before ? recallEndTime : beforeThreeHour;
        }
        while (recallStartTime.compareTo(compareTime) <= 0) {
            // 获取历史数据
            switch (dataSourcesEnum) {
                case ACREL:
                    if (AcrelDataConstants.DeviceType.WATER.equals(type)) {
                        this.getWaterHistoryData(meterId, recallStartTime);
                    } else {
                        this.getHistoryData(meterId, recallStartTime);
                    }
                    break;
                case FRONT_END_PROCESSOR:
                    log.info("数据类型为前置机！");
                    break;
                case MOCK_DATA:
                    // 模拟数据
                    this.getEnergyData2Demo(meterId, recallStartTime, null);
                    break;
                default:
                    break;
            }
            recallStartTime = DateUtil.offsetMinute(recallStartTime, dataFreq);
            // 如果是按天来回溯数据，不更新时间
            if (Boolean.TRUE.equals(isUpdateReCallDate)) {
                emProcessSensorService.updateSensor(meterId, recallStartTime, null, null, null);
            }
        }
        // 数据跑完后 清除running
        emProcessSensorService.removeRunningCache(meterId);
        // 返回时间加一
        return recallStartTime;
    }

    /**
     * 获取实时数据时间
     *
     * @param meterId         网关id
     * @param dataSourcesEnum 数据来源枚举
     * @param dataFreq        数据频率
     * @return 日期
     */
    private Date getRealDataTime(String meterId, DataSourcesEnum dataSourcesEnum, Integer dataFreq) {
        Date realDataTime = null;
        switch (dataSourcesEnum) {
            case ACREL:
                realDataTime = this.getAcrelRealData(meterId, Boolean.FALSE, dataFreq);
                break;
            case FRONT_END_PROCESSOR:
                log.info("数据类型为前置机！");
                break;
            case MOCK_DATA:
                // 获取当前时间的上次数据时间
                realDataTime = StatTimeUtils.getPrevDataTime(new Date(), dataFreq);
                break;
            default:
                break;
        }
        // 数据来源为ACREL，有可能没有数据返回
        if (Objects.isNull(realDataTime)) {
            realDataTime = StatTimeUtils.getPrevDataTime(new Date(), dataFreq);
        }
        return realDataTime;
    }

    @Async("asyncServiceExecutor")
    @Override
    public void getWhenBefore(EmProcessSensor emProcessSensor, boolean isRerunData) {
        long beginTime = System.currentTimeMillis();
        //log.info("meterId: {} 开始时间：{} ", emProcessSensor.getDeviceAddr(), DateUtil.date());
        // 存在redis，回溯数据跳过
        boolean existRunning = StringUtils.isNotBlank(emProcessSensorService.getRunningCache(emProcessSensor.getDeviceAddr()));
        if (Boolean.TRUE.equals(existRunning)) {
            log.warn("网关：{}正在回溯数据，该次获取数据跳过！", emProcessSensor.getDeviceAddr());
            return;
        }
        // 获取当前任务数据时间点
        Date currentDatadate = StatTimeUtils.getDataTime(new Date(), emProcessSensor.getDataFreq());
        DataSourcesEnum dataSourcesEnum = DataSourcesEnum.getEnumByCode(emProcessSensor.getDataSources());
        // 是否回溯完成，完成用当前最新数据时间覆盖三个，否则只改最近一次数据时间
        boolean isRecallEnd = true;
        // 是否需要回溯,三个时间相等不需要回溯
        boolean recallOne = emProcessSensor.getRecallStartTime().compareTo(emProcessSensor.getRecallEndTime()) == 0;
        boolean recallTwo = emProcessSensor.getRecallEndTime().compareTo(emProcessSensor.getLatestComTime()) == 0;
        if (Boolean.FALSE.equals(recallOne) || Boolean.FALSE.equals(recallTwo)) {
            emProcessSensor.setRecallStartTime(runHistoryData(emProcessSensor.getDeviceAddr(), emProcessSensor.getRecallStartTime(), emProcessSensor.getRecallEndTime(), dataSourcesEnum, emProcessSensor.getDataFreq(), true, emProcessSensor.getType()));
            // 再比较一次，用来判断回溯是否完成
            isRecallEnd = emProcessSensor.getRecallStartTime().compareTo(emProcessSensor.getRecallEndTime()) > 0;
        }
        // 获取网关数据最新时间
        Date realDataTime = this.getRealDataTime(emProcessSensor.getDeviceAddr(), dataSourcesEnum, emProcessSensor.getDataFreq());
        // 当前任务数据时间和最新任务数据时间差值，大于一个数据频率，再回溯两个时间范围的数据
        long diffrenceTask = getInterval(emProcessSensor.getLatestTaskTime(), currentDatadate);
        // 两个定时任务间隔时间超过一个数据频率则触发回溯
        if (diffrenceTask > emProcessSensor.getDataFreq()) {
            // 判断回溯完成，只需要回溯最近任务时间到最新一次数据减一个频率
            if (Boolean.TRUE.equals(recallOne)) {
                emProcessSensor.setRecallStartTime(emProcessSensor.getLatestTaskTime());
            }
            emProcessSensor.setRecallEndTime(DateUtil.offsetMinute(realDataTime, -emProcessSensor.getDataFreq()));
            if (emProcessSensor.getRecallEndTime().after(emProcessSensor.getRecallStartTime())) {
                isRecallEnd = false;
                emProcessSensorService.updateSensor(emProcessSensor.getDeviceAddr(), emProcessSensor.getRecallStartTime(), emProcessSensor.getRecallEndTime(), realDataTime, currentDatadate);
                emProcessSensor.setLatestTaskTime(currentDatadate);
                getWhenBefore(emProcessSensor, true);
            }
        }
        // 是否获取数据成功
        boolean isGetSuccess = false;
        // 获取实时数据，实时数据时间大于上一个实时数据时间
        if (Boolean.FALSE.equals(isRerunData) && realDataTime.compareTo(emProcessSensor.getLatestComTime()) > 0) {
            switch (dataSourcesEnum) {
                case ACREL:
                    this.getAcrelRealData(emProcessSensor.getDeviceAddr(), Boolean.TRUE, null);
                    break;
                case FRONT_END_PROCESSOR:
                    log.info("数据类型为前置机！");
                    break;
                case MOCK_DATA:
                    // 模拟数据 时间减一个小时
                    this.getEnergyData2Demo(emProcessSensor.getDeviceAddr(), realDataTime, null);
                    break;
                default:
                    break;
            }
            isGetSuccess = true;
        }
        // 获取数据成功修改，latestTaskTime为空不修改
        if (isGetSuccess || isRerunData) {
            updateDateTime(emProcessSensor.getDeviceAddr(), realDataTime, isRecallEnd, currentDatadate);
        } else {
            updateDateTime(emProcessSensor.getDeviceAddr(), null, isRecallEnd, currentDatadate);
        }
        long ms = System.currentTimeMillis() - beginTime;
        // 大于1秒才打印
        if (ms >= 1000) {
            log.info("meterId: {} 开始时间：{} 结束时间：{} ，耗时：{} 毫秒 {} 秒", emProcessSensor.getDeviceAddr(), DateUtil.formatDateTime(new Date(beginTime)), DateUtil.date(), ms, ms / 1000);
        }
    }

    /**
     * 修改网关最新数据时间
     *
     * @param meterId        网关id
     * @param latestComTime  最新一次数据时间
     * @param isRecallEnd    是否回溯完成
     * @param latestTaskTime 最近一次任务时间
     */
    private void updateDateTime(String meterId, Date latestComTime, boolean isRecallEnd, Date latestTaskTime) {
        if (isRecallEnd) {
            emProcessSensorService.updateSensor(meterId, latestComTime, latestComTime, latestComTime, latestTaskTime);
        } else {
            if (Objects.isNull(latestComTime)) {
                emProcessSensorService.updateSensor(meterId, null, null, null, latestTaskTime);
            } else {
                emProcessSensorService.updateSensor(meterId, null, null, latestComTime, latestTaskTime);
            }
        }
    }

    @Async("asyncServiceExecutor")
    @Override
    public void getWaterDataWhenBefore(EmProcessSensor emProcessSensor) {
        // 存在redis，回溯数据跳过
        String meterId = emProcessSensor.getDeviceAddr();
        boolean existRunning = StringUtils.isNotBlank(emProcessSensorService.getRunningCache(meterId));
        if (Boolean.TRUE.equals(existRunning)) {
            log.warn("网关：{}正在回溯数据，该次获取数据跳过！", meterId);
            return;
        }
        // 判断数据类型
        if (!DataSourcesEnum.ACREL.getCode().equals(emProcessSensor.getDataSources()) || !AcrelDataConstants.DeviceType.WATER.equals(emProcessSensor.getType())) {
            return;
        }
        emProcessSensorService.addRunningCache(meterId);
        // 获取数据结束时间
        Date endDateTime = new Date();
        Date latestComTime = emProcessSensor.getLatestComTime();
        while (latestComTime.compareTo(endDateTime) <= 0) {
            // 获取数据
            getWaterHistoryData(meterId, latestComTime);
            latestComTime = DateUtil.offsetMinute(latestComTime, emProcessSensor.getDataFreq());
            // 修改时间点
            emProcessSensorService.updateSensor(meterId, null, null, latestComTime, null);
        }
        // 数据跑完后 清除running
        emProcessSensorService.removeRunningCache(meterId);
        // 获取阀控信息
        Map<String, String> paramMap = new HashMap<>(1);
        paramMap.put(AcrelDataConstants.STR, meterId);
        try {
            Result<String> waterMeterInfo = this.getWaterMeterInfo(paramMap, false);
            JSONObject responseBody = JSON.parseObject(waterMeterInfo.getResult());
            if (Objects.isNull(responseBody)) {
                log.warn("水表信息JSON解析失败，responseBody为空！");
                return;
            }
            responseBody.put(AcrelDataConstants.CREATE_TIME, DateUtil.formatDateTime(DateUtil.parse(responseBody.getString(AcrelDataConstants.CREATE_TIME))));
            processMeterSend(responseBody.toJSONString(), DataInterfaceEnum.GET_METER_WATER_INFO.getDi(), Boolean.FALSE);
        } catch (Exception e) {
            log.error("获取水表信息异常！异常信息: {}", ThrowableUtils.getStackTraceByPackage(e));
        }
    }

    /**
     * 获取两个时间间隔分钟
     *
     * @param startDate
     * @param endDate
     * @return
     */
    private long getInterval(Date startDate, Date endDate) {
        long diff = endDate.getTime() - startDate.getTime();
        TimeUnit time = TimeUnit.MINUTES;
        return time.convert(diff, TimeUnit.MILLISECONDS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> addOneSensor(EmProcessSensor emProcessSensor) {
        emProcessSensorService.addProcessSensor(emProcessSensor);
        // 新增定时器
        // initAndAddJob(emProcessSensor.getDeviceAddr(), emProcessSensor.getDataFreq());
        // 根据安装时间回溯数据
        previousData(Collections.singletonList(emProcessSensor));
        return Result.ok(SysTips.SUCCESS_MSG);
    }

    @Override
    public Result<String> deleteAllJob(List<String> parameters, String jobClassName) {
        if (StringUtils.isBlank(jobClassName)) {
            jobClassName = QuartzJobEnum.GATEWAY_DATA_JOB.getClassName();
        }
        LambdaQueryWrapper<SysQuartzJob> queryWrapper = new LambdaQueryWrapper<SysQuartzJob>()
                .eq(SysQuartzJob::getDeleted, 0).eq(SysQuartzJob::getJobClassName, jobClassName);
        if (CollUtil.isNotEmpty(parameters)) {
            queryWrapper.in(SysQuartzJob::getParameter, parameters);
        }
        List<SysQuartzJob> jobList = quartzJobService.list(queryWrapper);
        if (CollUtil.isNotEmpty(jobList)) {
            jobList.forEach(job -> {
                quartzJobService.deleteAndStopJob(job);
            });
        }
        return Result.ok();
    }

    @Async("asyncServiceExecutor")
    @Override
    public void getRealDataAfterMeterControl(String deviceAddr) {
        // 只跑一次实时数据
        EmProcessSensor emProcessSensor = emProcessSensorService.getOneByDeviceAddr(deviceAddr);
        getAcrelRealData(emProcessSensor.getDeviceAddr(), Boolean.TRUE, null);
    }

    @Override
    public void getRealData(String deviceAddr) {
        EmProcessSensor emProcessSensor = emProcessSensorService.getOneByDeviceAddr(deviceAddr);
        if (Objects.isNull(emProcessSensor)) {
            log.error("获取实时数据查询不到网关信息！");
            return;
        }
        switch (DataSourcesEnum.getEnumByCode(emProcessSensor.getDataSources())) {
            case MOCK_DATA:
                getEnergyData2Demo(emProcessSensor.getDeviceAddr(), StatTimeUtils.getPrevDataTime(new Date(), emProcessSensor.getDataFreq()), null);
                break;
            case ACREL:
                getAcrelRealData(emProcessSensor.getDeviceAddr(), Boolean.TRUE, null);
                break;
            default:
                break;
        }
    }

    @Override
    public void triggerJob(String meterId) {
        List<EmProcessSensor> emProcessSensorList = new ArrayList<>();
        if (StringUtils.isBlank(meterId)) {
            emProcessSensorList = emProcessSensorService.selectAll();
        } else {
            emProcessSensorList.add(emProcessSensorService.getOneByDeviceAddr(meterId));
        }
        GetDataService bean = getBean(GetDataService.class);
        if (CollUtil.isNotEmpty(emProcessSensorList)) {
            emProcessSensorList.parallelStream().forEach(emProcessSensor -> {
                // 获取实时数据,检索是否需要回溯数据
                bean.getWhenBefore(emProcessSensor, Boolean.FALSE);
            });
        }
    }

    /**
     * 获取历史数据
     *
     * @param meterId
     * @param date
     */
    private void getHistoryData(String meterId, Date date) {
        Map<String, String> map = new HashMap<>(4);
        map.put(AcrelDataConstants.METER_ID, meterId);
        map.put("StartDate", DateUtil.formatDate(date));
        map.put("EndDate", DateUtil.formatDate(DateUtil.offsetDay(date, 1)));
        map.put("dataTime", DateUtil.formatDateTime(date));
        try {
            getEnergyData2(map, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 调用接口获取水表历史数据
     *
     * @param meterId 网关编号
     * @param date    数据时间点
     */
    private void getWaterHistoryData(String meterId, Date date) {
        Map<String, String> map = new HashMap<>(4);
        map.put("MeterID", meterId);
        // 是否小于三点
        boolean lessThird = DateUtil.hour(date, true) <= 2;
        if (lessThird) {
            map.put("StartDate", DateUtil.formatDate(DateUtil.offsetDay(date, -1)));
            map.put("EndDate", DateUtil.formatDate(date));
        } else {
            map.put("StartDate", DateUtil.formatDate(date));
            map.put("EndDate", DateUtil.formatDate(DateUtil.offsetDay(date, 1)));
        }
        // 获取数据时间减3
        map.put("dataTime", DateUtil.formatDateTime(DateUtil.offsetHour(date, -3)));
        try {
            getEnergyData2(map, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取实时数据
     *
     * @param meterId  网关id
     * @param isSend   是否推送数据
     * @param dataFreq 数据频率
     */
    private Date getAcrelRealData(String meterId, boolean isSend, Integer dataFreq) {
        Map<String, String> map = new HashMap<>(1);
        map.put(AcrelDataConstants.METER_ID, meterId);
        String responseBody = null;
        try {
            responseBody = getCollect(map, false).getResult();
        } catch (Exception e) {
            e.printStackTrace();
        }
        Date date = null;
        if (StringUtils.isBlank(responseBody) || StrPool.SQ_BRACKET.equals(responseBody)) {
            log.error("meterId:{},获取实时数据返回值为空！", meterId);
        } else {
            // 日期格式转换
            JSONObject jsonObject = JSON.parseObject(responseBody, JSONObject.class);
            date = DateUtil.truncate(DateUtil.parse(jsonObject.getString(AcrelDataConstants.CREATE_TIME)), DateField.SECOND);
            // 原有数据时间不动，直接推送
            jsonObject.put(AcrelDataConstants.CREATE_TIME, DateUtil.formatDateTime(date));
            if (Boolean.TRUE.equals(isSend)) {
                processMeterSend(String.valueOf(jsonObject), DataInterfaceEnum.GET_COLLECT.getDi(), Boolean.FALSE);
            } else {
                // 按数据频率处理数据时间
                date = StatTimeUtils.getDataTime(date, dataFreq);
            }
        }
        return date;
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> getEnergyDay(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_ENERGY_DAY;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，获取某天用电数据(管理员权限) return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        processMeterSend(dataLog.getResponseBody(), interfaceEnum.getDi(), false);
        return Result.ok(dataLog.getResponseBody());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> getAllOwners(Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_ALL_OWNERS;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), null,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，获取所有用户信息 return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseBody());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 1, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> waterMeterControl(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.WATER_METER_CONTROL;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，水表分合闸控制（管理员权限）return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(dataLog.getResponseMsg());
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 1, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public Result<String> getWaterMeterInfo(Map<String, String> map, Boolean isReTry) throws Exception {
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_METER_WATER_INFO;
        EmProcessDataLog dataLog = requestDataUtil.httpJsonObject(interfaceEnum.getCode(), map,
                interfaceEnum.getFunction(), false, interfaceEnum.getType());
        log.info("{}，获取用户名下某个水表信息 return -> {}", interfaceEnum.getFunction(), dataLog.toString());
        addLog(dataLog, isReTry);
        return Result.ok(SysTips.SUCCESS_MSG, dataLog.getResponseBody());
    }

    @Override
    public Result<String> reTryByLogId(String id) {
        EmProcessDataLog dataLog = emProcessDataLogService.getById(id);
        if (ObjectUtil.isEmpty(dataLog)) {
            return Result.error("重试失败，系统日志不存在！");
        }
        if (Boolean.TRUE.equals(dataLog.getSuccess())) {
            return Result.error("该日志调用成功，无需重试！");
        }
        Method method;
        Result<String> result;
        try {
            // 从ApplicationContext中取出已创建好的的对象
            ApplicationContext applicationContext = SpringContextUtils.getApplicationContext();
            Class<?> clazz = getClass();
            if (StringUtils.isNotBlank(dataLog.getRequestParam())) {
                method = clazz.getMethod(toLowerCaseFirstOne(dataLog.getInterfaceUrl()), Map.class, Boolean.class);
                result = (Result<String>) method.invoke(applicationContext.getBean(clazz), JSON.parseObject(dataLog.getRequestParam(), Map.class), true);
            } else {
                method = clazz.getMethod(toLowerCaseFirstOne(dataLog.getInterfaceUrl()), Boolean.class);
                result = (Result<String>) method.invoke(applicationContext.getBean(clazz), false);
            }
            dataLog.setAlreadyRetry(true);
        } catch (Exception e) {
            e.printStackTrace();
            dataLog.setRetrySuccess(false);
            emProcessDataLogService.update(dataLog);
            return Result.error("重试失败！系统异常，请联系管理员");
        }
        if (!result.isSuccess()) {
            dataLog.setRetrySuccess(false);
            emProcessDataLogService.update(dataLog);
            return Result.error("重试失败！返回-> " + result.getMsg());
        } else {
            dataLog.setRetrySuccess(true);
            emProcessDataLogService.update(dataLog);
            return Result.ok();
        }
    }

    @Async("asyncServiceExecutor")
    @Override
    public void rerunMockData(EmSensorRerunDataDTO emSensorRerunDataDTO) {
        String deviceAddr = StringUtils.substringBeforeLast(emSensorRerunDataDTO.getSensorCode(), StrPool.UNDER_LINE);
        EmProcessSensor emProcessSensor = emProcessSensorService.getOneByDeviceAddr(deviceAddr);
        if (Objects.isNull(emProcessSensor)) {
            log.error("查询不到网关信息，回溯失败！");
        }
        MockDataDTO mockDataDTO = new MockDataDTO();
        EmSensorOriginData emSensorOriginData = emSensorOriginDataService.getFirstOne(emProcessSensor.getDeviceAddr(), "P0053");
        if (Objects.isNull(emSensorOriginData)) {
            throw new JeecgBootException("网关数据异常，请联系管理员！");
        }
        BigDecimal reading = RandomUtil.randomBigDecimal(emProcessSensor.getInitDataVal(), new BigDecimal(emSensorOriginData.getValue()));
        mockDataDTO.setReading(reading);
        mockDataDTO.setLastReading(emProcessSensor.getInitDataVal());
        mockDataDTO.setChangeMockDataCache(Boolean.FALSE);
        // 结束时间大于当前时间取当前时间
        Date endDate = DateUtil.endOfDay(emSensorRerunDataDTO.getEndDate());
        if (new Date().before(endDate)) {
            throw new JeecgBootException("网关修改安装时间，回溯结束大于当前时间，跳过本次回溯！");
        }
        while (emSensorRerunDataDTO.getInstallTime().compareTo(endDate) <= 0) {
            this.getEnergyData2Demo(emProcessSensor.getDeviceAddr(), emSensorRerunDataDTO.getInstallTime(), mockDataDTO);
            emSensorRerunDataDTO.setInstallTime(DateUtil.offsetMinute(emSensorRerunDataDTO.getInstallTime(), emProcessSensor.getDataFreq()));
            mockDataDTO.setLastReading(reading);
        }
        // 修改初始读数
        EmProcessSensor updateEmProcessSensor = new EmProcessSensor();
        updateEmProcessSensor.setId(emProcessSensor.getId());
        updateEmProcessSensor.setInitDataVal(reading);
        emProcessSensorService.updateById(updateEmProcessSensor);
        // 回溯完成之后重报之前的第一条数据
        mockDataDTO.setReading(new BigDecimal(emSensorOriginData.getValue()));
        this.getEnergyData2Demo(emProcessSensor.getDeviceAddr(), DateUtil.parseDateTime(emSensorOriginData.getDataTime()), mockDataDTO);
    }

    @Override
    public Result<String> getEnergyData2Demo(String meterId, Date dataTime, MockDataDTO mockDataDTO) {
        if (Objects.isNull(mockDataDTO)) {
            mockDataDTO = new MockDataDTO();
        }
        // 时间在传入之前处理
        String dateStr = DateUtil.formatDateTime(dataTime);
        DataInterfaceEnum interfaceEnum = DataInterfaceEnum.GET_ENERGY_DATA2;
        String deviceLastPv = redisUtils.strGet(CharSequenceUtil.format(RedisKeyConstants.EmSensor.SENSOR_DEMO_DATA, meterId));
        BigDecimal lastReading;
        BigDecimal reading;
        Integer shiftData = null;
        if (Objects.nonNull(mockDataDTO.getLastReading())) {
            lastReading = mockDataDTO.getLastReading();
        } else {
            lastReading = new BigDecimal(deviceLastPv);
        }
        if (Objects.nonNull(mockDataDTO.getReading())) {
            reading = mockDataDTO.getReading();
        } else {
            // 加一个随机数
            reading = lastReading.add(RandomUtil.randomBigDecimal(BigDecimal.ZERO, BigDecimal.TEN));
        }
        if (Objects.isNull(mockDataDTO.getShiftData())) {
            shiftData = RandomUtil.randomInt(0, 3);
        }
        String responseBody = "{'PVG':0.0,'LastPVF':0.0,'PVF':0.0,'CurrentPVG':0.0,'LastPV':" + lastReading +
                ",'LastPVG':0.0,'CurrentPVJ':0.0,'LastPVJ':0.0,'PVJ':0.0,'PV':" + reading.subtract(lastReading).doubleValue() + ",'CreateTime':'" + dateStr +
                "','PVP':0.02,'CurrentPVF':0.0,'MeterID':'" + meterId + "','CurrentPVP':18.949999999999999," +
                "'CurrentPV':" + reading + ",'PowerRemain':100037.95,'LastPVP':18.93,'shiftMonitor':" + shiftData + "," +
                "'Ua':" + RandomUtil.randomBigDecimal(new BigDecimal("200"), new BigDecimal("300")).setScale(2, RoundingMode.UP) + "," +
                "'Ub':" + RandomUtil.randomBigDecimal(new BigDecimal("200"), new BigDecimal("300")).setScale(2, RoundingMode.UP) + "" +
                ",'Uc':" + RandomUtil.randomBigDecimal(new BigDecimal("200"), new BigDecimal("300")).setScale(2, RoundingMode.UP) + "," +
                "'Ia':" + RandomUtil.randomBigDecimal(new BigDecimal("200"), new BigDecimal("300")).setScale(2, RoundingMode.UP) + "" +
                ",'Ib':" + RandomUtil.randomBigDecimal(new BigDecimal("200"), new BigDecimal("300")).setScale(2, RoundingMode.UP) + "" +
                ",'Ic':" + RandomUtil.randomBigDecimal(new BigDecimal("200"), new BigDecimal("300")).setScale(2, RoundingMode.UP) + "}";
        EmProcessDataLog emProcessDataLog = new EmProcessDataLog();
        // 初始化后新增
        initEmProcessDataLog(emProcessDataLog, interfaceEnum, meterId, responseBody, dateStr);
        addLog(emProcessDataLog, false);
        // 发送数据
        processMeterSend(JSON.toJSONString(responseBody), interfaceEnum.getDi(), false);
        if (Boolean.TRUE.equals(mockDataDTO.getChangeMockDataCache())) {
            // 更新累加的mock数据
            redisUtils.strSet(CharSequenceUtil.format(RedisKeyConstants.EmSensor.SENSOR_DEMO_DATA, meterId), reading.toString());
        }
        return Result.ok();
    }

    /**
     * 初始化数据记录
     *
     * @param emProcessDataLog
     * @param interfaceEnum
     * @param meterId
     * @param responseBody
     * @param dateStr
     */
    private void initEmProcessDataLog(EmProcessDataLog emProcessDataLog, DataInterfaceEnum interfaceEnum, String meterId, String responseBody, String dateStr) {
        emProcessDataLog.setType(interfaceEnum.getCode());
        emProcessDataLog.setInterfaceUrl(interfaceEnum.getFunction());
        emProcessDataLog.setRequestType(interfaceEnum.getType());
        emProcessDataLog.setRequestParam(meterId.concat("_").concat(dateStr));
        emProcessDataLog.setResponseMsg("模拟数据成功");
        emProcessDataLog.setResponseBody(responseBody);
        emProcessDataLog.setSuccess(true);
        emProcessDataLog.setDeviceAddr(meterId);
    }

    /**
     * 新增日志
     *
     * @param dataLog
     * @param isReTry
     */
    private void addLog(EmProcessDataLog dataLog, Boolean isReTry) {
        if (Boolean.FALSE.equals(isReTry)) {
            dataLog.setDataDate(DateUtil.formatDate(new Date()));
            emProcessDataLogService.insert(dataLog);
        }
    }

    /**
     * 没map参数回调
     *
     * @param e
     * @param isReTry
     * @return
     */
    @Recover
    public Result<String> notHaveParam(Exception e, Boolean isReTry) {
        return resultError(e.getMessage(), isReTry);
    }

    /**
     * 有map参数回调
     *
     * @param e
     * @param isReTry
     * @return
     */
    @Recover
    public Result<String> haveParam(Exception e, Map<String, String> map, Boolean isReTry) {
        return resultError(e.getMessage(), isReTry);
    }

    /**
     * 新增错误日志 返回异常信息
     *
     * @param msg 异常信息
     * @return
     */
    private Result<String> resultError(String msg, Boolean isReTry) {
        // msg为调用接口dataLog
        log.error("调用接口异常：{}", msg);
        EmProcessDataLog dataLog;
        try {
            dataLog = JSON.parseObject(msg, EmProcessDataLog.class);
        } catch (Exception e) {
            log.error("em-cloud-data模块系统异常！" + e.getMessage());
            return Result.error("em-cloud-data模块系统异常！");
        }
        addLog(dataLog, isReTry);
        JSONObject responseBody = JSON.parseObject(dataLog.getResponseBody());
        return Result.error(responseBody.getString("msg"));
    }

    /**
     * 处理设备数据 生成json 塞mq
     *
     * @param returnData 数据集合
     * @param di
     * @param isList     是否是集合
     */
    private void processMeterSend(String returnData, String di, boolean isList) {
        if (StringUtils.isBlank(returnData) || StrPool.SQ_BRACKET.equals(returnData)) {
            return;
        }
        String taskStr = redisUtils.strGet(CharSequenceUtil.format(RedisKeyConstants.TaskDi.MQ_TASK_DI_KEY, di));
        TaskDTO taskDTO = JSON.parseObject(taskStr, TaskDTO.class);
        if (isList) {
            List<JSONObject> dataList = JSON.parseArray(returnData, JSONObject.class);
            for (JSONObject meter : dataList) {
                send(taskDTO, meter);
            }
        } else {
            // 模拟数据需要处理转义字符
            JSONObject meter = JSON.parseObject(JSON.parse(returnData).toString(), JSONObject.class);
            send(taskDTO, meter);
        }
    }

    /**
     * 发送到mq
     *
     * @param taskDTO
     * @param obj
     */
    private void send(TaskDTO taskDTO, JSONObject obj) {
        if (ObjectUtil.isNotEmpty(taskDTO)) {
            EmGatewayData gatewayData = new EmGatewayData();
            gatewayData.setTaskDi(taskDTO.getTaskDi());
            gatewayData.setTaskName(taskDTO.getTaskName());
            String meterId = obj.getString(AcrelDataConstants.METER_ID);
            gatewayData.setDeviceAddr(meterId);
            gatewayData.setJcdId(meterId);
            gatewayData.setSendTime(DateUtil.formatDateTime(new Date()));
            List<EmGatewayDataContentDTO> contentDtoList = new ArrayList<>();
            for (TaskDiDTO diDTO : taskDTO.getDiList()) {
                EmGatewayDataContentDTO contentDTO = new EmGatewayDataContentDTO();
                contentDTO.setDataTime(JSON.parseObject(obj.toString()).getString(AcrelDataConstants.CREATE_TIME));
                contentDTO.setDi(diDTO.getDi());
                contentDTO.setName(diDTO.getName());
                contentDTO.setPn(diDTO.getPn());
                String value = JSON.parseObject(obj.toString()).getString(diDTO.getCode());
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                contentDTO.setValue(value);
                contentDtoList.add(contentDTO);
            }
            gatewayData.setDataContent(JSON.toJSONString(contentDtoList));
            // 持久化网关数据和传感器原始数据
            List<EmSensorOriginData> sensorOriginDataList = emGatewayDataService.insert(gatewayData);
            if (CollUtil.isNotEmpty(sensorOriginDataList)) {
                log.info("发送传感器数据到MQ → {}", toLogMsg(gatewayData, new String[]{"createBy", "deleted", "updateBy", "updateTime", "createTime", "taskName", "dataDate", "jcdId"}));
                rabbitMqClient.sendMessage(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.DIRECT_EXCHANGE, RabbitMqConstants.SENSOR_DATA_QUEUE, JSON.toJSONString(sensorOriginDataList));
            }
        } else {
            log.error("数据转换对象为空，请联系管理员！");
        }
    }

    @Async("asyncServiceExecutor")
    @Override
    public void processData(String sysParkId, String meterId, Integer freq) {
        List<EmProcessSensor> emProcessSensorList;
        if (StringUtils.isNotBlank(meterId)) {
            emProcessSensorList = new ArrayList<>();
            EmProcessSensor ps = emProcessSensorService.getOneByDeviceAddr(meterId);
            if (Objects.isNull(ps)) {
                log.info("条件传感器code：{}，查询不到任何数据，网关处理终止", meterId);
                return;
            }
            emProcessSensorList.add(ps);
        } else if (StringUtils.isNotBlank(sysParkId)) {
            emProcessSensorList = emProcessSensorService.getBySysParkId(sysParkId, freq);
        } else {
            emProcessSensorList = emProcessSensorService.selectAll();
        }
        if (CollectionUtils.isEmpty(emProcessSensorList)) {
            log.info("条件园区：{}#{} 或 传感器：{}，查询不到任何数据，网关处理终止", sysParkId, freq, meterId);
            return;
        }
        log.info("条件园区：{}#{} 或 传感器：{}，待处理网关：{}", sysParkId, freq, meterId, emProcessSensorList.size());
        GetDataService bean = getBean(GetDataService.class);
        // 统一处理所有传感器数据
        Predicate<EmProcessSensor> acrelWaterPredicate = emProcessSensor ->
                DataSourcesEnum.ACREL.getCode().equals(emProcessSensor.getDataSources())
                        && AcrelDataConstants.DeviceType.WATER.equals(emProcessSensor.getType());
        emProcessSensorList.parallelStream().forEach(emProcessSensor -> {
            if (acrelWaterPredicate.test(emProcessSensor)) {
                // 安科瑞水处理
                bean.getWaterDataWhenBefore(emProcessSensor);
            } else {
                // 其他类型处理,获取实时数据,检索是否需要回溯数据
                bean.getWhenBefore(emProcessSensor, Boolean.FALSE);
            }
        });
    }

    @Override
    public void initSysParkJob(String sysParkId, Boolean checkPark) {
        List<Integer> freqList = new ArrayList<>(5);
        freqList.addAll(Arrays.asList(5, 10, 15, 30, 60));
        List<String> sysParkIds = new ArrayList<>();
        Map<String, Integer> sysParkParamMap = new LinkedHashMap<>();
        if (StringUtils.isNotBlank(sysParkId)) {
            if (Boolean.TRUE.equals(checkPark)) {
                Result<SysParkApiDTO> result = emWebApi.getSysParkById(sysParkId);
                if (Boolean.FALSE.equals(result.isSuccess()) || Objects.isNull(result.getResult())) {
                    log.info("根据id: {} 获取园区失败或者不存在，无法初始化，原因：{}", result.getMsg());
                    throw new JeecgBootException("获取园区失败或者不存在，无法初始化");
                }
            }
            sysParkIds.add(sysParkId);
        } else {
            // 获取所有园区
            Result<List<String>> result = emWebApi.getAllSysParkIds();
            if (!Boolean.TRUE.equals(result.isSuccess())) {
                log.info("获取所有园区ids失败，无法初始化，原因：{}", result.getMsg());
                throw new JeecgBootException("获取所有园区ids失败，无法初始化");
            }
            sysParkIds = result.getResult();
        }
        if (CollectionUtils.isEmpty(sysParkIds)) {
            log.info("暂时无园区，无需初始化");
            return;
        }
        sysParkIds.forEach(i -> {
            freqList.forEach(r -> {
                sysParkParamMap.put(String.format("%s#%s", i, r), r);
            });
        });
        String jobName = QuartzJobEnum.GATEWAY_DATA_JOB.getClassName();
        sysParkParamMap.forEach((p, r) -> {
            List<SysQuartzJob> jobList = quartzJobService.list(new LambdaQueryWrapper<SysQuartzJob>()
                    .eq(SysQuartzJob::getParameter, p)
                    .eq(SysQuartzJob::getJobClassName, jobName));
            if (CollUtil.isNotEmpty(jobList)) {
                //throw new JeecgBootException("已存在该定时器！");
                log.info("园区参数：{} dataGateWay处理任务已存在，无需重新初始化", p);
                return;
            }
            String cron = CronUtil.getReportFreqCron(r);
            // 新增定时器
            SysQuartzJob sysQuartzJob = new SysQuartzJob();
            sysQuartzJob.setJobClassName(jobName);
            sysQuartzJob.setCronExpression(cron);
            sysQuartzJob.setParameter(p);
            sysQuartzJob.setStatus(SysConstants.IsStatus.YES);
            quartzJobService.saveAndScheduleJob(sysQuartzJob);
            log.info("新增园区：{} 频率：{} 定时任务完成", p, r);
        });
    }

    @Override
    public void delSensorJobAndAddParkJob() {
        // 补充园区id
        emProcessSensorService.repairSysParkId(null);
        // 删除定时任务
        List<SysQuartzJob> jobList = quartzJobService.list(new LambdaQueryWrapper<SysQuartzJob>().eq(SysQuartzJob::getJobClassName, QuartzJobEnum.GATEWAY_DATA_JOB.getClassName()));
        if (CollUtil.isNotEmpty(jobList)) {
            for (SysQuartzJob job : jobList) {
                try {
                    quartzJobService.deleteAndStopJob(job);
                    log.info("定时任务删除成功：{} {}", job.getId(), job.getParameter());
                } catch (Exception e) {
                    log.error("删除传感器网关: {} 定时任务异常：{}", job.getId(), e.getMessage(), e);
                }
            }
        }
        // 初始化园区定时任务
        initSysParkJob(null, Boolean.FALSE);
    }

    @Override
    public void sendCollectMessage(JSONObject jsonObject, String di) {
        processMeterSend(String.valueOf(jsonObject), di, Boolean.FALSE);
    }

    /**
     * 格式化对象信息，并过滤不需打印的字段
     *
     * @param obj
     * @param params
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023年09月15日 15:09:54
     */
    private String toLogMsg(Object obj, String[] params) {
        if (Objects.isNull(params) || params.length < 1) {
            return JSON.toJSONString(obj);
        }
        SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
        Arrays.stream(params).forEach(p -> {
            filter.getExcludes().add(p);
        });
        SerializeFilter[] filters = {filter};
        // return JSON.toJSONString(obj, filters, SerializerFeature.WriteMapNullValue);
        return JSON.toJSONString(obj, filters);
    }
}
