package org.jeecg.modules.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.data.entity.EmProcessSensor;

import java.util.List;

/**
 * 能源监测-数据处理-设备Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Mapper
public interface EmProcessSensorMapper extends BaseMapper<EmProcessSensor> {
    /**
     * 获取没有定时器的集合
     *
     * @return
     */
    List<EmProcessSensor> selectListNotHaveJob();

    /**
     * 获取回溯数据结束时间不等于最近一次数据时间的集合
     *
     * @return
     */
    List<EmProcessSensor> selectlistByLatestComTime();

    /**
     * 根据传感器code查询，code为空查所有
     * <AUTHOR>
     * @date   2023年09月14日 12:09:29
     * @param  sensorCodes 可空，为空查所有
     * @return java.util.List<org.jeecg.modules.data.entity.EmProcessSensor>
     */
    List<EmProcessSensor> listBySensorCodes(@Param("sensorCodes") List<String> sensorCodes);
    
}