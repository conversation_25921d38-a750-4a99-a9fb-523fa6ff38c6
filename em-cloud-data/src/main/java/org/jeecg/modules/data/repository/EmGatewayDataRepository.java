package org.jeecg.modules.data.repository;

import org.jeecg.modules.data.entity.EmGatewayData;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

/**
 * 能源监测-网关数据
 *
 * <AUTHOR>
 * @date 2022-01-20 12:46
 */
public interface EmGatewayDataRepository extends MongoRepository<EmGatewayData, String> {
    /**
     * 获取数据列表
     *
     * @param deviceAddr
     * @param dataDate
     * @return
     */
    List<EmGatewayData> findByDeviceAddrAndDataDate(String deviceAddr, String dataDate);
}
