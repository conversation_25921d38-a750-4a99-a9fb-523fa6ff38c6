<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.data.mapper.EmProcessTaskMapper">
    <resultMap id="BaseResultMap" type="org.jeecg.modules.data.entity.EmProcessTask">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="task_di" jdbcType="VARCHAR" property="taskDi"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">id, task_di, task_name, is_deleted, create_by, create_time, update_by, update_time</sql>

    <!-- 表名称 -->
    <sql id="tname">em_process_task ept</sql>

    <resultMap id="getALLAndDetailMap" type="org.jeecg.modules.data.vo.TaskDTO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="task_di" jdbcType="VARCHAR" property="taskDi"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <collection property="diList"
                    ofType="org.jeecg.modules.data.vo.TaskDiDTO"
                    select="org.jeecg.modules.data.mapper.EmProcessTaskDiMapper.selectByMainId"
                    column="id"/>
    </resultMap>

    <select id="selectAllUpdateRedis" resultMap="getALLAndDetailMap">
        SELECT
            ept.id,
            ept.task_di,
            ept.task_name
        FROM <include refid="tname"/>
        WHERE ept.is_deleted = 0
    </select>
</mapper>