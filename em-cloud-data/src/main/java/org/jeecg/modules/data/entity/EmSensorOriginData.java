package org.jeecg.modules.data.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecg.modules.mongo.entity.SysMongoBaseEntity;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 能源监测-传感器原始数据
 *
 * <AUTHOR>
 * @date 2022/1/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Document(value = "em_sensor_origin_data")
@ApiModel(value = "em_sensor_origin_data对象", description = "能源监测-传感器数据")
@CompoundIndexes({
        @CompoundIndex(useGeneratedName = true, unique = true, def = "{'data_time':1, 'device_addr':1, 'di':1, 'pn':1}", background = true),
        @CompoundIndex(useGeneratedName = true, def = "{'data_date':1, 'device_addr':1, 'di':1, 'pn':1}", background = true)
})
public class EmSensorOriginData extends SysMongoBaseEntity {
    private static final long serialVersionUID = 3456313543669071417L;
    /**
     * 网关数据ID
     */
    @Field("gateway_data_id")
    private String gatewayDataId;
    /**
     * 终端地址
     */
    @Field("device_addr")
    private String deviceAddr;
    /**
     * 记录日期，yyyy-MM-dd
     */
    @Field("data_date")
    private String dataDate;
    /**
     * 原始数据时间
     */
    @Field("data_time")
    private String dataTime;
    /**
     * 指标编号
     */
    private String di;
    /**
     * 指标名称
     */
    private String name;
    /**
     * 指标数据
     */
    private String value;
    /**
     * 传感器测量点号
     */
    private Integer pn;
}
