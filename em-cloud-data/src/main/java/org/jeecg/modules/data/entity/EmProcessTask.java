package org.jeecg.modules.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.jeecg.common.system.base.entity.SysBaseEntity;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 能源监测-数据处理-任务
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("em_process_task")
@ApiModel(value = "em_process_task对象", description = "能源监测-数据处理-任务")
@EqualsAndHashCode(callSuper = true)
public class EmProcessTask extends SysBaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 任务标识
     */
    @Excel(name = "任务标识", width = 15)
    @NotBlank(message = "任务标识不能为空！")
    @ApiModelProperty(value = "任务标识")
    private String taskDi;
    /**
     * 任务名称
     */
    @Excel(name = "任务名称", width = 15)
    @NotBlank(message = "任务名称不能为空！")
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    /**
     * 是否删除：0-否，1-是
     */
    @Excel(name = "是否删除：0-否，1-是", width = 15)
    @NotNull(message = "是否删除：0-否，1-是不能为空！")
    @ApiModelProperty(value = "是否删除：0-否，1-是")
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;
}