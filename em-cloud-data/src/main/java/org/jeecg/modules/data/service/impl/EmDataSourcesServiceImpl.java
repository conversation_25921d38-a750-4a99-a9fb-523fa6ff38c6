package org.jeecg.modules.data.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.config.mqtoken.UserTokenContext;
import org.jeecg.common.constant.*;
import org.jeecg.common.constant.enums.DataSourcesEnum;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.RedisUtils;
import org.jeecg.common.util.StatTimeUtils;
import org.jeecg.common.util.ThrowableUtils;
import org.jeecg.common.util.TokenUtils;
import org.jeecg.modules.data.config.EmPrefixProperties;
import org.jeecg.modules.data.constant.AcrelDataConstants;
import org.jeecg.modules.data.constant.enums.DataInterfaceEnum;
import org.jeecg.modules.data.dto.EmProcessSensorDTO;
import org.jeecg.modules.data.dto.EmSensorRerunDataDTO;
import org.jeecg.modules.data.dto.MeterControlReqDTO;
import org.jeecg.modules.data.entity.EmProcessSensor;
import org.jeecg.modules.data.entity.EmSensor;
import org.jeecg.modules.data.service.*;
import org.jeecg.modules.data.vo.EmMeterInfoAfterControlDTO;
import org.jeecg.modules.data.vo.EmSensorCacheValidateDTO;
import org.jeecg.modules.web.api.IEmWebApi;
import org.jeecg.modules.web.dto.EmSensorApiDTO;
import org.jeecg.modules.web.dto.EmValveControlStatusDTO;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 能源监测- 处理不同数据平台走向
 *
 * <AUTHOR>
 * @date 2022-11-2
 */
@Slf4j
@Service
public class EmDataSourcesServiceImpl implements EmDataSourcesService {
    @Lazy
    @Autowired
    private IEmWebApi emWebApi;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Lazy
    @Autowired
    private GetDataService getDataService;
    @Autowired
    private EmPrefixProperties emPrefixProperties;
    @Autowired
    private EmDataProfileService emDataProfileService;
    @Autowired
    private EmProcessSensorService emProcessSensorService;
    @Autowired
    private EmSensorOriginDataService emSensorOriginDataService;

    @Override
    public void addSensor(List<EmProcessSensorDTO> emProcessSensorList) {
        emProcessSensorList.forEach(emProcessSensorDTO -> {
            DataSourcesEnum dataSourcesEnum = DataSourcesEnum.getEnumByCode(emProcessSensorDTO.getDataSources());
            // 传感器编号
            String sensorCode = emProcessSensorDTO.getDeviceAddr();
            log.info("传感器编号:{}", sensorCode);
            // 网关编号
            String deviceAddr = sensorCode.contains(StrPool.UNDER_LINE) ? StringUtils.substringBeforeLast(sensorCode, StrPool.UNDER_LINE) : sensorCode;
            EmProcessSensor emProcessSensor = new EmProcessSensor();
            BeanUtils.copyProperties(emProcessSensorDTO, emProcessSensor);
            emProcessSensor.setDeviceAddr(deviceAddr);
            switch (dataSourcesEnum) {
                case MOCK_DATA:
                    redisUtils.strSet(CharSequenceUtil.format(RedisKeyConstants.EmSensor.SENSOR_DEMO_DATA, emProcessSensor.getDeviceAddr()), emProcessSensor.getInitDataVal().toString());
                    emProcessSensorService.addProcessSensor(emProcessSensor);
                    checkExistCacheRunData(sensorCode, deviceAddr, 0);
                    break;
                case ACREL:
                    emProcessSensorService.addProcessSensor(emProcessSensor);
                    checkExistCacheRunData(sensorCode, deviceAddr, 0);
                    break;
                case FRONT_END_PROCESSOR:
                    // 前置机
                    log.info("数据类型为前置机！");
                    break;
                default:
                    break;
            }
        });
    }

    @Override
    public void checkExistCacheRunData(String sensorCode, String deviceAddr, Integer triggerCount) {
        // 获取传感器缓存
        EmSensor emSensor = redisUtils.hashGet(RedisKeyConstants.EmSensor.SENSOR_CODE_HASH, CharSequenceUtil.format(RedisKeyConstants.EmSensor.SENSOR_CODE_KEY, sensorCode), EmSensor.class);
        log.info("获取传感器信息：{}", emSensor);
        // 不存在发送延时消息，存在就开始上报数据
        if (Objects.nonNull(emSensor)) {
            // 双重校验 校验数据库是否存在数据
            UserTokenContext.setToken(TokenUtils.getTemporaryToken(redisUtils));
            Result<EmSensorApiDTO> sensorApiDTOResult = emWebApi.getSensorById(emSensor.getId());
            UserTokenContext.clear();
            if (sensorApiDTOResult.isSuccess() && Objects.nonNull(sensorApiDTOResult.getResult())) {
                EmProcessSensor emProcessSensor = emProcessSensorService.getOneByDeviceAddr(deviceAddr);
                if (Objects.isNull(emProcessSensor)) {
                    log.error("延时消息查询网关为空!!!编号 → {}", deviceAddr);
                    return;
                }
                // 清除running缓存后开始上报数据
                emProcessSensorService.removeRunningCache(deviceAddr);
                if (DataSourcesEnum.ACREL.getCode().equals(emProcessSensor.getDataSources()) && AcrelDataConstants.DeviceType.WATER.equals(emProcessSensor.getType())) {
                    getDataService.getWaterDataWhenBefore(emProcessSensor);
                } else {
                    getDataService.getWhenBefore(emProcessSensor, Boolean.FALSE);
                }
            } else {
                sendMqDelayMsg(sensorCode, deviceAddr, triggerCount);
            }
        } else {
            // 延时五分钟再校验
            sendMqDelayMsg(sensorCode, deviceAddr, triggerCount);
        }
    }

    /**
     * 发送延时消息
     *
     * @param sensorCode 传感器编号
     * @param deviceAddr 网关编号
     */
    private void sendMqDelayMsg(String sensorCode, String deviceAddr, Integer triggerCount) {
        boolean fourth = triggerCount == 4;
        if (fourth) {
            log.error("延时触发超过三次查询不到传感器缓存，编号 → {}", sensorCode);
            return;
        }
        // 加运行中缓存，以防定时器触发上报数据
        emProcessSensorService.addRunningCache(deviceAddr);
        log.info("发送RabbitMQ延迟消息，sensorCode → {}", sensorCode);
        // 五分钟
        int delayTime = 2 * 60 * 1000;
        rabbitTemplate.convertAndSend(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.SENSOR_CACHE_VALIDATE_EXCHANGE, emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.DELAY_ROUTING_KEY,
                JSON.toJSONString(new EmSensorCacheValidateDTO(sensorCode, deviceAddr, triggerCount)), message -> {
                    message.getMessageProperties().setHeader("x-delay", delayTime);
                    return message;
                });
        log.info("发送RabbitMQ延迟消息完成！");
    }

    @Override
    public void meterControl(MeterControlReqDTO meterControlReqDTO) {
        String deviceAddr = meterControlReqDTO.getStr();
        // 校验是否测试环境
        emDataProfileService.checkMeterControl(deviceAddr);
        // 根据type判断电表还是水表
        EmProcessSensor emProcessSensor = emProcessSensorService.getOneByDeviceAddr(deviceAddr);
        if (Objects.isNull(emProcessSensor)) {
            throw new JeecgBootException("系统查询不到该设备，请联系管理员！");
        }
        // 阀控
        boolean isWater = AcrelDataConstants.DeviceType.WATER.equals(emProcessSensor.getType());
        try {
            // 转换map
            Map<String, String> paramMap = JSON.parseObject(JSON.toJSONString(meterControlReqDTO), Map.class);
            paramMap.remove("mapSensorId");
            paramMap.remove("userId");
            if (isWater) {
                getDataService.waterMeterControl(paramMap, false);
            } else {
                getDataService.meterControl(paramMap, false);
            }
        } catch (Exception e) {
            log.error("阀控异常！ 异常信息: {}", ThrowableUtils.getStackTraceByPackage(e));
            // 失败了状态取反， 通知web
            Integer status = SysConstants.AfterValveControlStatus.CLOSE.equals(meterControlReqDTO.getCodeType()) ? SysConstants.AfterValveControlStatus.OPEN : SysConstants.AfterValveControlStatus.CLOSE;
            emWebApi.updateValveControlStatus(new EmValveControlStatusDTO(meterControlReqDTO.getMapSensorId(), meterControlReqDTO.getUserId(), meterControlReqDTO.getCodeType(), status));
        }
        // 判断是否存在key 存在不发送延时
        EmMeterInfoAfterControlDTO emMeterInfoAfterControlDTO = new EmMeterInfoAfterControlDTO(emProcessSensor.getType(), emProcessSensor.getDeviceAddr(), 1,
                meterControlReqDTO.getCodeType(), meterControlReqDTO.getMapSensorId(), meterControlReqDTO.getUserId());
        // 缓存key
        String key = RedisKeyConstants.EmGateway.GATEWAY_VALVE_CONTROL_STATUS_HASH;
        String hashKey = CharSequenceUtil.format(RedisKeyConstants.EmGateway.GATEWAY_VALVE_CONTROL_STATUS_KEY, deviceAddr);
        // 判断是否已经存在key
        if (Boolean.TRUE.equals(redisUtils.hashHasKey(key, hashKey))) {
            // 存在覆盖 不发送延时消息
            addDeviceValveControlCache(emMeterInfoAfterControlDTO, key, hashKey);
            return;
        }
        // 加入缓存
        addDeviceValveControlCache(emMeterInfoAfterControlDTO, key, hashKey);
        // 分合闸后延时获取设备信息
        this.meterControlDelayGetInfo(emMeterInfoAfterControlDTO);
    }

    public static void main(String[] args) throws JsonProcessingException {
        MeterControlReqDTO meterControlReqDTO = new MeterControlReqDTO("123", 1, null, "1245");
        Map<String, String> paramMap = JSON.parseObject(JSON.toJSONString(meterControlReqDTO), Map.class);
//        Map<String, String> paramMap = JSON.parseObject(JSON.toJSONString(meterControlReqDTO), new  TypeReference<Map<String, String>>() {});
        System.out.println(paramMap);
        paramMap.remove("mapSensorId");
        HttpHeaders headers = new HttpHeaders();
        ObjectMapper mapper = new ObjectMapper();
        HttpEntity<String> entity = new HttpEntity<>(mapper.writeValueAsString(paramMap), headers);
        log.info("requestEntity → " + mapper.writeValueAsString(entity));
        DateTime truncate2 = DateUtil.parse("2025-04-02T04:19:38.7644698+08:00");
        System.out.println(DateUtil.formatDateTime(truncate2));
    }

    @Override
    public void meterControlDelayGetInfo(EmMeterInfoAfterControlDTO emMeterInfoAfterControlDTO) {
        // 获取实时数据 校验是否成功，不成功继续执行
        boolean isWater = AcrelDataConstants.DeviceType.WATER.equals(emMeterInfoAfterControlDTO.getType());
        // 获取缓存覆盖当前数据，为了处理单个网关多次阀控
        copyDeviceValveControlCache(emMeterInfoAfterControlDTO);
        // 执行时长(小时) 开始次数从1开始
        int executeHours = isWater ? 25 : 3;
        String deviceTypeStr = isWater ? "水" : "电";
        // 如果超过执行时长，终止执行
        if (emMeterInfoAfterControlDTO.getTriggerCount() > executeHours) {
            log.warn("{}表阀控状态检查超过{}小时，终止执行", deviceTypeStr, executeHours);
            return;
        }
        Result<String> meterInfoResult;
        Map<String, String> paramMap = new HashMap<>(1);
        try {
            if (isWater) {
                paramMap.put(AcrelDataConstants.STR, emMeterInfoAfterControlDTO.getDeviceAddr());
                meterInfoResult = getDataService.getWaterMeterInfo(paramMap, false);
            } else {
                paramMap.put(AcrelDataConstants.METER_ID_TWO, emMeterInfoAfterControlDTO.getDeviceAddr());
                meterInfoResult = getDataService.getCollect(paramMap, false);
            }
        } catch (Exception e) {
            log.error("获取{}表信息异常！异常信息: {}", deviceTypeStr, ThrowableUtils.getStackTraceByPackage(e));
            return;
        }
        // 获取阀控状态
        Integer currentStatus = isWater ? parseWaterMeterStatus(meterInfoResult) : parseElectricMeterStatus(meterInfoResult);
        // 下发指令和获取信息状态是否一致
        boolean disagree = Objects.isNull(currentStatus) || !currentStatus.equals(emMeterInfoAfterControlDTO.getMeterControlType());
        // 检查是否达到最大次数
        boolean isLastTime = emMeterInfoAfterControlDTO.getTriggerCount() == executeHours;
        UserTokenContext.setToken(TokenUtils.getTemporaryToken(redisUtils));
        // 检查阀控状态
        if (disagree) {
            log.info("{}表阀控状态不一致，当前状态:{}, 期望状态:{}, 发送延时消息", deviceTypeStr, currentStatus, emMeterInfoAfterControlDTO.getMeterControlType());
            if (isLastTime) {
                // 取反 调用API后终止
                Integer status = SysConstants.AfterValveControlStatus.CLOSE.equals(emMeterInfoAfterControlDTO.getMeterControlType())
                        ? SysConstants.AfterValveControlStatus.OPEN : SysConstants.AfterValveControlStatus.CLOSE;
                emWebApi.updateValveControlStatus(new EmValveControlStatusDTO(emMeterInfoAfterControlDTO.getMapSensorId(), emMeterInfoAfterControlDTO.getUserId(), emMeterInfoAfterControlDTO.getMeterControlType(), status));
                deleteDeviceValveControlCache(emMeterInfoAfterControlDTO.getDeviceAddr());
                return;
            }
            sendDelayedMeterControlMessage(emMeterInfoAfterControlDTO);
        } else {
            log.info("{}表:{}阀控状态一致，检查完成", deviceTypeStr, emMeterInfoAfterControlDTO.getDeviceAddr());
            // 调用webapi 更新阀控状态
            emWebApi.updateValveControlStatus(new EmValveControlStatusDTO(emMeterInfoAfterControlDTO.getMapSensorId(), emMeterInfoAfterControlDTO.getUserId(), emMeterInfoAfterControlDTO.getMeterControlType(), emMeterInfoAfterControlDTO.getMeterControlType()));
            deleteDeviceValveControlCache(emMeterInfoAfterControlDTO.getDeviceAddr());
        }
        UserTokenContext.clear();
    }

    /**
     * 解析水表信息JSON并获取阀门状态
     *
     * @param waterMeterInfo 获取水表信息的结果对象
     * @return 阀门状态（1表示开阀，2表示关阀），如果解析失败则返回null
     */
    private Integer parseWaterMeterStatus(Result<String> waterMeterInfo) {
        if (Objects.isNull(waterMeterInfo) || StringUtils.isBlank(waterMeterInfo.getResult())) {
            log.warn("水表信息为空或结果为空！");
            return null;
        }
        try {
            JSONObject responseBody = JSON.parseObject(waterMeterInfo.getResult());
            if (Objects.isNull(responseBody)) {
                log.warn("水表信息JSON解析失败，body为空！");
                return null;
            }
            responseBody.put(AcrelDataConstants.CREATE_TIME, DateUtil.formatDateTime(DateUtil.parse(responseBody.getString(AcrelDataConstants.CREATE_TIME))));
            // 推送实时数据
            getDataService.sendCollectMessage(responseBody, DataInterfaceEnum.GET_METER_WATER_INFO.getDi());
            // 1是开阀，2是关阀
            return responseBody.getIntValue("ForceMode");
        } catch (Exception e) {
            log.error("解析水表信息JSON异常！responseBody: {}, 异常信息: {}", waterMeterInfo.getResult(), ThrowableUtils.getStackTraceByPackage(e));
            return null;
        }
    }

    /**
     * 解析电表信息JSON并获取阀门状态
     *
     * @param collectResult 获取电表信息的结果对象
     * @return 阀门状态（1表示开阀，2表示关阀），如果解析失败则返回null
     */
    private Integer parseElectricMeterStatus(Result<String> collectResult) {
        if (Objects.isNull(collectResult) || StringUtils.isBlank(collectResult.getResult()) || StrPool.SQ_BRACKET.equals(collectResult.getResult())) {
            log.warn("电表信息为空或结果为空！");
            return null;
        }
        try {
            JSONObject responseBody = JSON.parseObject(collectResult.getResult());
            if (Objects.isNull(responseBody)) {
                log.warn("电表信息JSON解析失败，body为空！");
                return null;
            }
            // 推送实时数据
            getDataService.sendCollectMessage(responseBody, DataInterfaceEnum.GET_COLLECT.getDi());
            // 分合闸状态（true为拉闸，false为合闸）
            Boolean openOrClose = responseBody.getBoolean("OpenOrClose");
            return Boolean.TRUE.equals(openOrClose) ? AcrelDataConstants.MeterControlType.OPEN : AcrelDataConstants.MeterControlType.CLOSE;
        } catch (Exception e) {
            log.error("解析电表抄表信息JSON异常！responseBody: {}, 异常信息: {}",
                    collectResult.getResult(), ThrowableUtils.getStackTraceByPackage(e));
            return null;
        }
    }

    /**
     * 发送延时消息以重新检查阀门控制状态
     *
     * @param emMeterInfoAfterControlDTO 包含阀门控制信息的对象
     */
    private void sendDelayedMeterControlMessage(EmMeterInfoAfterControlDTO emMeterInfoAfterControlDTO) {
        // 触发次数+1
        int count = emMeterInfoAfterControlDTO.getTriggerCount() + 1;
        emMeterInfoAfterControlDTO.setTriggerCount(count);
        addDeviceValveControlCache(emMeterInfoAfterControlDTO);
        // 一小时后重新检查
        int delayTime = 60 * 60 * 1000;
        rabbitTemplate.convertAndSend(
                emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.METER_INFO_AFTER_CONTROL_EXCHANGE,
                emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.METER_INFO_AFTER_CONTROL,
                JSON.toJSONString(emMeterInfoAfterControlDTO),
                message -> {
                    message.getMessageProperties().setHeader("x-delay", delayTime);
                    return message;
                });
    }

    /**
     * 新增阀控延时缓存(使用原有触发次数)
     *
     * @param emMeterInfoAfterControlDTO 缓存值
     * @param key                        缓存key
     * @param hashKey                    网关编号hashKey
     */
    private void addDeviceValveControlCache(EmMeterInfoAfterControlDTO emMeterInfoAfterControlDTO, String key, String hashKey) {
        EmMeterInfoAfterControlDTO cacheOne = redisUtils.hashGet(key, hashKey, EmMeterInfoAfterControlDTO.class);
        if (Objects.nonNull(cacheOne)) {
            ;
            emMeterInfoAfterControlDTO.setTriggerCount(cacheOne.getTriggerCount());
        }
        addDeviceValveControlCache(emMeterInfoAfterControlDTO);
    }

    /**
     * 新增阀控延时缓存(使用原有触发次数)
     *
     * @param emMeterInfoAfterControlDTO 缓存值
     */
    private void addDeviceValveControlCache(EmMeterInfoAfterControlDTO emMeterInfoAfterControlDTO) {
        String key = RedisKeyConstants.EmGateway.GATEWAY_VALVE_CONTROL_STATUS_HASH;
        String hashKey = CharSequenceUtil.format(RedisKeyConstants.EmGateway.GATEWAY_VALVE_CONTROL_STATUS_KEY, emMeterInfoAfterControlDTO.getDeviceAddr());
        // 清理Code缓存
        redisUtils.hashDelete(key, hashKey);
        // 设置Code缓存
        redisUtils.hashPutIfAbsent(key, hashKey, JSON.toJSONString(emMeterInfoAfterControlDTO));
    }

    /**
     * 删除阀控延时缓存
     *
     * @param deviceAddr 网关编号
     */
    private void deleteDeviceValveControlCache(String deviceAddr) {
        // 清理Code缓存
        redisUtils.hashDelete(RedisKeyConstants.EmGateway.GATEWAY_VALVE_CONTROL_STATUS_HASH, CharSequenceUtil.format(RedisKeyConstants.EmGateway.GATEWAY_VALVE_CONTROL_STATUS_KEY, deviceAddr));
    }

    /**
     * 获取阀控延时缓存覆盖当前数据
     *
     * @param emMeterInfoAfterControlDTO 被覆盖对象
     */
    private void copyDeviceValveControlCache(EmMeterInfoAfterControlDTO emMeterInfoAfterControlDTO) {
        EmMeterInfoAfterControlDTO cacheOne = redisUtils.hashGet(RedisKeyConstants.EmGateway.GATEWAY_VALVE_CONTROL_STATUS_HASH,
                CharSequenceUtil.format(RedisKeyConstants.EmGateway.GATEWAY_VALVE_CONTROL_STATUS_KEY, emMeterInfoAfterControlDTO.getDeviceAddr()), EmMeterInfoAfterControlDTO.class);
        if (Objects.isNull(cacheOne)) {
            return;
        }
        emMeterInfoAfterControlDTO.setMeterControlType(cacheOne.getMeterControlType());
        emMeterInfoAfterControlDTO.setMapSensorId(cacheOne.getMapSensorId());
        emMeterInfoAfterControlDTO.setUserId(cacheOne.getUserId());
    }

    @Override
    public void originChannel(String deviceAddr, Date date, Integer dataSources, Integer dataFreq) {
        // 回溯当天
        if (new Date().before(date)) {
            return;
        }
        // 结束时间大于当前时间取当前时间
        Date endDate = DateUtil.endOfDay(date);
        if (new Date().before(endDate)) {
            endDate = new Date();
        }
        // 按天回溯数据
        EmProcessSensor emProcessSensor = emProcessSensorService.getOneByDeviceAddr(deviceAddr);
        if (Objects.isNull(emProcessSensor)) {
            return;
        }
        getDataService.runHistoryData(deviceAddr, date, endDate, DataSourcesEnum.getEnumByCode(dataSources), dataFreq, Boolean.FALSE, emProcessSensor.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteJobAddAgain(EmSensorRerunDataDTO emSensorRerunDataDTO) {
        if (!emSensorRerunDataDTO.getSensorCode().contains(StrPool.UNDER_LINE)) {
            log.error("网关地址不包含下划线！跳过本次重跑数据.");
            throw new JeecgBootException("网关地址不包含下划线！跳过本次重跑数据");
        }
        emSensorRerunDataDTO.setSensorCode(StringUtils.substringBeforeLast(emSensorRerunDataDTO.getSensorCode(), StrPool.UNDER_LINE));
        emSensorRerunDataDTO.setOldSensorCode(StringUtils.substringBeforeLast(emSensorRerunDataDTO.getOldSensorCode(), StrPool.UNDER_LINE));
        String sysParkId = emSensorRerunDataDTO.getSysParkId();
        // 园区维度定时任务需要园区id
        if (StringUtils.isBlank(sysParkId)) {
            EmProcessSensor oldEmProcessSensor = emProcessSensorService.getOneByDeviceAddr(emSensorRerunDataDTO.getOldSensorCode());
            sysParkId = oldEmProcessSensor.getSysParkId();
            emSensorRerunDataDTO.setSysParkId(sysParkId);
        }
        // 删除sensor
        emProcessSensorService.removeByDeviceAddr(emSensorRerunDataDTO.getOldSensorCode());
        // 修改数据频率
        if (emSensorRerunDataDTO.getSensorCode().equals(emSensorRerunDataDTO.getOldSensorCode())) {
            EmProcessSensor emProcessSensor = new EmProcessSensor();
            emProcessSensor.setType(AcrelDataConstants.DeviceType.POWER);
            emProcessSensor.setUserId(DatabaseConstants.SYS_ADMIN);
            emProcessSensor.setDeviceAddr(emSensorRerunDataDTO.getSensorCode());
            emProcessSensor.setJcdId(emProcessSensor.getDeviceAddr());
            emProcessSensor.setDataSources(emSensorRerunDataDTO.getDataSources());
            emProcessSensor.setDataFreq(emSensorRerunDataDTO.getDataFreq());
            emProcessSensor.setInitDataVal(emSensorRerunDataDTO.getInitDataVal());
            // 获取上次数据时间
            Date dataTime = StatTimeUtils.getPrevDataTime(emSensorRerunDataDTO.getInstallTime(), emProcessSensor.getDataFreq());
            emProcessSensor.setLatestComTime(dataTime);
            emProcessSensor.setRecallStartTime(dataTime);
            emProcessSensor.setRecallEndTime(dataTime);
            // 最近任务时间默认当前时间
            emProcessSensor.setLatestTaskTime(StatTimeUtils.getDataTime(new Date(), emProcessSensor.getDataFreq()));
            emProcessSensor.setSysParkId(sysParkId);
            emProcessSensorService.save(emProcessSensor);
        } else {
            // 修改编号
            EmProcessSensorDTO emProcessSensorDTO = new EmProcessSensorDTO();
            BeanUtils.copyProperties(emSensorRerunDataDTO, emProcessSensorDTO);
            emProcessSensorDTO.setDeviceAddr(emSensorRerunDataDTO.getSensorCode());
            emProcessSensorDTO.setLatestComTime(emSensorRerunDataDTO.getInstallTime());
            addSensor(Collections.singletonList(emProcessSensorDTO));
        }
    }

    @Override
    public void rerunData(EmSensorRerunDataDTO emSensorRerunDataDTO) {
        if (StringUtils.isBlank(emSensorRerunDataDTO.getSensorCode())) {
            throw new JeecgBootException("传感器编码不能为空！");
        }
        if (!emSensorRerunDataDTO.getSensorCode().contains(StrPool.UNDER_LINE)) {
            throw new JeecgBootException("网关地址不包含下划线！跳过本次重跑数据.");
        }
        // 模拟数据修改安装时间
        if (DataSourcesEnum.MOCK_DATA.getCode().equals(emSensorRerunDataDTO.getDataSources()) && Boolean.TRUE.equals(emSensorRerunDataDTO.getChangeInstallTime())) {
            getDataService.rerunMockData(emSensorRerunDataDTO);
        } else {
            emSensorOriginDataService.rerunOriginData(emSensorRerunDataDTO);
        }
    }
}
