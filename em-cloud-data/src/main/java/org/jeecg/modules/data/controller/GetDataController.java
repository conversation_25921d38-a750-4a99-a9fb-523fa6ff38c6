package org.jeecg.modules.data.controller;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.boot.starter.rabbitmq.client.RabbitMqClient;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.SysIpRestrictions;
import org.jeecg.common.aspect.annotation.SysLog;
import org.jeecg.common.base.BaseMap;
import org.jeecg.common.constant.RabbitMqConstants;
import org.jeecg.common.constant.StrPool;
import org.jeecg.common.constant.SysTips;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.data.config.EmPrefixProperties;
import org.jeecg.modules.data.dto.MeterControlReqDTO;
import org.jeecg.modules.data.dto.SalePowerReqDTO;
import org.jeecg.modules.data.dto.UserReqDTO;
import org.jeecg.modules.data.entity.EmProcessSensor;
import org.jeecg.modules.data.entity.SysQuartzJob;
import org.jeecg.modules.data.service.*;
import org.jeecg.modules.data.vo.EmMeterInfoAfterControlDTO;
import org.jeecg.modules.data.vo.EmSensorCacheValidateDTO;
import org.jeecg.modules.data.vo.MockDataDTO;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;


/**
 * 获取数据
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/data/testOne")
public class GetDataController {
    @Lazy
    @Autowired
    private GetDataService getDataService;
    @Autowired
    private RabbitMqClient rabbitMqClient;
    @Autowired
    private QuartzJobService quartzJobService;
    @Autowired
    private TaskGetService taskGetService;
    @Autowired
    private EmProcessSensorService emProcessSensorService;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private EmPrefixProperties emPrefixProperties;
    @Autowired
    private EmDataSourcesService emDataSourcesService;

    @GetMapping(value = "/one")
    public void one(int count) {
        int delayTime = count * 60 * 1000;
        rabbitTemplate.convertAndSend(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.SENSOR_CACHE_VALIDATE_EXCHANGE, emPrefixProperties.getRabbitMqPrefix() + "em.delay.sensor.cache",
                JSON.toJSONString(new EmSensorCacheValidateDTO("2323", "dasdfsdfs", 0)), message -> {
                    message.getMessageProperties().setHeader("x-delay", delayTime);
                    return message;
                });
        log.info("发送RabbitMQ延迟消息完成！");
    }

    /**
     * 根据日志id重试
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/reTryByLogId")
    public Result<?> reTryByLogId(@NotBlank(message = "ID不能为空！") @RequestParam(name = "id") String id) {
        Result<String> result = new Result<>();
        try {
            result = getDataService.reTryByLogId(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 用户登录
     */
    @GetMapping(value = "/userLogin")
    public Result<?> userLogin() {
        Result<String> result = new Result<>();
        try {
            result = getDataService.userLogin(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取用户名下所有电表信息
     * 初始化库表 塞到redis
     */
    @GetMapping(value = "/meterList")
    public Result<?> meterList() {
        Result<String> result = new Result<>();
        try {
            result = getDataService.meterList(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取电表信息
     *
     * @param sensorId
     * @return
     */
    @GetMapping(value = "/getMeterInfo")
    public Result<?> getMeterInfo(@NotBlank(message = "电表ID不能为空！") @RequestParam(name = "sensorId") String sensorId) {
        Result<String> result = new Result<>();
        try {
            Map<String, String> map = new HashMap<>(1);
            map.put("str", sensorId);
            result = getDataService.getMeterInfo(map, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 电费充值（管理员权限）
     *
     * @param reqDTO
     */
    @PostMapping(value = "/salePower")
    public Result<?> salePower(@Valid @RequestBody SalePowerReqDTO reqDTO) {
        Result<String> result = new Result<>();
        try {
            result = getDataService.salePower(JSON.parseObject(JSON.toJSONString(reqDTO), Map.class), false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 分合闸控制（管理员权限）
     *
     * @param reqDTO
     */
    @SysIpRestrictions
    @PostMapping(value = "/meterControl")
    public Result<?> meterControl(@Valid @RequestBody MeterControlReqDTO reqDTO) {
        Result<String> result = new Result<>();
        try {
            result = getDataService.meterControl(JSON.parseObject(JSON.toJSONString(reqDTO), Map.class), false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 开户（管理员权限）
     *
     * @param reqDTO
     */
    @PostMapping(value = "/openUser")
    public Result<?> openUser(@Valid @RequestBody UserReqDTO reqDTO) {
        Result<String> result = new Result<>();
        try {
            result = getDataService.openUser(JSON.parseObject(JSON.toJSONString(reqDTO), Map.class), false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 修改用户（管理员权限）
     *
     * @param reqDTO
     * @throws Exception
     */
    @PostMapping(value = "/editorUser")
    public Result<?> editorUser(@Valid @RequestBody UserReqDTO reqDTO) {
        Result<String> result = new Result<>();
        try {
            result = getDataService.editorUser(JSON.parseObject(JSON.toJSONString(reqDTO), Map.class), false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 销户（管理员权限）
     *
     * @param id
     */
    @PostMapping(value = "/delUser")
    public Result<?> delUser(@NotBlank(message = "用户ID不能为空！") @RequestParam(name = "id") String id) {
        Result<String> result = new Result<>();
        try {
            Map<String, String> map = new HashMap<>(1);
            map.put("id", id);
            result = getDataService.delUser(map, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 用电统计
     *
     * @param id        用户Id
     * @param startDate 开始时间 例(2018061000)
     * @param endDate   结束时间 例(2018061400)
     * @return
     */
    @GetMapping(value = "/usePower")
    public Result<?> usePower(@NotBlank(message = "用户ID不能为空！") @RequestParam(name = "id") String id,
                              @NotBlank(message = "开始时间不能为空！") @RequestParam(name = "startDate") String startDate,
                              @NotBlank(message = "结束时间不能为空！") @RequestParam(name = "endDate") String endDate) {
        Result<String> result = new Result<>();
        try {
            Map<String, String> map = new HashMap<>(3);
            map.put("Id", id);
            map.put("startDate", startDate);
            map.put("endDate", endDate);
            result = getDataService.usePower(map, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 根据电表编号查管控表信息的接口
     *
     * @param str
     * @return
     */
    @GetMapping(value = "/getMeterWatchInfo")
    public Result<?> getMeterWatchInfo(@NotBlank(message = "电表编号不能为空！") @RequestParam(name = "str") String str) {
        Result<String> result = new Result<>();
        try {
            Map<String, String> map = new HashMap<>(1);
            map.put("str", str);
            result = getDataService.getMeterWatchInfo(map, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 根据电表编号获取抄表信息
     *
     * @param meterId
     * @return
     */
    @GetMapping(value = "/getCollect")
    public Result<?> getCollect(@NotBlank(message = "电表编号不能为空！") @RequestParam(name = "meterId") String meterId) {
        Result<String> result = new Result<>();
        try {
            Map<String, String> map = new HashMap<>(1);
            map.put("meterID", meterId);
            result = getDataService.getCollect(map, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取管理员账号下所有电表（管理员权限）
     *
     * @return
     */
    @GetMapping(value = "/getAllMeters")
    public Result<?> getAllMeters() {
        Result<String> result = new Result<>();
        try {
            result = getDataService.getAllMeters(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获得一段时间内的电表电能数据（管理员权限）   弃用
     *
     * @param meterId   电表编号
     * @param startDate 开始时间 例(2018061000)
     * @param endDate   结束时间 例(2018061400)
     * @return
     */
    @GetMapping(value = "/getEnergyData")
    public Result<?> getEnergyData(@NotBlank(message = "电表编号不能为空！") @RequestParam(name = "meterId") String meterId,
                                   @NotBlank(message = "开始时间不能为空！") @RequestParam(name = "startDate") String startDate,
                                   @NotBlank(message = "结束时间不能为空！") @RequestParam(name = "endDate") String endDate) {
        Result<String> result = new Result<>();
        try {
            Map<String, String> map = new HashMap<>(3);
            map.put("MeterID", meterId);
            map.put("StartDate", startDate);
            map.put("EndDate", endDate);
            result = getDataService.getEnergyData(map, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获得一段时间内的电表电能数据（管理员权限）
     * 新版接口，读取mongodb数据
     *
     * @param meterId   电表编号
     * @param startDate 开始时间 例(2021-02-22)
     * @param endDate   结束时间 例(2021-02-22)
     * @param dataTime  数据时间 例(2021-02-22 10:00:00)  为空返回这段时间所有数据
     * @return
     */
    @GetMapping(value = "/getEnergyData2")
    public Result<?> getEnergyData2(@NotBlank(message = "电表编号不能为空！") @RequestParam(name = "meterId") String meterId,
                                    @NotBlank(message = "开始时间不能为空！") @RequestParam(name = "startDate") String startDate,
                                    @NotBlank(message = "结束时间不能为空！") @RequestParam(name = "endDate") String endDate,
                                    @NotBlank(message = "数据时间不能为空！") @RequestParam(name = "dataTime") String dataTime) {
        Result<String> result = new Result<>();
        try {
            Map<String, String> map = new HashMap<>(4);
            map.put("MeterID", meterId);
            map.put("StartDate", startDate);
            map.put("EndDate", endDate);
            map.put("dataTime", dataTime);
            result = getDataService.getEnergyData2(map, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取管控表数据
     *
     * @return
     */
    @GetMapping(value = "/getControlMeter")
    public Result<?> getControlMeter() {
        Result<String> result = new Result<>();
        try {
            result = getDataService.getControlMeter(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取所有用户及电表(管理员权限)
     *
     * @return
     */
    @GetMapping(value = "/getOwnerDetails")
    public Result<?> getOwnerDetails() {
        Result<String> result = new Result<>();
        try {
            result = getDataService.getOwnerDetails(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取某天用电数据(管理员权限)     弃用
     *
     * @param meterId   电表编号
     * @param startDate 开始时间 例(2018061000)
     * @param endDate   结束时间 例(2018061400)
     * @return
     */
    @GetMapping(value = "/getEnergyDay")
    public Result<?> getEnergyDay(@NotBlank(message = "电表编号不能为空！") @RequestParam(name = "meterId") String meterId,
                                  @NotBlank(message = "开始时间不能为空！") @RequestParam(name = "startDate") String startDate,
                                  @NotBlank(message = "结束时间不能为空！") @RequestParam(name = "endDate") String endDate) {
        Result<String> result = new Result<>();
        try {
            Map<String, String> map = new HashMap<>(3);
            map.put("MeterID", meterId);
            map.put("StartDate", startDate);
            map.put("EndDate", endDate);
            result = getDataService.getEnergyDay(map, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @GetMapping(value = "/rabbitmq")
    public Result<?> rabbitMqClientTest(HttpServletRequest req) {
        //rabbitmq消息队列测试
        BaseMap map = new BaseMap();
        map.put("orderId", RandomUtil.randomNumbers(10));
        rabbitMqClient.sendMessage("balala", map);
        return Result.ok("MQ发送消息成功");
    }

    /**
     * 测试模拟数据
     *
     * @param meterId  电表编号
     * @param dataTime
     * @return
     */
    @PostMapping(value = "/getEnergyData2Demo")
    public Result<?> getEnergyDay(@NotBlank(message = "电表编号不能为空！") @RequestParam(name = "meterId") String meterId,
                                  @NotNull(message = "数据时间不能为空！") @RequestParam(name = "dataTime") Date dataTime,
                                  @NotNull(message = "测试状态数据不能为空！") @RequestParam(name = "shiftData") Integer shiftData,
                                  @RequestParam(name = "data", required = false) String data) {
        return getDataService.getEnergyData2Demo(meterId, dataTime, new MockDataDTO(new BigDecimal(data), shiftData));
    }

    /**
     * 删除定时任务
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/deleteJob")
    public Result<?> deleteJob(@RequestParam(name = "id") String id) {
        SysQuartzJob job = new SysQuartzJob();
        job.setId(id);
        quartzJobService.deleteAndStopJob(job);
        return Result.ok();
    }

    /**
     * 新增单个网关
     *
     * @param emProcessSensor
     * @return
     */
    @PostMapping(value = "/addOneSensor")
    public Result<String> addOneSensor(@RequestBody EmProcessSensor emProcessSensor) {
        return getDataService.addOneSensor(emProcessSensor);
    }

    /**
     * 删除所有网关定时器
     *
     * @return
     */
    @GetMapping(value = "/deleteAllJob")
    public Result<String> deleteAllJob(@RequestParam(name = "jobClassName", required = false) String jobClassName,
                                       @RequestParam(name = "meterId", required = false) String meterId) {
        List<String> idList = new ArrayList<>();
        if (StringUtils.isNotBlank(meterId)) {
            idList = Arrays.asList(meterId.split(StrPool.COMMA));
        }
        return getDataService.deleteAllJob(idList, jobClassName);
    }

    /**
     * 查询没有定时器的传感器，加定时器
     *
     * @return
     */
    @GetMapping(value = "/dealOldSensorJob")
    public void dealOldSensorJob() {
        getDataService.dealOldSensorJob();
    }

    /**
     * 前置机历史数据获取下发
     *
     * @return
     */
    @GetMapping("copyData")
    public Result<String> copyData(@RequestParam(name = "sdt") String sdt,
                                   @RequestParam(name = "edt") String edt,
                                   @RequestParam(name = "sensorId", required = false) String sensorId) {
        log.info("copyData sdt → {}, edt → {}, sensorId → {}", sdt, edt, sensorId);
        taskGetService.copyData(sdt, edt, sensorId);
        return Result.ok();
    }

    /**
     * 初始化回溯时间
     */
    @GetMapping(value = "/initReCallDate")
    public void initReCallDate() {
        emProcessSensorService.initReCallDate();
    }

    /**
     * 执行一次定时任务
     *
     * @return
     */
    @GetMapping(value = "/triggerJob")
    public Result<String> triggerJob(@RequestParam(name = "meterId", required = false) String meterId) {
        getDataService.triggerJob(meterId);
        return Result.ok();
    }

    /**
     * 处理传感器的上班数据
     *
     * @param sysParkId 园区
     * @param meterId   传感器code去掉下划线后面的字符 （优先级高于园区）
     * @param freq      频率，园区不为空时候才起作用
     * @return org.jeecg.common.api.vo.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023年09月15日 15:09:47
     */
    @GetMapping(value = "/processData")
    public Result<String> processData(@RequestParam(name = "sysParkId", required = false) String sysParkId,
                                      @RequestParam(name = "meterId", required = false) String meterId,
                                      @RequestParam(name = "freq", required = false) Integer freq) {
        getDataService.processData(sysParkId, meterId, freq);
        return Result.ok();
    }

    @GetMapping(value = "/initSysParkJob")
    @SysLog(value = "能源监测-初始化园区网关定时任务")
    @ApiOperation(value = "能源监测-初始化园区网关定时任务", notes = "能源监测-初始化园区网关定时任务")
    public Result<String> initSysParkJob(@RequestParam(name = "sysParkId", required = false) String sysParkId) {
        getDataService.initSysParkJob(sysParkId, Boolean.TRUE);
        return Result.ok();
    }

    @GetMapping(value = "/pauseJobs")
    @SysLog(value = "能源监测-暂停园区网关定时任务")
    @ApiOperation(value = "能源监测-暂停园区网关定时任务", notes = "能源监测-暂停园区网关定时任务")
    public Result<String> pauseJobs(@RequestParam(name = "jobIds", required = true) String jobIds) {
        if (StringUtils.isBlank(jobIds)) {
            return Result.error("参数为空");
        }
        List<String> failedIds = new ArrayList<>(1);
        List<String> idList = Arrays.asList(jobIds.split(","));
        for (String id : idList) {
            SysQuartzJob sysQuartzJob = new SysQuartzJob();
            sysQuartzJob.setId(id);
            try {
                quartzJobService.pause(sysQuartzJob);
                log.info("定时任务暂停成功：{}", id);
            } catch (Exception e) {
                log.error("暂停园区网关: {} 定时任务异常：{}", id, e.getMessage(), e);
                failedIds.add(id);
            }
        }
        return Result.ok(failedIds.stream().collect(Collectors.joining(",")));
    }

    /**
     * 恢复定时任务
     */
    @GetMapping(value = "/resumeJobs")
    @SysLog(value = "能源监测-恢复园区网关定时任务")
    @ApiOperation(value = "能源监测-恢复园区网关定时任务", notes = "能源监测-恢复园区网关定时任务")
    public Result<String> resumeJob(@RequestParam(name = "jobIds", required = true) String jobIds) {
        if (StringUtils.isBlank(jobIds)) {
            return Result.error("参数为空");
        }
        List<String> failedIds = new ArrayList<>(1);
        List<String> idList = Arrays.asList(jobIds.split(","));
        for (String id : idList) {
            try {
                SysQuartzJob job = quartzJobService.getById(id);
                if (job == null) {
                    return Result.error("定时任务不存在！");
                }
                quartzJobService.resumeJob(job);
                log.info("定时任务恢复成功：{} 园区：{}", id, job.getParameter());
            } catch (Exception e) {
                log.error("恢复园区网关: {} 定时任务异常：{}", id, e.getMessage(), e);
                failedIds.add(id);
            }
        }
        return Result.ok(failedIds.stream().collect(Collectors.joining(",")));
    }

    /**
     * 删除传感器维度的定时任务和初始化园区网关
     *
     * @return org.jeecg.common.api.vo.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023年09月14日 13:09:00
     */
    @SysLog("数据网关-删除传感器维度的定时任务和初始化园区网关")
    @GetMapping(value = "/delSensorJobAndAddParkJob")
    public Result<String> delSensorJobAndAddParkJob() {
        getDataService.delSensorJobAndAddParkJob();
        return Result.ok(SysTips.SUCCESS_MSG);
    }

    /**
     * 获取异步线程池的情况
     *
     * @param poolName
     * @return org.jeecg.common.api.vo.Result<java.lang.String>
     * <AUTHOR> @date 2023年09月15日 15:09:46
     */
    @GetMapping(value = "/getThreadPool")
    public Result<String> getThreadPool(@RequestParam(name = "poolName", required = false) String poolName) {
        if (StringUtils.isBlank(poolName)) {
            poolName = "asyncServiceExecutor";
        }
        ThreadPoolExecutor poolExecutor = SpringContextUtils.getBean("asyncServiceExecutor");
        return Result.ok(JSON.toJSONString(poolExecutor));
    }

    /**
     * 阀控后，延迟获取表信息
     *
     * @param emMeterInfoAfterControlDTO
     * @return
     */
    @PostMapping(value = "/meterControlDelayGetInfo")
    public Result<String> meterControlDelayGetInfo(@RequestBody EmMeterInfoAfterControlDTO emMeterInfoAfterControlDTO) {
        emDataSourcesService.meterControlDelayGetInfo(emMeterInfoAfterControlDTO);
        return Result.ok(SysTips.SUCCESS_MSG);
    }

}
