package org.jeecg.modules.data.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 用户和设备信息
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Data
public class UserAndMeterResDTO implements Serializable {
    private static final long serialVersionUID = 7425169604734729835L;
    /**
     * 用户名称
     */
    @JsonProperty("UserName")
    private String userName;
    /**
     * 用户id
     */
    @JsonProperty("UserId")
    private String userId;
    /**
     * 仪表编号
     */
    @JsonProperty("MeterID")
    private String meterId;
    /**
     * 是否合并计量
     */
    @JsonProperty("Together")
    private Integer together;
    /**
     * 是否开户
     */
    @JsonProperty("UserStatus")
    private String userStatus;
    /**
     * 项目名称
     */
    @JsonProperty("ProjectName")
    private String projectName;
}
