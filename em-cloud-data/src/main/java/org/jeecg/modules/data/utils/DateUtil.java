package org.jeecg.modules.data.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.jeecg.common.constant.SysTips;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间转换
 *
 * <AUTHOR>
 * @date 2022/1/24
 */
@Slf4j
public class DateUtil {
    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String PATTERN_T = "yyyy-MM-dd";

    /**
     * 设置传入字符串分钟秒为0
     *
     * @param str
     * @return
     */
    public static String getHourDateStr(String str) {
        Calendar calendar = Calendar.getInstance();
        if (str != null) {
            try {
                calendar.setTime(DateUtils.parseDate(str, PATTERN));
            } catch (ParseException e) {
                log.error(SysTips.DATE_FORMAT_ERROR);
                e.printStackTrace();
            }
        } else {
            calendar.setTime(new Date());
        }
        // 小时减一个小时
        calendar.add(Calendar.HOUR_OF_DAY, -3);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return new SimpleDateFormat(PATTERN).format(calendar.getTime());
    }

    /**
     * 获取传入日期str
     * 如果是零点取传入日期的昨日
     *
     * @return
     */
    public static String getToDayStr(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (calendar.get(Calendar.HOUR_OF_DAY) <= 2) {
            calendar.add(Calendar.DAY_OF_MONTH, -1);
        }
        return new SimpleDateFormat(PATTERN_T).format(calendar.getTime());
    }

    /**
     * 获取传入日期的明天str
     * 如果是零点取传入日期
     *
     * @return
     */
    public static String getTomorrowStr(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (calendar.get(Calendar.HOUR_OF_DAY) >= 3) {
            calendar.add(Calendar.DAY_OF_MONTH, +1);
        }
        return new SimpleDateFormat(PATTERN_T).format(calendar.getTime());
    }

    /**
     * 转换时间格式
     *
     * @return
     */
    public static String dateToString(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return new SimpleDateFormat(PATTERN).format(calendar.getTime());
    }

    /**
     * 转换时间格式
     *
     * @return
     */
    public static String dateToStringReduceHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, -3);
        // 减3个小时
        return new SimpleDateFormat(PATTERN).format(calendar.getTime());
    }

    /**
     * 传入时间加一小时
     *
     * @param date
     * @return
     */
    public static String addOneHourStr(Date date) {
        return new SimpleDateFormat(PATTERN).format(addOneFreq(date, 60));
    }

    /**
     * 传入时间加一小时  和当前时间比较，是否在当前时间之前
     *
     * @param date
     * @return
     */
    public static Boolean beforeOneHourBoolean(Date date) {
        return addOneFreq(date, 60).before(new Date());
    }

    /**
     * 传入时间加一个数据频率 返回date
     *
     * @param date
     * @param min
     * @return
     */
    public static Date addOneFreq(Date date, Integer min) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, min);
        return calendar.getTime();
    }

    /**
     * 当前时间减一个小时
     *
     * @param date
     * @return
     */
    public static Date dateLastHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
//        calendar.add(Calendar.HOUR, -1);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * string转date,分钟，秒赋值0
     *
     * @param str
     * @return
     */
    public static Date strToDate(String str) {
        Calendar calendar = Calendar.getInstance();
        try {
            ZonedDateTime datetime = ZonedDateTime.parse(str);
            Date date = Date.from(datetime.toInstant());
            calendar.setTime(date);
        } catch (Exception e) {
            log.error(SysTips.DATE_FORMAT_ERROR);
            e.printStackTrace();
        }
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
}
