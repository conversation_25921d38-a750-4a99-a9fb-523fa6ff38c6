package org.jeecg.modules.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.data.entity.EmProcessSensor;

import java.util.Date;
import java.util.List;

/**
 * 能源监测-数据处理-设备Service接口
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
public interface EmProcessSensorService extends IService<EmProcessSensor> {

    /**
     * 查询所有
     *
     * @return
     */
    List<EmProcessSensor> selectAll();

    /**
     * 查询该用户下所有设备
     *
     * @param userId
     * @return
     */
    List<EmProcessSensor> selectListByUserId(String userId);

    /**
     * 根据网关id获取
     *
     * @param deviceAddr
     * @return
     */
    EmProcessSensor getOneByDeviceAddr(String deviceAddr);

    /**
     * 更新数据上传时间
     *
     * @param meterId         网关id
     * @param recallStartTime 回溯开始时间
     * @param recallEndTime   回溯结束时间
     * @param latestComTime   最近一次数据时间
     * @param latestTaskTime  最近一次任务时间
     */
    void updateSensor(String meterId, Date recallStartTime, Date recallEndTime, Date latestComTime, Date latestTaskTime);

    /**
     * 获取没有定时的传感器
     *
     * @return
     */
    List<EmProcessSensor> getNotHaveJob();

    /**
     * 新增网关
     *
     * @param processSensor
     */
    void addProcessSensor(EmProcessSensor processSensor);

    /**
     * 根据网关地址删除
     *
     * @param deviceAddr
     */
    void removeByDeviceAddr(String deviceAddr);

    /**
     * 根据网关地址删除并删除数据缓存
     *
     * @param deviceAddrList
     */
    void removeByDeviceAddrAndDeleteDataCache(List<String> deviceAddrList);

    /**
     * 新增网关正运行缓存
     *
     * @param deviceAddr
     */
    void addRunningCache(String deviceAddr);

    /**
     * 获取网关正运行缓存
     *
     * @param deviceAddr
     * @return
     */
    String getRunningCache(String deviceAddr);

    /**
     * 清除网关正运行缓存
     *
     * @param deviceAddr
     */
    void removeRunningCache(String deviceAddr);

    /**
     * 获取回溯数据结束时间不等于最近一次数据时间的集合
     *
     * @return
     */
    List<EmProcessSensor> listByLatestComTime();

    /**
     * 初始化回溯时间
     */
    void initReCallDate();

    /**
     * 按园区获取
     * <AUTHOR>
     * @date   2023年09月05日 18:09:12
     * @param  sysParkId
     * @param  freq      频率可空
     * @return java.util.List<org.jeecg.modules.data.entity.EmProcessSensor>
     */
    List<EmProcessSensor> getBySysParkId(String sysParkId, Integer freq);

    void repairSysParkId(String sensorCode);
}
