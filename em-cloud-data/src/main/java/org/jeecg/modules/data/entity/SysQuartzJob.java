package org.jeecg.modules.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecg.common.aspect.annotation.SysDict;
import org.jeecg.common.system.base.entity.SysBaseEntity;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * 定时任务在线管理
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2019-01-02
 */
@Data
@TableName("sys_quartz_job")
@EqualsAndHashCode(callSuper = true)
public class SysQuartzJob extends SysBaseEntity implements Serializable {
    private static final long serialVersionUID = -1586641914312238384L;
    /**
     * 任务类名
     */
    @Excel(name = "任务类名", width = 40)
    private String jobClassName;
    /**
     * cron表达式
     */
    @Excel(name = "cron表达式", width = 30)
    private String cronExpression;
    /**
     * 参数
     */
    @Excel(name = "参数", width = 15)
    private String parameter;
    /**
     * 状态：1-正常，0-停止
     */
    @Excel(name = "状态", width = 15, dicCode = "quartz_status")
    @SysDict(dictCode = "quartz_status")
    private Boolean status;
    /**
     * 删除状态：0-未删除，1-已删除
     */
    @TableField(value = "is_deleted")
    private Boolean deleted;
    /**
     * 描述
     */
    @Excel(name = "描述", width = 40)
    private String description;
}
