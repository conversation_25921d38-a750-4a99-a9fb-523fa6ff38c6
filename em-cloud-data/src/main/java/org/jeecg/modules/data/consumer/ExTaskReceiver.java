package org.jeecg.modules.data.consumer;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.boot.starter.rabbitmq.client.RabbitMqClient;
import org.jeecg.common.constant.RabbitMqConstants;
import org.jeecg.modules.data.config.EmPrefixProperties;
import org.jeecg.modules.data.entity.EmGatewayData;
import org.jeecg.modules.data.entity.EmSensorOriginData;
import org.jeecg.modules.data.service.EmGatewayDataService;
import org.jeecg.modules.data.vo.EmExGatewayDataDTO;
import org.jeecg.modules.data.vo.EmExSensorOriginDataDTO;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

/**
 * 前置机数据消费
 *
 * <AUTHOR>
 * @date 2022-05-20
 */
@Slf4j
@Component
public class ExTaskReceiver {
    @Autowired
    private RabbitMqClient rabbitMqClient;
    @Autowired
    private EmPrefixProperties emPrefixProperties;
    @Lazy
    @Autowired
    private EmGatewayDataService emGatewayDataService;

    /**
     * 前置机数据
     *
     * @param msg
     * @param channel
     * @param deliveryTag
     */
    @RabbitListener(bindings = @QueueBinding(
            exchange = @Exchange(name = RabbitMqConstants.EX_DATA_EXCHANGE, type = "fanout", ignoreDeclarationExceptions = "true"),
            value = @Queue(value = RabbitMqConstants.EX_TASK_QUEUE, durable = "true")
    ), concurrency = "4-10")
    public void onMessage(Message<String> msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("消费前置机消息 → " + msg);
            EmExGatewayDataDTO exGatewayData = JSON.parseObject(msg.getPayload(), EmExGatewayDataDTO.class);
            // 网关数据
            EmGatewayData gatewayData = new EmGatewayData();
            gatewayData.setSendTime(DateUtil.formatDateTime(exGatewayData.getSendTime()));
            gatewayData.setJcdId(exGatewayData.getJcdId());
            gatewayData.setTaskDi(exGatewayData.getTaskDi());
            gatewayData.setTaskName(exGatewayData.getTaskName());
            gatewayData.setDeviceAddr(exGatewayData.getDeviceAddr());
            // 获取传感器原始数据
            List<EmExSensorOriginDataDTO> enesSensorRealDataList = JSON.parseArray(exGatewayData.getDatas(), EmExSensorOriginDataDTO.class);
            gatewayData.setDataContent(JSON.toJSONString(enesSensorRealDataList));
            List<EmSensorOriginData> sensorOriginDataList = emGatewayDataService.insert(gatewayData);
            log.info("发送前置机数据 → {}", JSON.toJSONString(sensorOriginDataList));
            // 投递消息到RabbitMQ
            rabbitMqClient.sendMessage(emPrefixProperties.getRabbitMqPrefix() + RabbitMqConstants.DIRECT_EXCHANGE, RabbitMqConstants.SENSOR_DATA_QUEUE, JSON.toJSONString(sensorOriginDataList));
            // 持久化网关数据和传感器原始数据
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            log.error("传感器前置机数据消费失败，重新放回队列，失败原因 → {}, 前置机消息 → {}", e.getMessage(), msg);
            try {
                // deliveryTag → 该消息的index，multiple → 是否批量，true:将一次性拒绝所有小于deliveryTag的消息，requeue → 被拒绝的是否重新入队列
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("传感器前置机数据重新放回队列错误，失败原因 → {}, 前置机消息 → {}", e.getMessage(), msg);
            }
        }
    }

}