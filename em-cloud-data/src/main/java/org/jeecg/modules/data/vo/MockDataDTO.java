package org.jeecg.modules.data.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 模拟数据DTO
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@Data
@AllArgsConstructor
public class MockDataDTO implements Serializable {
    private static final long serialVersionUID = 4778497411175444253L;
    /**
     * 读数
     */
    private BigDecimal reading;
    /**
     * 上一个读数
     */
    private BigDecimal lastReading;
    /**
     * 中断数据
     */
    private Integer shiftData;
    /**
     * 是否更改模拟数据缓存
     */
    private Boolean changeMockDataCache;

    public MockDataDTO() {
        this.changeMockDataCache = Boolean.TRUE;
    }

    /**
     * 构造方法
     *
     * @param reading
     * @param shiftData
     */
    public MockDataDTO(BigDecimal reading, Integer shiftData) {
        this.reading = reading;
        this.shiftData = shiftData;
        this.changeMockDataCache = Boolean.FALSE;
    }
}
