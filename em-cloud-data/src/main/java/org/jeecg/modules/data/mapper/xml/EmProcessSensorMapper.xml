<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.data.mapper.EmProcessSensorMapper">
    <resultMap id="BaseResultMap" type="org.jeecg.modules.data.entity.EmProcessSensor">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="type" jdbcType="SMALLINT" property="type"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="device_addr" jdbcType="VARCHAR" property="deviceAddr"/>
        <result column="jcd_id" jdbcType="VARCHAR" property="jcdId"/>
        <result column="latest_com_time" jdbcType="TIMESTAMP" property="latestComTime"/>
        <result column="recall_start_time" jdbcType="TIMESTAMP" property="recallStartTime"/>
        <result column="recall_end_time" jdbcType="TIMESTAMP" property="recallEndTime"/>
        <result column="data_sources" jdbcType="SMALLINT" property="dataSources"/>
        <result column="data_freq" jdbcType="SMALLINT" property="dataFreq"/>
        <result column="is_valve_open" jdbcType="BOOLEAN" property="valveOpen"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">eps.id, eps.type, eps.user_id, eps.device_addr, eps.jcd_id, eps.latest_com_time, eps.recall_start_time, eps.recall_end_time, eps.data_sources, eps.data_freq, eps.is_valve_open, eps.is_deleted, eps.create_by, eps.create_time, eps.update_by, eps.update_time</sql>

    <sql id="tName">
        em_process_sensor eps
    </sql>

    <select id="selectListNotHaveJob" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM em_process_sensor eps
             LEFT JOIN sys_quartz_job sqj
                ON eps.device_addr = sqj.parameter
                AND sqj.is_deleted = 0
                AND sqj.job_class_name = 'org.jeecg.modules.data.job.GatewayDataJob'
        WHERE
            sqj.id IS NULL
            AND eps.is_deleted = 0
    </select>

    <select id="selectlistByLatestComTime" resultMap="BaseResultMap">
        SELECT
            eps.id,
            eps.latest_com_time,
            eps.recall_start_time,
            eps.recall_end_time
        FROM
            <include refid="tName"/>
        WHERE
            eps.latest_com_time != eps.recall_end_time
            AND eps.is_deleted = 0
    </select>
    <select id="listBySensorCodes" resultMap="BaseResultMap">
        SELECT
            eps.id, 
            eps.type, 
            eps.user_id, 
            eps.device_addr, 
            eps.jcd_id, 
            eps.latest_com_time, 
            eps.recall_start_time, 
            eps.recall_end_time, 
            eps.data_sources, 
            eps.data_freq, 
            eps.is_valve_open, 
            eps.is_deleted,
            es.sys_park_id
        FROM
            em_process_sensor eps
            LEFT JOIN em_sensor es 
                ON SUBSTRING( es.`code`, 1, LOCATE( '_', es.`code` ) - 1) = eps.device_addr
        WHERE eps.is_deleted = 0
        <if test="sensorCodes != null and sensorCodes.size() != 0">
            AND es.code IN
            <foreach collection="sensorCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>
</mapper>