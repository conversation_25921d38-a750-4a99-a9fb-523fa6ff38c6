package org.jeecg.modules.data.constant;

/**
 * acrel数据constants
 *
 * <AUTHOR>
 * @date 2022-01-12
 */
public interface AcrelDataConstants {
    /**
     * acrel token
     */
    String ACREL_DATA_TOKEN = "acrel_data_token";

    /**
     * 网关code
     */
    String METER_ID = "MeterID";

    /**
     * 网关code
     */
    String METER_ID_TWO = "meterID";

    /**
     * 网关code
     */
    String STR = "str";

    /**
     * 创建时间、数据时间
     */
    String CREATE_TIME = "CreateTime";

    /**
     * 请求类型 1：get,2：post
     */
    interface RequestType {
        Integer GET = 1;
        Integer POST = 2;
    }

    /**
     * 能源类型 1：水, 2：电
     */
    interface DeviceType {
        Integer WATER = 1;
        Integer POWER = 2;
    }

    /**
     * 分合闸控制类型 1：强制合闸，2：强制分闸，3：恢复预付费
     */
    interface MeterControlType {
        Integer CLOSE = 1;
        Integer OPEN = 2;
        Integer RESTORE = 3;
    }

}
