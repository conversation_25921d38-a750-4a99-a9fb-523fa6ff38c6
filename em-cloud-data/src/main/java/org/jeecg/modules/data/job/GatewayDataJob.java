package org.jeecg.modules.data.job;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.data.entity.EmProcessSensor;
import org.jeecg.modules.data.service.EmProcessSensorService;
import org.jeecg.modules.data.service.GetDataService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 定时获取网关数据
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GatewayDataJob implements Job {
    @Lazy
    @Autowired
    private GetDataService getDataService;
    @Autowired
    private EmProcessSensorService emProcessSensorService;

    /**
     * 参数
     */
    private String parameter;

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("{} → 定时任务开始！", this.parameter);
        handleDataNew();
        log.info("{} → 定时任务结束！", this.parameter);
    }

    /* 旧的业务逻辑 */
    private void handleDataOld() {
        EmProcessSensor emProcessSensor = emProcessSensorService.getOneByDeviceAddr(this.parameter);
        if (Objects.isNull(emProcessSensor)) {
            log.info("{}, 不存在processSensor，定时任务跳过!", this.parameter);
            return;
        }
        // 获取实时数据,检索是否需要回溯数据
        getDataService.getWhenBefore(emProcessSensor, Boolean.FALSE);
    }
    
    private void handleDataNew() {
        try {
            if (Objects.isNull(parameter) || StringUtils.isBlank(parameter)) {
                log.info("参数为空，将执行所有园区的传感器上报");
                getDataService.processData(null, null, null);
            } else {
                String[] arr = parameter.split("#");
                if (arr.length > 1) {
                    getDataService.processData(arr[0], "", Integer.valueOf(arr[1]));
                } else {
                    getDataService.processData(arr[0], "", null);
                }
            }
        } catch (Exception e) {
            log.error("执行定时任务异常：{}", e.getMessage(), e);
        }
    }
}
