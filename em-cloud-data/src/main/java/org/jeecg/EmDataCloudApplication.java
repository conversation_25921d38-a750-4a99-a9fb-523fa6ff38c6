package org.jeecg;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.core.spring.EnableDynamicTp;
import org.jeecg.common.util.ConvertUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 启动类
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Slf4j
@EnableRetry
@EnableAsync
@EnableDynamicTp
@EnableFeignClients
@EnableDiscoveryClient
@SpringBootApplication
@EnableMongoRepositories
@EnableTransactionManagement
public class EmDataCloudApplication extends SpringBootServletInitializer {
    public static void main(String[] args) throws UnknownHostException {
        SpringApplication application = new SpringApplication(EmDataCloudApplication.class);
        application.setAllowBeanDefinitionOverriding(Boolean.TRUE);
        application.setAllowCircularReferences(Boolean.TRUE);
        ConfigurableApplicationContext applicationContext = application.run(args);
        Environment env = applicationContext.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = ConvertUtils.getString(env.getProperty("server.servlet.context-path"));
        log.info("\n----------------------------------------------------------\n" +
                "Application EM-Data-Cloud is running! Access URLs:\n" +
                "Local: \t\thttp://localhost:" + port + path + "/doc.html\n" +
                "External: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
                "Swagger文档: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
                "----------------------------------------------------------");
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(EmDataCloudApplication.class);
    }
}